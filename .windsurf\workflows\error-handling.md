---
description: 学习通自动化系统错误处理和重试机制规范
globs: ["**/*.py"]
alwaysApply: true
---
# 学习通自动化系统 - 错误处理和重试机制规范

## 概述
本规范定义了学习通自动化系统中统一的错误处理模式、重试策略和异常恢复机制，确保系统的稳定性和可靠性。

## 错误分类体系

### 1. 网络相关错误
- **连接超时** - requests.exceptions.Timeout
- **连接异常** - requests.exceptions.RequestException
- **HTTP状态码错误** - 4xx, 5xx响应
- **验证码触发** - 反爬虫机制激活

### 2. 业务逻辑错误
- **登录失败** - 用户名密码错误、学校ID错误
- **课程匹配失败** - 课程ID不存在或无权限
- **任务解析失败** - 页面结构变化、参数格式错误
- **数据提交失败** - 表单验证失败、权限不足

### 3. 系统资源错误
- **数据库连接失败** - 连接池耗尽、网络中断
- **内存不足** - 大量并发任务导致内存溢出
- **文件操作失败** - 日志文件写入失败、权限问题

## 统一错误处理模式

### 1. 基础错误处理器
```python
def handle_error(self, error_msg, update_status=True, error_type="general"):
    """统一处理错误，支持重试机制"""
    self.retry_count += 1
    
    # 记录错误详情
    logger.error(f"ID:{self.username}, 错误类型:{error_type}, 错误信息:{error_msg}")
    
    if self.retry_count <= self.max_retries:
        # 根据错误类型调整重试间隔
        wait_time = self._calculate_retry_interval(error_type, self.retry_count)
        logger.warning(f"ID:{self.username}, 第{self.retry_count}次重试，等待{wait_time}秒...")
        time.sleep(wait_time)
        return False  # 继续重试
    else:
        logger.error(f"ID:{self.username}, 重试{self.max_retries}次后失败")
        if update_status:
            self._update_error_status(error_msg, error_type)
        return True  # 停止重试

def _calculate_retry_interval(self, error_type, retry_count):
    """根据错误类型和重试次数计算等待时间"""
    base_intervals = {
        "network": 5,      # 网络错误基础间隔5秒
        "captcha": 10,     # 验证码错误基础间隔10秒
        "business": 3,     # 业务错误基础间隔3秒
        "database": 15,    # 数据库错误基础间隔15秒
        "general": 2       # 通用错误基础间隔2秒
    }
    base_interval = base_intervals.get(error_type, 2)
    # 指数退避策略
    return min(base_interval * retry_count, 60)  # 最大等待60秒
```

### 2. 网络请求错误处理
```python
def safe_request(self, method, url, max_retries=3, **kwargs):
    """安全的网络请求包装器"""
    for attempt in range(max_retries + 1):
        try:
            if method.upper() == "GET":
                response = self.session.get(url, timeout=30, **kwargs)
            elif method.upper() == "POST":
                response = self.session.post(url, timeout=30, **kwargs)
            
            # 检查响应状态
            if response.status_code == 200:
                return response
            elif response.status_code in [429, 503]:  # 限流或服务不可用
                if attempt < max_retries:
                    wait_time = (2 ** attempt) * 5  # 指数退避
                    logger.warning(f"服务器限流，等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    continue
            else:
                logger.error(f"HTTP错误: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            if attempt < max_retries:
                logger.warning(f"请求超时，第{attempt + 1}次重试...")
                time.sleep(5 * (attempt + 1))
                continue
            else:
                logger.error("请求超时，已达最大重试次数")
                return None
                
        except requests.exceptions.RequestException as e:
            if attempt < max_retries:
                logger.warning(f"网络异常: {str(e)}, 第{attempt + 1}次重试...")
                time.sleep(10 * (attempt + 1))
                continue
            else:
                logger.error(f"网络异常，已达最大重试次数: {str(e)}")
                return None
    
    return None
```

### 3. 数据库操作错误处理
```python
def safe_db_operation(self, operation_func, *args, **kwargs):
    """安全的数据库操作包装器"""
    max_retries = 3
    for attempt in range(max_retries + 1):
        try:
            return operation_func(*args, **kwargs)
        except pymysql.err.OperationalError as e:
            if "Lost connection" in str(e) or "MySQL server has gone away" in str(e):
                if attempt < max_retries:
                    logger.warning(f"数据库连接丢失，第{attempt + 1}次重试...")
                    time.sleep(5)
                    # 重新初始化连接池
                    self._reinit_db_pool()
                    continue
            logger.error(f"数据库操作错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"数据库操作异常: {str(e)}")
            return None
    
    logger.error("数据库操作失败，已达最大重试次数")
    return None
```

## 验证码处理专用机制

### 1. 验证码检测和处理
```python
def handle_captcha_challenge(self, response):
    """处理验证码挑战"""
    if not self._is_captcha_response(response):
        return response
    
    logger.info("检测到验证码，开始处理...")
    
    for attempt in range(self.captcha_max_retry):
        try:
            # 获取验证码图片
            captcha_img = self._get_captcha_image()
            if not captcha_img:
                continue
            
            # 识别验证码
            captcha_code = self._identify_captcha(captcha_img)
            logger.info(f"验证码识别结果: {captcha_code}")
            
            # 提交验证码
            if self._submit_captcha(captcha_code):
                logger.success("验证码验证成功")
                return self._retry_original_request()
            else:
                logger.warning(f"验证码验证失败，第{attempt + 1}次重试...")
                time.sleep(5)
                
        except Exception as e:
            logger.error(f"验证码处理异常: {str(e)}")
            time.sleep(3)
    
    logger.error("验证码处理失败，已达最大重试次数")
    return None
```

## 业务逻辑错误恢复

### 1. 登录失败恢复
```python
def recover_login_failure(self, error_msg):
    """登录失败恢复策略"""
    if "密码错误" in error_msg:
        logger.error("密码错误，无法自动恢复")
        return False
    elif "学校错误" in error_msg:
        logger.warning("学校信息错误，尝试重新获取学校ID...")
        return self._retry_school_lookup()
    elif "验证码" in error_msg:
        logger.info("登录需要验证码，启动验证码处理...")
        return self._handle_login_captcha()
    else:
        logger.warning("未知登录错误，尝试通用恢复...")
        return self._generic_login_recovery()
```

### 2. 任务执行失败恢复
```python
def recover_task_failure(self, task_type, error_msg):
    """任务执行失败恢复策略"""
    recovery_strategies = {
        "video": self._recover_video_task,
        "document": self._recover_document_task,
        "workid": self._recover_work_task,
        "assignment": self._recover_assignment_task
    }
    
    recovery_func = recovery_strategies.get(task_type, self._generic_task_recovery)
    return recovery_func(error_msg)
```

## 状态更新和通知机制

### 1. 错误状态更新
```python
def _update_error_status(self, error_msg, error_type):
    """更新错误状态到数据库"""
    # 清理错误消息，防止SQL注入
    clean_error_msg = self._sanitize_error_message(error_msg)
    
    status_mapping = {
        "network": "网络异常",
        "captcha": "验证码失败",
        "business": "业务异常",
        "database": "数据库异常",
        "general": "系统异常"
    }
    
    status = status_mapping.get(error_type, "异常")
    
    try:
        self.pool.update_order(
            f"UPDATE qingka_wangke_order SET "
            f"status = '{status}', "
            f"process = '0%', "
            f"remarks = '{clean_error_msg}' "
            f"WHERE oid = '{self.oid}'"
        )
    except Exception as e:
        logger.error(f"更新错误状态失败: {str(e)}")

def _sanitize_error_message(self, error_msg):
    """清理错误消息，防止SQL注入"""
    # 替换单引号为双引号
    clean_msg = str(error_msg).replace("'", '"')
    # 限制长度
    if len(clean_msg) > 200:
        clean_msg = clean_msg[:197] + "..."
    return clean_msg
```

## 监控和告警

### 1. 错误统计和监控
```python
class ErrorMonitor:
    def __init__(self):
        self.error_counts = {}
        self.error_rates = {}
    
    def record_error(self, error_type, username=None):
        """记录错误发生"""
        key = f"{error_type}_{username}" if username else error_type
        self.error_counts[key] = self.error_counts.get(key, 0) + 1
        
        # 检查错误率是否过高
        if self._is_error_rate_high(error_type):
            self._trigger_alert(error_type)
    
    def _is_error_rate_high(self, error_type):
        """检查错误率是否过高"""
        threshold = {
            "network": 0.3,    # 网络错误率30%
            "captcha": 0.5,    # 验证码错误率50%
            "business": 0.2,   # 业务错误率20%
            "database": 0.1    # 数据库错误率10%
        }
        return self.error_rates.get(error_type, 0) > threshold.get(error_type, 0.2)
```

## 最佳实践

### 1. 错误处理原则
- **快速失败** - 对于无法恢复的错误，立即失败并记录
- **优雅降级** - 在部分功能失败时，保持核心功能可用
- **详细日志** - 记录足够的上下文信息用于问题诊断
- **用户友好** - 向用户提供清晰的错误信息和建议

### 2. 重试策略选择
- **网络错误** - 使用指数退避，最多重试5次
- **业务错误** - 快速重试，最多重试3次
- **验证码错误** - 固定间隔重试，最多重试5次
- **数据库错误** - 立即重试，最多重试3次

### 3. 资源清理
```python
def cleanup_resources(self):
    """清理资源，确保没有资源泄露"""
    try:
        # 释放线程锁
        ThreadManager.account_unlock(self.username, self.cid)
        # 关闭会话
        if hasattr(self, 'session'):
            self.session.close()
        # 清理临时文件
        self._cleanup_temp_files()
    except Exception as e:
        logger.error(f"资源清理失败: {str(e)}")
```
