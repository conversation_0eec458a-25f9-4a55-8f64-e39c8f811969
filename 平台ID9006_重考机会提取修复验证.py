#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台ID9006重考机会提取修复验证脚本
基于日志文件 logs/app_2025-08-07_22-04-57_355684.log 的问题分析和修复
"""

import re
import sys
import os

def test_extract_exam_details_function():
    """测试extract_exam_details函数的重考机会提取"""
    print("=== 测试extract_exam_details函数 ===")
    
    try:
        # 导入函数
        sys.path.append('.')
        from 学习通跑单启动文件 import extract_exam_details
        
        # 测试用例1：有重考机会的情况
        test_html_1 = '''
        <html>
        <body>
            <h2 class="fs16 color3 textCenter result_number">本次成绩<b>76.0</b>分</h2>
            <div class="Retake">本次考试允许重考2次，已重考1次<a href="javascript:" onclick="jumpRetest()">重考</a></div>
            <div>共75题</div>
            <div>已答75题</div>
        </body>
        </html>
        '''
        
        score, exam_name, answer_status, retake_info = extract_exam_details(test_html_1)
        print(f"测试用例1 - 有重考机会:")
        print(f"  分数: {score}")
        print(f"  重考机会: {retake_info}")
        
        if "还有1次重考机会" in retake_info:
            print("✅ 重考机会提取正确")
        else:
            print(f"❌ 重考机会提取错误: {retake_info}")
        
        # 测试用例2：无重考机会的情况
        test_html_2 = '''
        <html>
        <body>
            <h2 class="fs16 color3 textCenter result_number">本次成绩<b>76.0</b>分</h2>
            <div>考试已完成，无重考机会</div>
            <div>共75题</div>
            <div>已答75题</div>
        </body>
        </html>
        '''
        
        score, exam_name, answer_status, retake_info = extract_exam_details(test_html_2)
        print(f"\n测试用例2 - 无重考机会:")
        print(f"  分数: {score}")
        print(f"  重考机会: {retake_info}")
        
        if "还有0次重考机会" in retake_info:
            print("✅ 无重考机会检测正确")
        else:
            print(f"❌ 无重考机会检测错误: {retake_info}")
        
        # 测试用例3：没有重考信息但有分数的情况（智能推断）
        test_html_3 = '''
        <html>
        <body>
            <h2 class="fs16 color3 textCenter result_number">本次成绩<b>76.0</b>分</h2>
            <div>考试已完成</div>
            <div>共75题</div>
            <div>已答75题</div>
        </body>
        </html>
        '''
        
        score, exam_name, answer_status, retake_info = extract_exam_details(test_html_3)
        print(f"\n测试用例3 - 智能推断:")
        print(f"  分数: {score}")
        print(f"  重考机会: {retake_info}")
        
        if "还有0次重考机会" in retake_info:
            print("✅ 智能推断正确")
        else:
            print(f"❌ 智能推断失败: {retake_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试extract_exam_details函数时出错: {e}")
        return False

def test_new_retake_patterns():
    """测试新增的重考信息提取模式"""
    print("\n=== 测试新增的重考信息提取模式 ===")
    
    # 测试各种重考信息格式
    test_cases = [
        ("本次考试允许重考2次，已重考1次", "还有1次重考机会"),
        ("重考 3 次", "还有3次重考机会"),
        ("可以重考 2 次", "还有2次重考机会"),
        ("重新考试 1 次", "还有1次重考机会"),
        ("再次考试 2 次", "还有2次重考机会"),
        ("无重考机会", "还有0次重考机会"),
        ("没有重考机会", "还有0次重考机会"),
        ("不可重考", "还有0次重考机会"),
        ("重考已用完", "还有0次重考机会"),
        ("重考次数已满", "还有0次重考机会"),
        ("已达到最大重考次数", "还有0次重考机会")
    ]
    
    success_count = 0
    for test_text, expected in test_cases:
        # 模拟页面内容
        test_html = f'''
        <html>
        <body>
            <h2 class="fs16 color3 textCenter result_number">本次成绩<b>76.0</b>分</h2>
            <div>{test_text}</div>
        </body>
        </html>
        '''
        
        try:
            sys.path.append('.')
            from 学习通跑单启动文件 import extract_exam_details
            
            score, exam_name, answer_status, retake_info = extract_exam_details(test_html)
            
            if expected in retake_info:
                print(f"✅ '{test_text}' -> {retake_info}")
                success_count += 1
            else:
                print(f"❌ '{test_text}' -> {retake_info} (期望: {expected})")
                
        except Exception as e:
            print(f"❌ 测试'{test_text}'时出错: {e}")
    
    total_count = len(test_cases)
    print(f"\n模式测试结果: {success_count}/{total_count} 通过")
    
    return success_count == total_count

def verify_platform_isolation():
    """验证平台隔离"""
    print("\n=== 验证平台隔离 ===")
    
    try:
        with open("学习通跑单启动文件.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查新增的平台ID9006特殊处理
        isolation_checks = [
            "平台ID9006增加详细的重考元素查找日志",
            "平台ID9006增加更多重考信息提取模式",
            "平台ID9006新增模式",
            "平台ID9006新增无重考模式",
            "平台ID9006特殊处理：如果考试已完成且分数不为0",
            "平台ID9006异常时的默认处理",
            "平台ID9006最终检查和日志记录",
            "平台ID9006增加详细的页面内容日志",
            "if retake_info == \"未知次重考机会\" and cid == 9006:"
        ]
        
        all_found = True
        for check in isolation_checks:
            if check in content:
                print(f"✅ {check}")
            else:
                print(f"❌ 未找到: {check}")
                all_found = False
        
        if all_found:
            print("✅ 所有平台隔离检查通过")
        else:
            print("❌ 部分平台隔离检查失败")
        
        return all_found
        
    except Exception as e:
        print(f"❌ 验证平台隔离时出错: {e}")
        return False

def analyze_log_problem():
    """分析日志中的问题"""
    print("\n=== 分析日志中的问题 ===")
    
    print("根据日志 logs/app_2025-08-07_22-04-57_355684.log 分析:")
    print("问题: 重考机会显示'未知次重考机会'")
    print("日志证据:")
    print("  - 2025-08-07 22:19:42.039 | INFO | 成功提取考试详情 - 分数: 76.0, 作答情况: 未知/未知, 重考机会: 未知次重考机会")
    print("  - 2025-08-07 22:19:42.040 | INFO | 考试详情 - 名称:网络创业理论与实践, 分数:76.0分, 作答情况:75/75, 重考机会:未知次重考机会, 耗时:14分钟")
    
    print("\n修复措施:")
    print("1. ✅ 增加更多重考信息提取模式")
    print("2. ✅ 添加大小写不敏感匹配")
    print("3. ✅ 增加智能推断机制（根据分数推断）")
    print("4. ✅ 添加详细的调试日志")
    print("5. ✅ 改进异常处理")
    
    print("\n预期效果:")
    print("- 如果页面包含重考信息，能正确提取")
    print("- 如果页面不包含重考信息但有分数，推断为无重考机会")
    print("- 提供详细的调试日志便于问题诊断")
    
    return True

def test_smart_inference():
    """测试智能推断机制"""
    print("\n=== 测试智能推断机制 ===")
    
    test_cases = [
        ("76.0", "还有0次重考机会", "有分数应推断为无重考机会"),
        ("82.5", "还有0次重考机会", "有分数应推断为无重考机会"),
        ("0", "未知次重考机会", "分数为0不应推断"),
        ("未知", "未知次重考机会", "分数未知不应推断")
    ]
    
    success_count = 0
    for score, expected_retake, description in test_cases:
        # 模拟没有重考信息的页面
        test_html = f'''
        <html>
        <body>
            <h2 class="fs16 color3 textCenter result_number">本次成绩<b>{score}</b>分</h2>
            <div>考试已完成</div>
        </body>
        </html>
        '''
        
        try:
            sys.path.append('.')
            from 学习通跑单启动文件 import extract_exam_details
            
            extracted_score, exam_name, answer_status, retake_info = extract_exam_details(test_html)
            
            if expected_retake in retake_info:
                print(f"✅ 分数{score}: {retake_info} ({description})")
                success_count += 1
            else:
                print(f"❌ 分数{score}: {retake_info} (期望: {expected_retake}, {description})")
                
        except Exception as e:
            print(f"❌ 测试分数{score}时出错: {e}")
    
    total_count = len(test_cases)
    print(f"\n智能推断测试结果: {success_count}/{total_count} 通过")
    
    return success_count == total_count

if __name__ == "__main__":
    print("平台ID9006重考机会提取修复验证")
    print("=" * 50)
    print("基于日志文件: logs/app_2025-08-07_22-04-57_355684.log")
    print("=" * 50)
    
    results = []
    
    # 执行所有测试
    results.append(("extract_exam_details函数测试", test_extract_exam_details_function()))
    results.append(("新增重考模式测试", test_new_retake_patterns()))
    results.append(("平台隔离验证", verify_platform_isolation()))
    results.append(("日志问题分析", analyze_log_problem()))
    results.append(("智能推断机制测试", test_smart_inference()))
    
    print("\n" + "=" * 50)
    print("验证结果总结:")
    
    success_count = 0
    for i, (test_name, result) in enumerate(results, 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i}. {test_name}: {status}")
        if result:
            success_count += 1
    
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("\n🎉 所有验证通过，重考机会提取修复成功！")
        print("\n关键修复总结:")
        print("1. ✅ 增强重考信息提取模式：支持更多格式")
        print("2. ✅ 智能推断机制：根据分数推断重考机会")
        print("3. ✅ 详细调试日志：便于问题诊断")
        print("4. ✅ 异常处理改进：确保合理默认值")
        print("5. ✅ 平台隔离：仅影响平台ID9006")
        print("\n预期效果:")
        print("- 正确识别各种格式的重考信息")
        print("- 智能推断无重考机会的情况")
        print("- 提供详细的调试信息")
        print("- 不影响其他平台的处理")
    else:
        print("⚠️ 部分验证失败，需要进一步检查")
