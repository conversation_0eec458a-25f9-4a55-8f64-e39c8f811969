2025-07-24 00:49:30,249 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='黄祥旭' sex=AccountSex.男 phone=*********** school='益阳医学高等专科学校智慧学工' stu_id=************)
2025-07-24 00:49:30,373 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='黄祥旭' sex=AccountSex.男 phone=*********** school='益阳医学高等专科学校智慧学工' stu_id=************)
2025-07-24 00:49:30,374 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 00:49:30,374 [Main] INFO -> Ver. 0.4.5
2025-07-24 00:49:30,939 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/0005b951e9e66381e2cf718f5ab3c375.jpg
2025-07-24 00:49:31,071 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 00:49:31,730 [RootAPI] INFO -> 课程列表拉取成功 共 42 个
2025-07-24 00:50:46,893 [Main] ERROR -> 
-----*程序运行异常退出*-----
Traceback (most recent call last):
  File "Y:\yuanma\CxKitty-main\main.py", line 369, in <module>
    for task_obj in ClassSelector(command, classes):
  File "Y:\yuanma\CxKitty-main\cxapi\classes.py", line 224, in __next__
    exams_lst = self.__classes.get_exam_by_index(curr_index)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Y:\yuanma\CxKitty-main\cxapi\classes.py", line 151, in get_exam_by_index
    status=ExamStatus(exam.span.text),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "W:\Python\Python312\Lib\enum.py", line 757, in __call__
    return cls.__new__(cls, value)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "W:\Python\Python312\Lib\enum.py", line 1171, in __new__
    raise ve_exc
ValueError: '待做' is not a valid ExamStatus
2025-07-24 03:30:40,808 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='黄祥旭' sex=AccountSex.男 phone=*********** school='益阳医学高等专科学校智慧学工' stu_id=************)
2025-07-24 03:30:40,808 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 03:30:40,808 [Main] INFO -> Ver. 0.4.5
2025-07-24 03:30:41,251 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/0005b951e9e66381e2cf718f5ab3c375.jpg
2025-07-24 03:30:41,287 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 03:30:41,865 [RootAPI] INFO -> 课程列表拉取成功 共 42 个
2025-07-24 03:30:51,039 [Classes] INFO -> 拉取课程考试成功 (共 1 个) [走近中华优秀传统文化(Cou.*********/Cla.*********)]
2025-07-24 03:30:51,111 [Main] ERROR -> 
-----*程序运行异常退出*-----
Traceback (most recent call last):
  File "Y:\yuanma\CxKitty-main\main.py", line 380, in <module>
    exam, export = dialog.select_exam(console, task_obj, api)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Y:\yuanma\CxKitty-main\dialog.py", line 210, in select_exam
    Styled(exam.status.name, style=status_style)
                                   ^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'status_style' where it is not associated with a value
2025-07-24 03:34:57,780 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='黄祥旭' sex=AccountSex.男 phone=*********** school='益阳医学高等专科学校智慧学工' stu_id=************)
2025-07-24 03:34:57,780 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 03:34:57,780 [Main] INFO -> Ver. 0.4.5
2025-07-24 03:34:58,214 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/0005b951e9e66381e2cf718f5ab3c375.jpg
2025-07-24 03:34:58,255 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 03:34:58,821 [RootAPI] INFO -> 课程列表拉取成功 共 42 个
2025-07-24 03:35:02,729 [Classes] INFO -> 拉取课程考试成功 (共 1 个) [走近中华优秀传统文化(Cou.*********/Cla.*********)]
2025-07-24 03:36:57,390 [Exam] ERROR -> 获取考试失败 (该考试教师已设置章节任务点未完成95%，不能参加考试) (I.7117998)
2025-07-24 03:36:57,500 [Main] ERROR -> 
-----*程序运行异常退出*-----
Traceback (most recent call last):
  File "Y:\yuanma\CxKitty-main\main.py", line 381, in <module>
    fuck_exam_worker(exam, export)
  File "Y:\yuanma\CxKitty-main\main.py", line 277, in fuck_exam_worker
    exam.get_meta()
  File "Y:\yuanma\CxKitty-main\cxapi\exam.py", line 387, in get_meta
    raise ExamEnterError(t.text)
cxapi.exception.ExamEnterError: 该考试教师已设置章节任务点未完成95%，不能参加考试
