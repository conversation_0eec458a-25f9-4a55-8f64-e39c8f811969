import requests
import json
import time
import re
import os
import sys
import urllib3
from bs4 import BeautifulSoup

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ChaoxingHomeworkManager:
    def __init__(self, username, password):
        """
        初始化超星学习通作业管理器
        :param username: 加密后的用户名
        :param password: 加密后的密码
        """
        self.username = username
        self.password = password
        self.session = requests.Session()
        # 禁用SSL验证，解决证书验证失败问题
        self.session.verify = False
        self.cookies = None
        self.base_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }

    def login(self):
        """
        登录超星学习通
        :return: 登录是否成功
        """
        url = "https://passport2.chaoxing.com/fanyalogin"

        payload = {
            "fid": "-1",
            "uname": self.username,
            "password": self.password,
            "refer": "https%3A%2F%2Fi.chaoxing.com",
            "t": "true",
            "forbidotherlogin": "0",
            "validate": "",
            "doubleFactorLogin": "0",
            "independentId": "0",
            "independentNameId": "0",
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "sec-ch-ua-platform": '"Windows"',
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "Origin": "https://passport2.chaoxing.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://passport2.chaoxing.com/login?fid=&newversion=true&refer=https%3A%2F%2Fi.chaoxing.com",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }

        response = self.session.post(url, data=payload, headers=headers)

        try:
            result = json.loads(response.text)
            if result.get("status") and result.get("status") is True:
                print("登录成功")
                self.cookies = self.session.cookies
                return True
            else:
                print(f"登录失败: {response.text}")
                return False
        except Exception as e:
            print(f"登录过程出错: {e}")
            print(f"响应内容: {response.text}")
            return False

    def get_course_list(self):
        """
        获取课程列表
        :return: 课程列表
        """
        try:
            url = "https://mooc2-ans.chaoxing.com/visit/courselistdata"

            payload = {
                "courseType": "1",  # 1表示学生课程
                "courseFolderId": "0",
                "query": "",
                "pageHeader": "",
                "single": "0",
                "superstarClass": "0",
            }

            headers = self.base_headers.copy()
            headers["Referer"] = "https://mooc2-ans.chaoxing.com/visit/interaction"
            headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"

            response = self.session.post(url, data=payload, headers=headers)

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找课程列表
            course_items = soup.select(".learnCourse")

            courses = []
            for item in course_items:
                try:
                    # 获取课程ID和班级ID
                    course_id_input = item.select_one(".courseId")
                    clazz_id_input = item.select_one(".clazzId")
                    person_id_input = item.select_one(".curPersonId")

                    if course_id_input and clazz_id_input and person_id_input:
                        course_id = course_id_input.get("value")
                        clazz_id = clazz_id_input.get("value")
                        person_id = person_id_input.get("value")

                        # 获取课程名称
                        course_name_span = item.select_one(".course-name")
                        course_name = (
                            course_name_span.text.strip()
                            if course_name_span
                            else "未知课程"
                        )

                        # 获取教师名称
                        teacher_name_p = item.select_one(".line2.color3")
                        teacher_name = (
                            teacher_name_p.text.strip()
                            if teacher_name_p
                            else "未知教师"
                        )

                        courses.append(
                            {
                                "course_name": course_name,
                                "teacher_name": teacher_name,
                                "course_id": course_id,
                                "clazz_id": clazz_id,
                                "person_id": person_id,
                            }
                        )
                except Exception as e:
                    print(f"解析课程项时出错: {e}")

            return courses
        except Exception as e:
            print(f"获取课程列表时出错: {e}")
            return []

    def get_homework_list(self, course_id, clazz_id, person_id):
        """
        获取作业列表
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: 作业列表
        """
        try:
            # 生成时间戳
            timestamp = int(time.time() * 1000)

            # 获取enc参数，从作业列表页面获取
            enc_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}"
            enc_response = self.session.get(
                enc_url, headers=self.base_headers, verify=False
            )
            enc_soup = BeautifulSoup(enc_response.text, "html.parser")
            enc_input = enc_soup.select_one("#enc")
            enc = (
                enc_input.get("value")
                if enc_input
                else "550114ba5f5bf0c61d3f650fe04350da"
            )  # 默认值

            # 直接构建作业列表URL，使用正确的路径和参数
            url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&enc={enc}&status=0&pageNum=1&topicId=0"

            headers = self.base_headers.copy()

            # 禁用SSL证书验证
            response = self.session.get(url, headers=headers, verify=False)

            # 直接在终端输出响应状态码和内容长度
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容长度: {len(response.text)}")
            print(f"作业列表URL: {url}")

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 检查是否有错误提示
            error_msg = soup.select_one(".word_null p")
            if error_msg:
                print(f"页面错误提示: {error_msg.text.strip()}")

            # 获取作业列表项，使用正确的选择器
            homework_items = soup.select(".bottomList li")

            # 直接输出找到的作业项数量
            print(f"找到作业项数量: {len(homework_items)}")

            homeworks = []
            for item in homework_items:
                try:
                    # 获取作业标题
                    title_elem = item.select_one(".overHidden2")
                    title = title_elem.text.strip() if title_elem else "未知作业"

                    # 获取作业状态
                    status_elem = item.select_one(".status")
                    status = status_elem.text.strip() if status_elem else "未知状态"

                    # 获取作业链接，从data属性获取
                    link = item.get("data") or ""

                    # 获取作业ID和答案ID
                    work_id = None
                    answer_id = None
                    if link:
                        work_id_match = re.search(r"workId=(\d+)", link)
                        if work_id_match:
                            work_id = work_id_match.group(1)

                        answer_id_match = re.search(r"answerId=(\d+)", link)
                        if answer_id_match:
                            answer_id = answer_id_match.group(1)

                    homework_info = {
                        "title": title,
                        "status": status,
                        "link": link,
                        "work_id": work_id,
                        "answer_id": answer_id,
                        "course_id": course_id,
                        "clazz_id": clazz_id,
                        "cpi": person_id,
                    }

                    # 直接在终端输出每个作业的信息
                    print(f"作业: {title} - 状态: {status}")
                    print(f"  链接: {link}")
                    print(f"  作业ID: {work_id}, 答案ID: {answer_id}")
                    print("-" * 50)

                    homeworks.append(homework_info)
                except Exception as e:
                    print(f"解析作业项时出错: {e}")

            return homeworks
        except Exception as e:
            print(f"获取作业列表时出错: {e}")
            return []

    def get_homework_detail(self, homework):
        """
        获取作业详情
        :param homework: 作业信息
        :return: 作业详情
        """
        try:
            url = homework["link"]

            headers = self.base_headers.copy()
            headers["Referer"] = (
                f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={homework['course_id']}&classId={homework['clazz_id']}&cpi={homework['cpi']}"
            )

            response = self.session.get(url, headers=headers)

            # 保存作业详情页面，以便分析
            filename = f"homework_detail_{homework['work_id']}.html"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(response.text)

            print(f"已保存作业详情页面到 {filename}")

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 获取作业标题
            title = (
                soup.select_one(".mark_title").text.strip()
                if soup.select_one(".mark_title")
                else homework["title"]
            )

            # 获取作业题目
            questions = []
            question_items = soup.select(".questionLi")

            for i, item in enumerate(question_items, 1):
                try:
                    # 获取题目类型
                    type_span = item.select_one(".colorShallow")
                    question_type = (
                        type_span.text.strip().replace("(", "").replace(")", "")
                        if type_span
                        else "未知类型"
                    )

                    # 获取题目内容
                    content_elem = item.select_one(".mark_name")
                    content = content_elem.text.strip() if content_elem else f"题目 {i}"

                    # 获取题目ID
                    question_id = item.get("data") or ""

                    questions.append(
                        {
                            "question_id": question_id,
                            "question_type": question_type,
                            "content": content,
                            "index": i,
                        }
                    )
                except Exception as e:
                    print(f"解析题目时出错: {e}")

            return {"title": title, "questions": questions, "html": response.text}
        except Exception as e:
            print(f"获取作业详情时出错: {e}")
            return None

    def submit_homework_answer(self, homework, answers):
        """
        提交作业答案
        :param homework: 作业信息
        :param answers: 答案信息，格式为 {question_id: answer}
        :return: 提交结果
        """
        try:
            # 构建提交URL
            url = f"https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"

            # 构建提交数据
            payload = {
                "courseId": homework["course_id"],
                "classId": homework["clazz_id"],
                "knowledgeid": "0",
                "cpi": homework["cpi"],
                "workRelationId": homework["work_id"],
                "workAnswerId": homework["answer_id"],
                "jobid": "",
                "enc_work": "",
                "totalQuestionNum": "",
                "pyFlag": "",
                "answerwqbid": "",
                "mooc2": "1",
                "randomOptions": "false",
            }

            # 添加每个题目的答案
            for question_id, answer in answers.items():
                payload[f"answer{question_id}"] = answer
                payload[f"answertype{question_id}"] = "4"  # 假设所有题目都是简答题

            headers = self.base_headers.copy()
            headers["Referer"] = homework["link"]
            headers["Content-Type"] = "application/x-www-form-urlencoded"

            response = self.session.post(url, data=payload, headers=headers)

            # 检查提交结果
            if "提交成功" in response.text:
                return {"success": True, "message": "作业提交成功"}
            else:
                return {"success": False, "message": f"作业提交失败: {response.text}"}
        except Exception as e:
            print(f"提交作业答案时出错: {e}")
            return {"success": False, "message": f"提交作业答案时出错: {str(e)}"}


def show_menu():
    """
    显示主菜单
    """
    print("\n===== 超星学习通作业管理系统 =====")
    print("1. 获取课程列表")
    print("2. 获取作业列表")
    print("3. 查看作业详情")
    print("4. 自动完成作业")
    print("5. 退出系统")
    print("================================")
    choice = input("请选择操作: ")
    return choice


def main():
    # 使用denglu.py中的加密用户名和密码
    username = "D2bCfRqh3U9bDhfsvVUneg=="
    password = "/H+E5YEkVpxVz37gYPB0xg=="

    manager = ChaoxingHomeworkManager(username, password)

    # 先登录
    login_success = manager.login()

    if login_success:
        # 登录成功后等待一下，确保登录状态生效
        print("等待2秒，确保登录状态生效...")
        time.sleep(2)

        # 获取课程列表
        print("\n获取课程列表...")
        courses = manager.get_course_list()

        if courses:
            print(f"找到 {len(courses)} 门课程:")
            for i, course in enumerate(courses, 1):
                print(f"{i}. {course['course_name']} - {course['teacher_name']}")

            # 让用户选择一门课程
            selected_index = 0
            try:
                selected_index = int(input("\n请选择要查看作业的课程编号: ")) - 1
                if selected_index < 0 or selected_index >= len(courses):
                    print("无效的课程编号，默认选择第一门课程")
                    selected_index = 0
            except:
                print("输入无效，默认选择第一门课程")
                selected_index = 0

            selected_course = courses[selected_index]
            print(f"\n已选择课程: {selected_course['course_name']}")

            # 获取作业列表
            print("\n获取作业列表...")
            homeworks = manager.get_homework_list(
                selected_course["course_id"],
                selected_course["clazz_id"],
                selected_course["person_id"],
            )

            if homeworks:
                print(f"找到 {len(homeworks)} 个作业:")
                for i, homework in enumerate(homeworks, 1):
                    print(f"{i}. {homework['title']} - 状态: {homework['status']}")
                    if homework["work_id"]:
                        print(f"   作业ID: {homework['work_id']}")
                    if homework["link"]:
                        print(f"   作业链接: {homework['link']}")

                # 让用户选择一个作业
                homework_index = 0
                try:
                    homework_index = (
                        int(input("\n请选择要查看的作业编号(0表示退出): ")) - 1
                    )
                    if homework_index < 0:
                        print("已退出")
                        return
                    if homework_index >= len(homeworks):
                        print("无效的作业编号，默认选择第一个作业")
                        homework_index = 0
                except:
                    print("输入无效，默认选择第一个作业")
                    homework_index = 0

                selected_homework = homeworks[homework_index]
                print(f"\n已选择作业: {selected_homework['title']}")

                # 获取作业详情
                print("\n获取作业详情...")
                homework_detail = manager.get_homework_detail(selected_homework["link"])

                if homework_detail:
                    print(f"作业标题: {homework_detail['title']}")
                    print(f"题目数量: {len(homework_detail['questions'])}")

                    # 显示题目
                    for i, question in enumerate(homework_detail["questions"], 1):
                        print(f"\n题目 {i}: {question['title']}")
                        print(f"类型: {question['type']}")
                        if question.get("options"):
                            print("选项:")
                            for j, option in enumerate(question["options"], 1):
                                print(f"  {j}. {option}")

                    # 询问是否提交作业
                    submit = input("\n是否提交作业?(y/n): ").lower()
                    if submit == "y":
                        # 生成答案
                        answers = manager.generate_answers(homework_detail["questions"])

                        # 提交作业
                        print("\n提交作业...")
                        submit_result = manager.submit_homework(
                            selected_homework["course_id"],
                            selected_homework["clazz_id"],
                            selected_homework["cpi"],
                            selected_homework["work_id"],
                            selected_homework["answer_id"],
                            answers,
                        )

                        if submit_result:
                            print("作业提交成功!")
                        else:
                            print("作业提交失败!")
                else:
                    print("获取作业详情失败")
            else:
                print("未找到作业")
        else:
            print("未找到课程")
    else:
        print("登录失败，无法继续操作")


if __name__ == "__main__":
    main()
