# 学习通自动化系统功能实现指南

本文档详细分析了学习通自动化系统的考试自动答题功能及相关特性，包括验证码识别和人脸识别功能，并提供了实现这些功能的技术指南。

## 目录

1. [考试自动答题功能](#考试自动答题功能)
   - [支持题型](#支持题型)
   - [跳过考试承诺书](#跳过考试承诺书)
   - [跳过重考须知](#跳过重考须知)
   - [过滤空白Unicode字符](#过滤空白unicode字符)
   - [答题流程](#答题流程)
2. [验证码识别功能](#验证码识别功能)
   - [OpenCV预处理](#opencv预处理)
   - [ddddocr识别](#ddddocr识别)
3. [人脸识别功能](#人脸识别功能)
   - [人脸图片上传](#人脸图片上传)
   - [人脸识别提交](#人脸识别提交)
4. [实现步骤](#实现步骤)
   - [依赖安装](#依赖安装)
   - [代码实现](#代码实现)
   - [配置说明](#配置说明)

## 考试自动答题功能

### 支持题型

学习通自动化系统支持多种题型的自动答题，主要包括：

- **单选题**：通过匹配选项内容与搜索结果，自动选择正确答案
- **多选题**：支持多个选项的匹配和选择
- **填空题**：自动填写答案，支持多个空的情况
- **判断题**：通过关键词匹配（如"对/错"、"是/否"、"正确/错误"）自动判断

代码实现核心逻辑位于`QuestionResolver`类的`fill`方法中：

```python
def fill(self, question: QuestionModel, search_results: list[SearcherResp]) -> bool:
    "查询并填充对应选项"
    # 遍历多个搜索器返回以适配结果
    for result in search_results:
        if result.code != 0 or result.answer is None:
            continue
        search_answer = result.answer.strip()
        match question.type:
            case QuestionType.单选题:
                for k, v in question.options.items():
                    if difflib.SequenceMatcher(a=v, b=search_answer).ratio() >= 0.95:
                        question.answer = k
                        return True
            case QuestionType.判断题:
                if re.search(r"(错|否|错误|false|×)", search_answer):
                    question.answer = False
                    return True
                elif re.search(r"(对|是|正确|true|√)", search_answer):
                    question.answer = True
                    return True
            case QuestionType.多选题:
                option_lst = []
                if len(part_answer_lst := search_answer.split("#")) <= 1:
                    part_answer_lst = search_answer.split(";")
                for part_answer in part_answer_lst:
                    for k, v in question.options.items():
                        if v == part_answer:
                            option_lst.append(k)
                # 多选题选项必须排序，否则提交错误
                option_lst.sort()
                if len(option_lst):
                    question.answer = "".join(option_lst)
                    return True
            case QuestionType.填空题:
                blanks_answer = search_answer.split("#")
                if blanks_answer:
                    question.answer = blanks_answer
                    return True
```

### 跳过考试承诺书

系统可以自动跳过考试前的承诺书页面，通过在请求参数中添加特定标志实现：

```python
# 在ExamDto类的get_meta方法中，通过examsignal参数跳过承诺书
resp = self.session.get(
    PAGE_EXAM_COVER,
    params={
        # ...其他参数
        "examsignal": 1,  # 强制跳过考试承诺书
    },
    allow_redirects=False,
)
```

### 跳过重考须知

系统通过在请求参数中添加`redo`参数，可以跳过重考须知页面：

```python
# 在ExamDto类的get_meta方法中
resp = self.session.get(
    PAGE_EXAM_COVER,
    params={
        "redo": 1,  # 强制跳过重新作答重定向
        # ...其他参数
    },
    allow_redirects=False,
)
```

### 过滤空白Unicode字符

系统会自动过滤题干和选项中的空白Unicode字符，如u+2002、u+200b、u+3000等，这些字符可能会影响答案匹配。实现方法是在处理题目文本时使用正则表达式或字符串替换：

```python
# 在utils.py中实现的remove_escape_chars函数
def remove_escape_chars(text: str) -> str:
    """移除转义字符和不可见字符
    Args:
        text: 原始文本
    Returns:
        str: 处理后的文本
    """
    # 移除HTML转义字符
    text = html.unescape(text)
    # 移除Unicode空白字符
    text = re.sub(r'[\u2002\u200b\u3000\xa0\u0020]', '', text)
    # 移除多余的空格和换行
    text = re.sub(r'\s+', ' ', text)
    return text.strip()
```

这个函数在解析题目和选项时被调用，确保文本中的特殊空白字符不会影响答案匹配。

### 答题流程

完整的考试自动答题流程如下：

1. **初始化考试**：获取考试元数据，处理人脸识别（如需）
2. **开始考试**：调用开始考试接口
3. **获取题目**：逐题获取或一次性获取全部题目
4. **搜索答案**：使用配置的搜索器查找答案
5. **填充答案**：根据题型匹配并填充答案
6. **提交答案**：单题提交或批量提交
7. **结束考试**：根据配置决定是否自动交卷

核心实现在`QuestionResolver`类的`execute`方法中：

```python
def execute(self) -> None:
    # 初始化UI组件
    # ...
    
    # 迭代答题接口, 遍历所有题目
    for index, question in self.exam_dto:
        # 调用搜索器
        results = self.searcher.invoke(question)
        
        # 填充选项
        status = self.fill(question, results)
        
        # 单题提交
        time.sleep(self.persubmit_delay)  # 提交延迟
        try:
            result = self.exam_dto.submit(index=index, question=question)
        except APIError as e:
            # 处理提交失败
            # ...
        else:
            # 处理提交成功
            # ...
    
    # 答题完毕处理
    self.finish_flag = True
    
    # 没有错误且配置了自动交卷
    if self.incompleted_cnt == 0 and self.auto_final_submit:
        # 可选的交卷确认
        if self.cb_confirm_submit is not None:
            if not self.cb_confirm_submit(...):
                return
        
        # 提交试题
        try:
            result = self.exam_dto.final_submit()
        except APIError as e:
            # 处理交卷失败
            # ...
        else:
            # 处理交卷成功
            # ...
```

## 验证码识别功能

系统使用OpenCV进行验证码图像预处理，然后使用ddddocr进行识别。

### OpenCV预处理

验证码识别前，使用OpenCV进行以下预处理步骤：

1. 灰度化
2. 二值化
3. 反色处理
4. 膨胀处理

```python
def identify_captcha(captcha_img: bytes) -> str:
    """验证码识别实现
    Args:
        captcha_img: 验证码图片数据 png 格式
    Returns:
        str: 识别的验证码文本
    """
    img = np.frombuffer(captcha_img, np.uint8)
    img = cv2.imdecode(img, cv2.IMREAD_GRAYSCALE)

    # 二值化
    _, img = cv2.threshold(img, 190, 255, cv2.THRESH_BINARY)

    # 反色
    img = cv2.bitwise_not(img)

    # 膨胀
    kernal = np.ones([3, 2], np.uint8)
    img = cv2.dilate(img, kernal, iterations=1)

    # 交给 OCR 识别
    _, img_data = cv2.imencode(".png", img)
    code = ocr.classification(img_data.tostring())
    return code
```

### ddddocr识别

使用ddddocr库进行验证码识别：

```python
from ddddocr import DdddOcr

ocr = DdddOcr(show_ad=False)
# ...
code = ocr.classification(img_data.tostring())
```

验证码识别流程集成在`SessionWraper`类的请求处理中，当检测到验证码页面时自动触发：

```python
def __handle_anti_spider(self) -> None:
    """处理风控验证码"""
    self.logger.info("开始处理验证码")
    for retry_times in range(self.__captcha_max_retry):
        self.logger.info(f"验证码处理第 {retry_times + 1} 次")
        self.__cb_resolve_captcha_after(retry_times + 1)

        # 这里过快可能导致 capthca 拉取识别，故延时
        time.sleep(5.0)
        captcha_img = self.__get_captcha_image()
        if captcha_img is None:
            continue
        captcha_code = identify_captcha(captcha_img)
        self.logger.info(f"验证码已识别 {captcha_code}")
        status = self.__submit_captcha(captcha_code)

        self.__cb_resolve_captcha_before(status, captcha_code)
        if status is True:
            # 识别成功退出 retry
            break
        # 失败后进行 retry
        time.sleep(5.0)
    else:
        # retry 超限
        raise HandleCaptchaError
```

## 人脸识别功能

系统支持自动处理学习通的人脸识别验证，包括上传人脸图片和提交人脸识别。

### 人脸图片上传

人脸图片上传流程：

1. 获取云盘上传token
2. 对人脸图片进行随机LSB像素干扰（破解哈希值检测）
3. 上传图片到学习通云盘
4. 获取返回的objectId

```python
def upload_face_img(self, face_img: str | PathLike) -> str:
    """上传人脸照片
    Args:
        face_img: 待上传的人脸图片路径
    Returns:
        str: 上传后的对象 objectId
    """
    # 随机 LSB 像素干扰，破坏 hash 风控
    face_img = cv2.imread(str(face_img))
    img_h, img_w, _ = face_img.shape
    rng = np.random.default_rng()
    for _ in range(rng.integers(0, 5)):
        face_img[
            rng.integers(0, img_h - 1),
            rng.integers(0, img_w - 1),
            rng.integers(0, 2),
        ] += rng.integers(-2, 2)
    _, face_img_data = cv2.imencode(".jpg", face_img)

    resp = self.session.post(
        API_UPLOAD_FACE,
        params={
            "uploadtype": "face",
            "_token": self.upload_token,
            "puid": self.session.acc.puid,
        },
        files={
            "file": (f"{get_ts()}.jpg", face_img_data, "image/jpeg"),
        },
    )
    json_content = resp.json()
    if json_content.get("result") is not True:
        self.logger.error("人脸上传失败")
        raise APIError
    object_id = json_content["objectId"]
    return object_id
```

### 人脸识别提交

系统支持三种人脸识别提交方式：

1. **普通任务点人脸识别**（旧版API）
2. **普通任务点人脸识别**（新版API）
3. **考试人脸识别**（专用API）

考试人脸识别提交实现：

```python
def submit_face_exam(
    self,
    exam_id: int,
    course_id: int,
    class_id: int,
    cpi: int,
    object_id: str,
):
    """提交考试人脸识别"""
    resp = self.session.get(
        API_FACE_SUBMIT_EXAM,
        params={
            "relationid": exam_id,
            "courseId": course_id,
            "classId": class_id,
            "currentFaceId": object_id,
            "liveDetectionStatus": 1,
            "cpi": cpi,
        },
    )
    resp.raise_for_status()
    json_content = resp.json()
    if json_content.get("status") is not True:
        message = json_content.get("msg")
        self.logger.error(f"人脸识别提交失败 {message}")
        raise APIError
    result = json_content["data"]
    return result
```

在考试流程中，`ExamDto`类的`resolve_face_detection`方法会自动处理人脸识别：

```python
def resolve_face_detection(self):
    """解决人脸识别"""
    self.session.face_detection.get_upload_token()

    # 上传人脸图片
    object_id, _ = self.session.face_detection.upload_face_by_puid()
    object_id2, _ = self.session.face_detection.upload_face_by_puid()

    # 提交并比对人脸
    submit_result = self.session.face_detection.submit_face_exam(
        exam_id=self.exam_id,
        course_id=self.course_id,
        class_id=self.class_id,
        cpi=self.cpi,
        object_id=object_id,
    )

    # 生成人脸识别提交 Token 与 result 数据
    self.face_key = submit_result["facekey"]
    self.face_detection_result = {
        "collectedFaceId": submit_result["detail"]["collectObjectId"],
        "currentFaceId": submit_result["detail"]["faceObjectId"],
        "collectStatus": 1,
        "LiveDetectionStatus": 1,
        "extraData": {
            "a_eye": random.choices((-1, 0), (0.2, 0.8)),
            "a_score": 0,
            "f_extra": f"{random.randint(5000, 10000)}_0_-1_1_0",
            "ret": random.randint(100, 105),
            "s_objectId": object_id2,
            "s_score": random.randint(80000000, 99000000) / 1e8,
        },
        "ignoreLiveDetectionStatus": 1,
    }
```

## 实现步骤

### 依赖安装

实现上述功能需要安装以下依赖：

```bash
pip install opencv-python numpy ddddocr requests bs4 rich
```

### 代码实现

1. **创建验证码识别模块**

```python
# captcha_handler.py
import cv2
import numpy as np
from ddddocr import DdddOcr

ocr = DdddOcr(show_ad=False)

def identify_captcha(captcha_img: bytes) -> str:
    """验证码识别实现"""
    img = np.frombuffer(captcha_img, np.uint8)
    img = cv2.imdecode(img, cv2.IMREAD_GRAYSCALE)
    
    # 二值化
    _, img = cv2.threshold(img, 190, 255, cv2.THRESH_BINARY)
    
    # 反色
    img = cv2.bitwise_not(img)
    
    # 膨胀
    kernal = np.ones([3, 2], np.uint8)
    img = cv2.dilate(img, kernal, iterations=1)
    
    # 交给 OCR 识别
    _, img_data = cv2.imencode(".png", img)
    code = ocr.classification(img_data.tobytes())
    return code
```

2. **创建人脸识别模块**

```python
# face_handler.py
import cv2
import numpy as np
import random
import time
import requests
from pathlib import Path

class FaceHandler:
    def __init__(self, session, logger):
        self.session = session
        self.logger = logger
        self.upload_token = None
        
    def get_upload_token(self):
        """获取云盘上传token"""
        resp = self.session.get("https://pan-yz.chaoxing.com/api/token/uservalid")
        json_content = resp.json()
        if json_content.get("result") is not True:
            raise Exception("获取上传token失败")
        self.upload_token = json_content["_token"]
        return self.upload_token
        
    def upload_face_img(self, face_img_path):
        """上传人脸图片"""
        # 随机LSB像素干扰
        face_img = cv2.imread(str(face_img_path))
        img_h, img_w, _ = face_img.shape
        rng = np.random.default_rng()
        for _ in range(rng.integers(0, 5)):
            face_img[
                rng.integers(0, img_h - 1),
                rng.integers(0, img_w - 1),
                rng.integers(0, 2),
            ] += rng.integers(-2, 2)
        _, face_img_data = cv2.imencode(".jpg", face_img)
        
        # 上传图片
        resp = self.session.post(
            "https://pan-yz.chaoxing.com/upload",
            params={
                "uploadtype": "face",
                "_token": self.upload_token,
                "puid": self.session.user_id,
            },
            files={
                "file": (f"{int(time.time() * 1000)}.jpg", face_img_data, "image/jpeg"),
            },
        )
        json_content = resp.json()
        if json_content.get("result") is not True:
            raise Exception("人脸上传失败")
        return json_content["objectId"]
        
    def submit_face_exam(self, exam_id, course_id, class_id, cpi, object_id):
        """提交考试人脸识别"""
        resp = self.session.get(
            "https://mooc1-api.chaoxing.com/exam-ans/exam/phone/face-compare",
            params={
                "relationid": exam_id,
                "courseId": course_id,
                "classId": class_id,
                "currentFaceId": object_id,
                "liveDetectionStatus": 1,
                "cpi": cpi,
            },
        )
        json_content = resp.json()
        if json_content.get("status") is not True:
            raise Exception(f"人脸识别提交失败: {json_content.get('msg')}")
        return json_content["data"]
```

3. **创建文本处理工具**

```python
# text_utils.py
import re
import html

def remove_escape_chars(text: str) -> str:
    """移除转义字符和不可见字符"""
    # 移除HTML转义字符
    text = html.unescape(text)
    # 移除Unicode空白字符
    text = re.sub(r'[\u2002\u200b\u3000\xa0\u0020]', '', text)
    # 移除多余的空格和换行
    text = re.sub(r'\s+', ' ', text)
    return text.strip()
```

4. **创建考试处理模块**

```python
# exam_handler.py
import time
import random
import json
from enum import Enum

class QuestionType(Enum):
    单选题 = 0
    多选题 = 1
    填空题 = 2
    判断题 = 3
    # 其他题型...

class ExamHandler:
    def __init__(self, session, logger, face_handler=None):
        self.session = session
        self.logger = logger
        self.face_handler = face_handler
        
    def start_exam(self, exam_id, course_id, class_id, cpi, need_face=False):
        """开始考试"""
        # 处理人脸识别
        face_key = None
        face_detection_result = None
        
        if need_face and self.face_handler:
            # 获取上传token
            self.face_handler.get_upload_token()
            
            # 上传人脸图片
            object_id = self.face_handler.upload_face_img("face.jpg")
            object_id2 = self.face_handler.upload_face_img("face.jpg")
            
            # 提交人脸识别
            submit_result = self.face_handler.submit_face_exam(
                exam_id=exam_id,
                course_id=course_id,
                class_id=class_id,
                cpi=cpi,
                object_id=object_id,
            )
            
            # 生成人脸识别数据
            face_key = submit_result["facekey"]
            face_detection_result = {
                "collectedFaceId": submit_result["detail"]["collectObjectId"],
                "currentFaceId": submit_result["detail"]["faceObjectId"],
                "collectStatus": 1,
                "LiveDetectionStatus": 1,
                "extraData": {
                    "a_eye": random.choices((-1, 0), (0.2, 0.8)),
                    "a_score": 0,
                    "f_extra": f"{random.randint(5000, 10000)}_0_-1_1_0",
                    "ret": random.randint(100, 105),
                    "s_objectId": object_id2,
                    "s_score": random.randint(80000000, 99000000) / 1e8,
                },
                "ignoreLiveDetectionStatus": 1,
            }
        
        # 开始考试请求
        resp = self.session.get(
            "https://mooc1-api.chaoxing.com/exam-ans/exam/phone/start",
            params={
                "courseId": course_id,
                "classId": class_id,
                "examId": exam_id,
                "source": 0,
                "examAnswerId": self.exam_answer_id,  # 需要先获取
                "cpi": cpi,
                "keyboardDisplayRequiresUserAction": 1,
                "imei": self._get_imei(),  # 需要实现
                "faceDetection": int(need_face),
                "facekey": face_key or "",
                "faceDetectionResult": (
                    json.dumps(face_detection_result, separators=(",", ":"))
                    if need_face and face_detection_result else ""
                ),
                "jt": 0,
                "code": "",  # 考试码，如需要
            },
        )
        return resp.json()
    
    def answer_question(self, question_type, question_content, options, search_result):
        """根据题型和搜索结果填充答案"""
        import difflib
        import re
        
        search_answer = search_result.strip()
        
        if question_type == QuestionType.单选题:
            for k, v in options.items():
                if difflib.SequenceMatcher(a=v, b=search_answer).ratio() >= 0.95:
                    return k
                    
        elif question_type == QuestionType.判断题:
            if re.search(r"(错|否|错误|false|×)", search_answer):
                return False
            elif re.search(r"(对|是|正确|true|√)", search_answer):
                return True
                
        elif question_type == QuestionType.多选题:
            option_lst = []
            if len(part_answer_lst := search_answer.split("#")) <= 1:
                part_answer_lst = search_answer.split(";")
            for part_answer in part_answer_lst:
                for k, v in options.items():
                    if v == part_answer:
                        option_lst.append(k)
            # 多选题选项必须排序
            option_lst.sort()
            if len(option_lst):
                return "".join(option_lst)
                
        elif question_type == QuestionType.填空题:
            blanks_answer = search_answer.split("#")
            if blanks_answer:
                return blanks_answer
                
        return None
```

### 配置说明

在实现过程中，需要配置以下内容：

1. **验证码识别配置**
   - 最大重试次数
   - 识别阈值参数

2. **人脸识别配置**
   - 人脸图片路径
   - 是否启用人脸识别

3. **考试配置**
   - 是否自动交卷
   - 答题间隔时间
   - 是否启用模糊匹配

示例配置文件（YAML格式）：

```yaml
# 验证码配置
captcha:
  max_retry: 6
  threshold: 190

# 人脸识别配置
face:
  enable: true
  image_path: "faces/"
  
# 考试配置
exam:
  enable: true
  auto_submit: false
  persubmit_delay: 1.0
  fallback_fuzzer: true
  confirm_submit: true
```

通过以上实现步骤和配置，可以在其他学习通自动化系统中添加考试自动答题、验证码识别和人脸识别功能。 