---
description: 学习通自动化系统Python模块导入顺序规范
globs: ["**/*.py"]
alwaysApply: true
---
# 学习通自动化系统 - Python导入顺序规范

## 规则概述

本规范定义了学习通自动化系统中Python模块的导入顺序和组织方式，确保代码的一致性和可维护性。

## 导入分组顺序

导入语句应按以下顺序分组，每组之间用空行分隔：

1. **标准库导入** - Python内置模块
2. **第三方库导入** - 外部依赖包
3. **项目内部导入** - 本项目的模块
4. **条件导入** - 使用try-except的可选导入

### 标准导入示例

```python
# -*- coding: utf-8 -*-
# 标准库导入 (按字母顺序排列)
import datetime
import json
import queue
import random
import re
import signal
import string
import sys
import threading
import time
import traceback
from base64 import b64encode
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlparse, parse_qsl, urljoin

# 第三方库导入 (按字母顺序排列)
import certifi
import parsel
import pymysql
import requests
import urllib3
from bs4 import BeautifulSoup
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from dbutils.pooled_db import PooledDB
from ddddocr import DdddOcr
from loguru import logger
from yarl import URL

# 项目内部导入 (按模块层次排列)
from Config.UserSql import OrderProcessorsql
from data.Exam import EXAM
from data.Porgres import StartProces
from API.Session import StartSession
from API.TaskDo import Task, AssignmentTask
from API.VideoTask import VideoStart
from API.WorkTask import StaratWorkTaks
from API.Questionbank import questionbank, questionbank2, fujia

# 条件导入 (可选模块)
try:
    from API.HomeworkAI import HomeworkAI
    HOMEWORK_AI_AVAILABLE = True
except ImportError:
    HOMEWORK_AI_AVAILABLE = False
    logger.warning("HomeworkAI模块未找到，AI自动填充功能不可用")
```

## Rule: Import Style

1. Use absolute imports rather than relative imports
2. Avoid wildcard imports (`from module import *`)
3. Use aliases for imports when it helps readability

### Example:

```python
# Good
import numpy as np
from matplotlib import pyplot as plt

# Avoid
from numpy import *
```

## Rule: Import Organization

1. Alphabetize imports within each group
2. For `from` imports, place them after regular imports in their respective groups

### Example:

```python
# Standard library
import os
import sys
from collections import defaultdict
from datetime import datetime

# Third party
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
```

## 学习通系统特定规则

### 1. 编码声明
所有Python文件必须在第一行包含编码声明：
```python
# -*- coding: utf-8 -*-
```

### 2. 条件导入模式
对于可选的模块（如AI模块），使用标准的条件导入模式：
```python
try:
    from API.HomeworkAI import HomeworkAI
    HOMEWORK_AI_AVAILABLE = True
except ImportError:
    HOMEWORK_AI_AVAILABLE = False
    logger.warning("HomeworkAI模块未找到，AI自动填充功能不可用")
```

### 3. 项目内部导入层次
项目内部导入应按以下层次顺序：
1. Config层 - 配置和数据库
2. data层 - 数据处理和状态管理
3. API层 - 业务逻辑模块
4. 工具函数和辅助模块

### 4. 常用导入模板
对于新的API模块，推荐使用以下导入模板：
```python
# -*- coding: utf-8 -*-
import re
import time
import traceback
from bs4 import BeautifulSoup
from loguru import logger
import requests

# 项目内部导入
from API.Session import StartSession
```

### 5. 避免循环导入
- 不要在模块级别进行相互导入
- 如需相互引用，考虑在函数内部导入
- 使用依赖注入模式传递对象引用

## 违规示例和修正

### 违规示例：
```python
# 错误：混乱的导入顺序
from API.Session import StartSession
import requests
import sys
from loguru import logger
import time
```

### 修正示例：
```python
# -*- coding: utf-8 -*-
# 正确：按规范分组排序
import sys
import time

import requests
from loguru import logger

from API.Session import StartSession
```

