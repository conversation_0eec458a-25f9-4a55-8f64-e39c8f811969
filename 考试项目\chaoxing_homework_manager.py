import requests
import json
import time
import re
import os
import sys
import urllib3
from bs4 import BeautifulSoup
import random

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ChaoxingHomeworkManager:
    def __init__(self, username, password):
        """
        初始化超星学习通作业管理器
        :param username: 加密后的用户名
        :param password: 加密后的密码
        """
        self.username = username
        self.password = password
        self.session = requests.Session()
        # 禁用SSL验证，解决证书验证失败问题
        self.session.verify = False
        self.cookies = None
        self.base_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }
        # 初始化course_enc属性为None
        self.course_enc = None
        # 初始化stuenc属性为None
        self.stuenc = None
        # 过滤选项
        self.include_finished = False
        self.include_ended_courses = False

    def login(self):
        """
        登录超星学习通
        :return: 登录是否成功
        """
        url = "https://passport2.chaoxing.com/fanyalogin"

        payload = {
            "fid": "-1",
            "uname": self.username,
            "password": self.password,
            "refer": "https%3A%2F%2Fi.chaoxing.com",
            "t": "true",
            "forbidotherlogin": "0",
            "validate": "",
            "doubleFactorLogin": "0",
            "independentId": "0",
            "independentNameId": "0",
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "sec-ch-ua-platform": '"Windows"',
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "Origin": "https://passport2.chaoxing.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://passport2.chaoxing.com/login?fid=&newversion=true&refer=https%3A%2F%2Fi.chaoxing.com",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        }

        response = self.session.post(url, data=payload, headers=headers)

        try:
            result = json.loads(response.text)
            if result.get("status") and result.get("status") is True:
                print("登录成功")
                self.cookies = self.session.cookies
                return True
            else:
                print(f"登录失败: {response.text}")
                return False
        except Exception as e:
            print(f"登录过程出错: {e}")
            print(f"响应内容: {response.text}")
            return False

    def get_course_list(self):
        """
        获取课程列表，过滤掉已结束的课程
        :return: 课程列表
        """
        try:
            url = "https://mooc2-ans.chaoxing.com/visit/courselistdata"

            payload = {
                "courseType": "1",  # 1表示学生课程
                "courseFolderId": "0",
                "query": "",
                "pageHeader": "",
                "single": "0",
                "superstarClass": "0",
            }

            headers = self.base_headers.copy()
            headers["Referer"] = "https://mooc2-ans.chaoxing.com/visit/interaction"
            headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"

            response = self.session.post(url, data=payload, headers=headers)

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找课程列表
            course_items = soup.select(".learnCourse")

            courses = []
            filtered_courses = 0
            for item in course_items:
                try:
                    # 检查课程是否已结束
                    course_status = item.select_one(".not-open-tip")
                    if (
                        not self.include_ended_courses
                        and course_status
                        and "课程已结束" in course_status.text
                    ):
                        filtered_courses += 1
                        continue  # 跳过已结束的课程

                    # 获取课程ID和班级ID
                    course_id_input = item.select_one(".courseId")
                    clazz_id_input = item.select_one(".clazzId")
                    person_id_input = item.select_one(".curPersonId")

                    if course_id_input and clazz_id_input and person_id_input:
                        course_id = course_id_input.get("value")
                        clazz_id = clazz_id_input.get("value")
                        person_id = person_id_input.get("value")

                        # 获取课程名称
                        course_name_span = item.select_one(".course-name")
                        course_name = (
                            course_name_span.text.strip()
                            if course_name_span
                            else "未知课程"
                        )

                        # 获取教师名称
                        teacher_name_p = item.select_one(".line2.color3")
                        teacher_name = (
                            teacher_name_p.text.strip()
                            if teacher_name_p
                            else "未知教师"
                        )

                        courses.append(
                            {
                                "course_name": course_name,
                                "teacher_name": teacher_name,
                                "course_id": course_id,
                                "clazz_id": clazz_id,
                                "person_id": person_id,
                            }
                        )
                except Exception as e:
                    print(f"解析课程项时出错: {e}")

            if filtered_courses > 0:
                print(f"已过滤 {filtered_courses} 门已结束的课程")

            return courses
        except Exception as e:
            print(f"获取课程列表时出错: {e}")
            return []

    def get_homework_list(self, course_id, clazz_id, person_id):
        """
        获取作业列表，过滤掉已完成的作业
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: 作业列表
        """
        try:
            # 生成时间戳
            timestamp = int(time.time() * 1000)

            # 首先检查是否有预先获取的enc参数
            enc = None
            stuenc = None
            if hasattr(self, "course_enc") and self.course_enc:
                enc = self.course_enc

            # 检查是否有预先获取的stuenc参数
            if hasattr(self, "stuenc") and self.stuenc:
                stuenc = self.stuenc

            # 如果没有获取到参数，尝试重新获取
            if not stuenc or not enc:
                # 尝试获取课程enc参数
                from batch_fill_homework import get_course_enc

                course_enc_info = get_course_enc(
                    self.session, course_id, clazz_id, person_id
                )

                if course_enc_info:
                    stuenc = course_enc_info.get("stuenc")
                    enc = course_enc_info.get("enc")
                    # 保存获取到的参数，供后续使用
                    self.stuenc = stuenc
                    self.course_enc = enc
                else:
                    # 尝试直接访问课程详情页面，从页面中提取参数
                    try:
                        course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

                        response = self.session.get(
                            course_url,
                            headers=self.base_headers,
                            verify=False,
                            allow_redirects=True,
                        )

                        # 保存最近的响应，用于检测反爬验证
                        if not hasattr(self.session, "history"):
                            self.session.history = []
                        self.session.history.append(response)

                        # 检查是否遇到反爬验证
                        if "antispiderShowVerify" in response.url:
                            print("警告: 遇到反爬验证，需要等待一段时间后重试")
                            return None

                        # 从响应URL中提取enc参数
                        enc_match = re.search(r"enc=([^&]+)", response.url)
                        if enc_match:
                            enc = enc_match.group(1)
                            self.course_enc = enc

                            # 尝试从页面中提取workEnc参数
                            soup = BeautifulSoup(response.text, "html.parser")

                            # 重要发现：作业按钮(dataname='zy')点击后实际跳转到讨论页面，而考试按钮(dataname='ks')点击后实际跳转到作业页面
                            # 因此，我们优先查找考试按钮，而不是作业按钮
                            exam_button_li = soup.select_one("li[dataname='ks']")
                            if exam_button_li:
                                # 检查是否有考试按钮
                                exam_button = exam_button_li.select_one("a")
                                if exam_button:
                                    # 获取考试按钮的data-url属性
                                    exam_url = exam_button.get("data-url")
                                    if exam_url:
                                        # 尝试从URL中提取enc参数
                                        exam_enc_match = re.search(
                                            r"enc=([^&]+)", exam_url
                                        )
                                        if exam_enc_match:
                                            exam_enc = exam_enc_match.group(1)
                                            enc = exam_enc
                                            self.course_enc = exam_enc

                                        # 访问考试按钮链接(实际是作业页面)
                                        exam_response = self.session.get(
                                            exam_url,
                                            headers=self.base_headers,
                                            verify=False,
                                        )

                                        # 保存最近的响应，用于检测反爬验证
                                        self.session.history.append(exam_response)

                                        # 检查是否遇到反爬验证
                                        if "antispiderShowVerify" in exam_response.url:
                                            print(
                                                "警告: 遇到反爬验证，需要等待一段时间后重试"
                                            )
                                            return None

                                        # 从响应URL中提取stuenc参数
                                        stuenc_match = re.search(
                                            r"stuenc=([^&]+)", exam_response.url
                                        )
                                        if stuenc_match:
                                            stuenc = stuenc_match.group(1)
                                            self.stuenc = stuenc

                                        # 从响应URL中提取enc参数
                                        enc_match = re.search(
                                            r"enc=([^&]+)", exam_response.url
                                        )
                                        if enc_match:
                                            enc = enc_match.group(1)
                                            self.course_enc = enc

                            # 如果没有找到考试按钮，再尝试查找作业按钮(但要注意作业按钮实际跳转到讨论页面)
                            if not (stuenc and enc):
                                work_button_li = soup.select_one("li[dataname='zy']")
                                if work_button_li:
                                    # 检查是否有作业按钮
                                    work_button = work_button_li.select_one("a")
                                    if work_button:
                                        # 获取作业按钮的data-url属性
                                        work_url = work_button.get("data-url")
                                        if work_url:
                                            # 尝试从URL中提取enc参数
                                            work_enc_match = re.search(
                                                r"enc=([^&]+)", work_url
                                            )
                                            if work_enc_match:
                                                work_enc = work_enc_match.group(1)
                                                enc = work_enc
                                                self.course_enc = work_enc

                            # 查找workEnc输入框
                            work_enc_input = soup.select_one("#workEnc")
                            if work_enc_input and work_enc_input.get("value"):
                                work_enc = work_enc_input.get("value")
                                enc = work_enc
                                self.course_enc = work_enc

                            # 如果没有找到stuenc参数，使用enc作为stuenc
                            if not stuenc:
                                stuenc = enc
                                self.stuenc = enc
                        else:
                            # 尝试从页面中的隐藏输入框获取enc参数
                            soup = BeautifulSoup(response.text, "html.parser")
                            enc_input = soup.select_one("#enc")
                            if enc_input and enc_input.get("value"):
                                enc = enc_input.get("value")
                                self.course_enc = enc

                                # 如果没有找到stuenc参数，使用enc作为stuenc
                                if not stuenc:
                                    stuenc = enc
                                    self.stuenc = enc
                            else:
                                return []
                    except Exception as e:
                        print(f"访问课程详情页面出错: {e}")
                    return []

            # 直接构建作业列表请求URL，使用获取到的stuenc和enc参数
            list_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&stuenc={stuenc}&enc={enc}"

            # 设置请求头
            list_headers = self.base_headers.copy()
            list_headers["Referer"] = (
                f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"
            )

            # 添加随机请求头字段，降低被识别为爬虫的概率
            list_headers["Accept-Encoding"] = "gzip, deflate, br"
            list_headers["Connection"] = "keep-alive"
            list_headers["Cache-Control"] = "max-age=0"
            list_headers["Sec-Fetch-Dest"] = "document"
            list_headers["Sec-Fetch-Mode"] = "navigate"
            list_headers["Sec-Fetch-Site"] = "same-origin"
            list_headers["Sec-Fetch-User"] = "?1"
            list_headers["Upgrade-Insecure-Requests"] = "1"

            # 发送GET请求获取作业列表
            list_response = self.session.get(
                list_url, headers=list_headers, verify=False
            )

            # 保存最近的响应，用于检测反爬验证
            if not hasattr(self.session, "history"):
                self.session.history = []
            self.session.history.append(list_response)

            # 检查是否遇到反爬验证
            if "antispiderShowVerify" in list_response.url:
                print("警告: 遇到反爬验证，需要等待一段时间后重试")
                return None

            # 解析响应内容
            soup = BeautifulSoup(list_response.text, "html.parser")

            # 检查是否有错误提示
            error_msg = soup.select_one(".word_null p")
            if error_msg:
                # 尝试交换stuenc和enc参数
                swap_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}&stuenc={enc}&enc={stuenc}"

                # 发送请求
                swap_response = self.session.get(
                    swap_url, headers=list_headers, verify=False
                )

                # 保存最近的响应，用于检测反爬验证
                self.session.history.append(swap_response)

                # 检查是否遇到反爬验证
                if "antispiderShowVerify" in swap_response.url:
                    print("警告: 遇到反爬验证，需要等待一段时间后重试")
                    return None

                # 解析响应
                swap_soup = BeautifulSoup(swap_response.text, "html.parser")

                # 检查是否有错误
                swap_error_msg = swap_soup.select_one(".word_null p")
                if swap_error_msg:
                    # 尝试监控网络请求获取正确的参数
                    # 尝试不同的URL模式
                    alt_urls = [
                        f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s&t={timestamp}",
                        f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&ut=s",
                        f"https://mooc1.chaoxing.com/work/getAllWork?classId={clazz_id}&courseId={course_id}&isdisplaytable=2&mooc=1&ut=s&cpi={person_id}&t={timestamp}",
                    ]

                    for alt_url in alt_urls:
                        try:
                            alt_response = self.session.get(
                                alt_url, headers=list_headers, verify=False
                            )

                            # 保存最近的响应，用于检测反爬验证
                            self.session.history.append(alt_response)

                            # 检查是否遇到反爬验证
                            if "antispiderShowVerify" in alt_response.url:
                                print("警告: 遇到反爬验证，需要等待一段时间后重试")
                                continue

                            alt_soup = BeautifulSoup(alt_response.text, "html.parser")
                            alt_error_msg = alt_soup.select_one(".word_null p")

                            if not alt_error_msg:
                                response = alt_response
                                soup = alt_soup
                                break
                        except Exception as e:
                                                        print(f"访问替代URL出错: {e}")
                    else:
                        return []
                else:
                    response = swap_response
                    soup = swap_soup
            else:
                response = list_response

            # 获取作业列表项
            homework_items = soup.select(".bottomList li")

            homeworks = []
            filtered_homeworks = 0

            for item in homework_items:
                try:
                    # 获取作业状态
                    status_elem = item.select_one(".status")
                    status = status_elem.text.strip() if status_elem else "未知状态"

                    # 过滤掉已完成的作业
                    if not self.include_finished and status == "已完成":
                        filtered_homeworks += 1
                        continue

                    # 获取作业标题
                    title_elem = item.select_one(".overHidden2")
                    title = title_elem.text.strip() if title_elem else "未知作业"

                    # 获取作业链接，从data属性获取
                    link = item.get("data") or ""

                    # 获取作业ID和答案ID
                    work_id = None
                    answer_id = None
                    if link:
                        work_id_match = re.search(r"workId=(\d+)", link)
                        if work_id_match:
                            work_id = work_id_match.group(1)

                        answer_id_match = re.search(r"answerId=(\d+)", link)
                        if answer_id_match:
                            answer_id = answer_id_match.group(1)

                    homework_info = {
                        "title": title,
                        "status": status,
                        "link": link,
                        "work_id": work_id,
                        "answer_id": answer_id,
                        "course_id": course_id,
                        "clazz_id": clazz_id,
                        "cpi": person_id,
                    }

                    homeworks.append(homework_info)
                except Exception as e:
                    print(f"解析作业项时出错: {e}")

            return homeworks
        except Exception as e:
            print(f"获取作业列表时出错: {e}")
            return []

    def get_exam_list(self, course_id, clazz_id, person_id):
        """
        获取考试列表
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: 考试列表
        """
        try:
            import urllib3
            from bs4 import BeautifulSoup

            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # 生成时间戳
            timestamp = int(time.time() * 1000)

            # 首先检查是否有预先获取的enc参数
            enc = None
            stuenc = None
            if hasattr(self, "exam_enc") and self.exam_enc:
                enc = self.exam_enc

            # 检查是否有预先获取的stuenc参数
            if hasattr(self, "exam_stuenc") and self.exam_stuenc:
                stuenc = self.exam_stuenc

            # 如果没有获取到参数，尝试重新获取
            if not stuenc or not enc:
                # 尝试获取课程enc参数，使用考试按钮
                course_enc_info = self.get_course_enc_for_exam(course_id, clazz_id, person_id)

                if course_enc_info:
                    stuenc = course_enc_info.get("stuenc")
                    enc = course_enc_info.get("enc")
                    openc = course_enc_info.get("openc")
                    # 保存获取到的参数，供后续使用
                    self.exam_stuenc = stuenc
                    self.exam_enc = enc
                    self.exam_openc = openc
                else:
                    # 尝试直接访问课程详情页面，从页面中提取参数
                    try:
                        course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

                        response = self.session.get(
                            course_url,
                            headers=self.base_headers,
                            verify=False,
                            allow_redirects=True,
                            timeout=15
                        )

                        # 从响应URL中提取enc参数
                        enc_match = re.search(r"enc=([^&]+)", response.url)
                        if enc_match:
                            enc = enc_match.group(1)
                            self.exam_enc = enc
                            stuenc = enc  # 默认使用相同的值
                            self.exam_stuenc = stuenc
                        else:
                            # 尝试从页面中的隐藏输入框获取enc参数
                            soup = BeautifulSoup(response.text, "html.parser")
                            enc_input = soup.select_one("#examEnc")
                            if enc_input and enc_input.get("value"):
                                enc = enc_input.get("value")
                                self.exam_enc = enc
                                stuenc = enc
                                self.exam_stuenc = enc
                            else:
                                return []
                    except Exception:
                        return []

            # 随机User-Agent列表
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.83 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
            ]

            # 设置请求头
            list_headers = self.base_headers.copy()
            list_headers["Referer"] = (
                f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"
            )
            list_headers["User-Agent"] = random.choice(user_agents)

            # 构建考试列表URL
            url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={timestamp}"
            
            # 添加加密参数
            if stuenc:
                url += f"&stuenc={stuenc}"
            if enc:
                url += f"&enc={enc}"
            if hasattr(self, "exam_openc") and self.exam_openc:
                url += f"&openc={self.exam_openc}"

            # 发送请求
            response = self.session.get(
                url, headers=list_headers, verify=False, timeout=15
            )

            # 检查是否遇到反爬验证
            if "antispiderShowVerify" in response.url:
                # 如果遇到反爬验证，更换User-Agent并返回空列表
                list_headers["User-Agent"] = random.choice(user_agents)
                return []

            # 解析页面
            soup = BeautifulSoup(response.text, "html.parser")

            # 获取考试列表项
            exam_items = soup.select(".examListItem")
            if not exam_items:
                # 尝试其他选择器
                exam_items = soup.select(".bottomList li")
            
            if not exam_items:
                return []

            exams = []
            for item in exam_items:
                try:
                    # 获取考试状态
                    status_elem = item.select_one(".status")
                    status = status_elem.text.strip() if status_elem else "未知状态"

                    # 获取考试标题
                    title_elem = item.select_one(".overHidden2")
                    if not title_elem:
                        title_elem = item.select_one(".examTitle")
                    title = title_elem.text.strip() if title_elem else "未知考试"

                    # 获取考试链接
                    link = item.get("data") or ""
                    if not link:
                        link_elem = item.select_one("a")
                        if link_elem and link_elem.get("href"):
                            link = link_elem.get("href")

                    # 获取考试ID
                    exam_id = None
                    if link:
                        exam_id_match = re.search(r"examId=(\d+)", link)
                        if exam_id_match:
                            exam_id = exam_id_match.group(1)

                    # 从链接中提取enc参数
                    enc_param = ""
                    if link:
                        enc_match = re.search(r"enc=([^&]+)", link)
                        if enc_match:
                            enc_param = enc_match.group(1)

                    # 构建考试信息
                    exam_info = {
                        "title": title,
                        "status": status,
                        "link": link,
                        "exam_id": exam_id,
                        "course_id": course_id,
                        "clazz_id": clazz_id,
                        "cpi": person_id,
                        "enc": enc_param,
                    }

                    exams.append(exam_info)
                except Exception:
                    pass

            return exams
        except Exception:
            return []

    def get_course_enc_for_exam(self, course_id, clazz_id, person_id):
        """
        通过访问课程详情页面，模拟点击考试按钮，获取考试列表的enc参数
        :param course_id: 课程ID
        :param clazz_id: 班级ID
        :param person_id: 个人ID
        :return: enc参数字典，包含stuenc、enc和openc参数
        """
        try:
            # 构建课程详情页链接
            course_url = f"https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ismooc2=1"

            headers = self.base_headers.copy()

            # 访问课程详情页，可能会重定向
            response = self.session.get(
                course_url, headers=headers, verify=False, allow_redirects=True
            )

            # 解析页面内容，获取考试按钮的相关信息
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(response.text, "html.parser")

            # 首先尝试从页面中获取enc和stuenc参数
            enc = None
            stuenc = None
            openc = None

            # 从隐藏输入框中获取
            enc_input = soup.select_one("#examEnc")
            if enc_input and enc_input.get("value"):
                enc = enc_input.get("value")

            # 尝试获取openc参数
            openc_input = soup.select_one("#openc")
            if openc_input and openc_input.get("value"):
                openc = openc_input.get("value")

            # 尝试获取stuenc参数（可能不存在）
            stuenc_input = soup.select_one("#stuenc")
            if stuenc_input and stuenc_input.get("value"):
                stuenc = stuenc_input.get("value")

            # 如果没有找到stuenc参数，可以尝试使用userId作为stuenc参数
            if not stuenc:
                user_id_input = soup.select_one("#userId")
                if user_id_input and user_id_input.get("value"):
                    user_id = user_id_input.get("value")

            # 如果已经获取到了enc参数，但没有stuenc参数，可以尝试使用enc作为stuenc
            if enc and not stuenc:
                stuenc = enc

            # 如果已经获取到了参数，可以直接返回
            if enc:  # 只要有enc参数就可以返回，stuenc可以为空
                return {"stuenc": stuenc or enc, "enc": enc, "openc": openc}

            # 如果没有直接获取到参数，继续尝试通过点击考试按钮获取
            # 获取所有导航按钮，用于后续分析
            nav_buttons = soup.select(".nav-content ul li")

            # 找到考试按钮(dataname='ks')
            exam_button_li = None
            for btn in nav_buttons:
                if btn.get("dataname") == "ks":
                    exam_button_li = btn
                    break

            if exam_button_li:
                # 获取考试按钮的链接
                exam_button = exam_button_li.select_one("a")
                if exam_button:
                    # 优先使用data-url属性
                    exam_url = exam_button.get("data-url")
                    if not exam_url:
                        exam_url = exam_button.get("href")

                    if exam_url:
                        # 如果是相对路径，转换为绝对路径
                        if exam_url.startswith("/"):
                            exam_url = f"https://mooc1.chaoxing.com{exam_url}"

                        # 如果是javascript:void(0)这样的链接，需要特殊处理
                        if exam_url.startswith("javascript:"):
                            # 尝试构建考试列表URL
                            exam_url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"

                        # 访问考试按钮链接
                        exam_response = self.session.get(exam_url, headers=headers, verify=False)

                        # 从响应URL中提取参数
                        stuenc_match = re.search(r"stuenc=([^&]+)", exam_response.url)
                        enc_match = re.search(r"enc=([^&]+)", exam_response.url)
                        openc_match = re.search(r"openc=([^&]+)", exam_response.url)

                        if stuenc_match:
                            stuenc = stuenc_match.group(1)

                        if enc_match:
                            enc = enc_match.group(1)
                            
                        if openc_match:
                            openc = openc_match.group(1)

                        # 检查pageHeader参数，确认是否为考试页面(pageHeader=9)
                        pageheader_match = re.search(r"pageHeader=(\d+)", exam_response.url)
                        if pageheader_match:
                            pageheader = pageheader_match.group(1)
                            if pageheader != "9":
                                # 如果不是考试页面，可能需要额外处理
                                pass

                        # 尝试直接访问考试列表页面
                        if stuenc or enc:
                            exam_list_url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"
                            
                            if stuenc:
                                exam_list_url += f"&stuenc={stuenc}"
                            if enc:
                                exam_list_url += f"&enc={enc}"
                            if openc:
                                exam_list_url += f"&openc={openc}"

                            list_response = self.session.get(
                                exam_list_url, headers=headers, verify=False
                            )

                            # 从响应URL中提取最终的enc参数
                            final_enc_match = re.search(r"enc=([^&]+)", list_response.url)
                            if final_enc_match:
                                enc = final_enc_match.group(1)

                            # 从响应URL中提取最终的openc参数
                            final_openc_match = re.search(r"openc=([^&]+)", list_response.url)
                            if final_openc_match:
                                openc = final_openc_match.group(1)

                            # 检查考试列表页面内容，查找可能包含enc参数的隐藏输入框
                            list_soup = BeautifulSoup(list_response.text, "html.parser")
                            enc_inputs = list_soup.select(
                                "input[type='hidden'][name='enc'], input[type='hidden'][id='enc']"
                            )
                            for input_elem in enc_inputs:
                                if input_elem.get("value"):
                                    enc = input_elem.get("value")
                                    break

                            if stuenc or enc:
                                return {"stuenc": stuenc or enc, "enc": enc or stuenc, "openc": openc}

            # 如果所有方法都失败了，尝试直接构建考试列表URL
            exam_list_url = f"https://mooc1.chaoxing.com/exam-ans/mooc2/exam/exam-list?courseid={course_id}&clazzid={clazz_id}&cpi={person_id}&ut=s&t={int(time.time() * 1000)}"

            list_response = self.session.get(
                exam_list_url, headers=headers, verify=False
            )

            stuenc_match = re.search(r"stuenc=([^&]+)", list_response.url)
            enc_match = re.search(r"enc=([^&]+)", list_response.url)
            openc_match = re.search(r"openc=([^&]+)", list_response.url)

            if stuenc_match:
                stuenc = stuenc_match.group(1)

            if enc_match:
                enc = enc_match.group(1)
                
            if openc_match:
                openc = openc_match.group(1)

            if stuenc or enc:
                return {"stuenc": stuenc or enc, "enc": enc or stuenc, "openc": openc}

            # 如果URL中没有参数，尝试从页面内容中提取
            list_soup = BeautifulSoup(list_response.text, "html.parser")

            # 尝试从页面中的隐藏输入框中提取enc参数
            enc_inputs = list_soup.select(
                "input[type='hidden'][name='enc'], input[type='hidden'][id='enc']"
            )
            for input_elem in enc_inputs:
                if input_elem.get("value"):
                    enc = input_elem.get("value")
                    break

            # 如果所有方法都失败了，但至少有一个参数，尝试返回
            if stuenc or enc:
                return {"stuenc": stuenc or enc, "enc": enc or stuenc, "openc": openc}

            return None
        except Exception:
            return None

    def get_homework_detail(self, homework):
        """
        获取作业详情
        :param homework: 作业信息
        :return: 作业详情
        """
        try:
            url = homework["link"]

            headers = self.base_headers.copy()
            headers["Referer"] = (
                f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={homework['course_id']}&classId={homework['clazz_id']}&cpi={homework['cpi']}"
            )

            response = self.session.get(url, headers=headers)

            # 保存作业详情页面，以便分析
            filename = f"homework_detail_{homework['work_id']}.html"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(response.text)

            print(f"已保存作业详情页面到 {filename}")

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # 获取作业标题
            title = (
                soup.select_one(".mark_title").text.strip()
                if soup.select_one(".mark_title")
                else homework["title"]
            )

            # 获取作业题目
            questions = []
            question_items = soup.select(".questionLi")

            for i, item in enumerate(question_items, 1):
                try:
                    # 获取题目类型
                    type_span = item.select_one(".colorShallow")
                    question_type = (
                        type_span.text.strip().replace("(", "").replace(")", "")
                        if type_span
                        else "未知类型"
                    )

                    # 获取题目内容
                    content_elem = item.select_one(".mark_name")
                    content = content_elem.text.strip() if content_elem else f"题目 {i}"

                    # 获取题目ID
                    question_id = item.get("data") or ""

                    questions.append(
                        {
                            "question_id": question_id,
                            "question_type": question_type,
                            "content": content,
                            "index": i,
                        }
                    )
                except Exception as e:
                    print(f"解析题目时出错: {e}")

            return {"title": title, "questions": questions, "html": response.text}
        except Exception as e:
            print(f"获取作业详情时出错: {e}")
            return None

    def submit_homework_answer(self, homework, answers):
        """
        提交作业答案
        :param homework: 作业信息
        :param answers: 答案信息，格式为 {question_id: answer}
        :return: 提交结果
        """
        try:
            # 构建提交URL
            url = f"https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"

            # 构建提交数据
            payload = {
                "courseId": homework["course_id"],
                "classId": homework["clazz_id"],
                "knowledgeid": "0",
                "cpi": homework["cpi"],
                "workRelationId": homework["work_id"],
                "workAnswerId": homework["answer_id"],
                "jobid": "",
                "enc_work": "",
                "totalQuestionNum": "",
                "pyFlag": "",
                "answerwqbid": "",
                "mooc2": "1",
                "randomOptions": "false",
            }

            # 添加每个题目的答案
            for question_id, answer in answers.items():
                payload[f"answer{question_id}"] = answer
                payload[f"answertype{question_id}"] = "4"  # 假设所有题目都是简答题

            headers = self.base_headers.copy()
            headers["Referer"] = homework["link"]
            headers["Content-Type"] = "application/x-www-form-urlencoded"

            response = self.session.post(url, data=payload, headers=headers)

            # 检查提交结果
            if "提交成功" in response.text:
                return {"success": True, "message": "作业提交成功"}
            else:
                return {"success": False, "message": f"作业提交失败: {response.text}"}
        except Exception as e:
            print(f"提交作业答案时出错: {e}")
            return {"success": False, "message": f"提交作业答案时出错: {str(e)}"}


def show_menu():
    """
    显示主菜单
    """
    print("\n===== 超星学习通作业管理系统 =====")
    print("1. 获取课程列表")
    print("2. 获取作业列表")
    print("3. 查看作业详情")
    print("4. 自动完成作业")
    print("5. 退出系统")
    print("================================")
    choice = input("请选择操作: ")
    return choice


def main():
    # 使用denglu.py中的加密用户名和密码
    username = "D2bCfRqh3U9bDhfsvVUneg=="
    password = "/H+E5YEkVpxVz37gYPB0xg=="

    manager = ChaoxingHomeworkManager(username, password)

    # 先登录
    login_success = manager.login()

    if login_success:
        # 登录成功后等待一下，确保登录状态生效
        print("等待2秒，确保登录状态生效...")
        time.sleep(2)

        # 获取课程列表
        print("\n获取课程列表...")
        courses = manager.get_course_list()

        if courses:
            print(f"找到 {len(courses)} 门课程:")
            for i, course in enumerate(courses, 1):
                print(f"{i}. {course['course_name']} - {course['teacher_name']}")

            # 让用户选择一门课程
            selected_index = 0
            try:
                selected_index = int(input("\n请选择要查看作业的课程编号: ")) - 1
                if selected_index < 0 or selected_index >= len(courses):
                    print("无效的课程编号，默认选择第一门课程")
                    selected_index = 0
            except:
                print("输入无效，默认选择第一门课程")
                selected_index = 0

            selected_course = courses[selected_index]
            print(f"\n已选择课程: {selected_course['course_name']}")

            # 获取作业列表
            print("\n获取作业列表...")
            homeworks = manager.get_homework_list(
                selected_course["course_id"],
                selected_course["clazz_id"],
                selected_course["person_id"],
            )

            if homeworks:
                print(f"找到 {len(homeworks)} 个作业:")
                for i, homework in enumerate(homeworks, 1):
                    print(f"{i}. {homework['title']} - 状态: {homework['status']}")
                    if homework["work_id"]:
                        print(f"   作业ID: {homework['work_id']}")
                    if homework["link"]:
                        print(f"   作业链接: {homework['link']}")

                # 让用户选择一个作业
                homework_index = 0
                try:
                    homework_index = (
                        int(input("\n请选择要查看的作业编号(0表示退出): ")) - 1
                    )
                    if homework_index < 0:
                        print("已退出")
                        return
                    if homework_index >= len(homeworks):
                        print("无效的作业编号，默认选择第一个作业")
                        homework_index = 0
                except:
                    print("输入无效，默认选择第一个作业")
                    homework_index = 0

                selected_homework = homeworks[homework_index]
                print(f"\n已选择作业: {selected_homework['title']}")

                # 获取作业详情
                print("\n获取作业详情...")
                homework_detail = manager.get_homework_detail(selected_homework["link"])

                if homework_detail:
                    print(f"作业标题: {homework_detail['title']}")
                    print(f"题目数量: {len(homework_detail['questions'])}")

                    # 显示题目
                    for i, question in enumerate(homework_detail["questions"], 1):
                        print(f"\n题目 {i}: {question['title']}")
                        print(f"类型: {question['type']}")
                        if question.get("options"):
                            print("选项:")
                            for j, option in enumerate(question["options"], 1):
                                print(f"  {j}. {option}")

                    # 询问是否提交作业
                    submit = input("\n是否提交作业?(y/n): ").lower()
                    if submit == "y":
                        # 生成答案
                        answers = manager.generate_answers(homework_detail["questions"])

                        # 提交作业
                        print("\n提交作业...")
                        submit_result = manager.submit_homework(
                            selected_homework["course_id"],
                            selected_homework["clazz_id"],
                            selected_homework["cpi"],
                            selected_homework["work_id"],
                            selected_homework["answer_id"],
                            answers,
                        )

                        if submit_result:
                            print("作业提交成功!")
                        else:
                            print("作业提交失败!")
                else:
                    print("获取作业详情失败")
            else:
                print("未找到作业")
        else:
            print("未找到课程")
    else:
        print("登录失败，无法继续操作")


if __name__ == "__main__":
    main()
