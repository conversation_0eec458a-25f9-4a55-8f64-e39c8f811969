# -*- coding: utf-8 -*-
"""
强制应用所有修复，确保代码是最新版本
解决用户运行旧版本代码的问题
"""

import sys
import os
import shutil
import traceback
from loguru import logger

def backup_files():
    """备份原始文件"""
    try:
        logger.info("=== 备份原始文件 ===")
        
        files_to_backup = [
            "API/Questionbank.py",
            "data/Exam.py",
            "学习通跑单启动文件.py"
        ]
        
        backup_dir = "backup_before_force_fix"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        for file_path in files_to_backup:
            if os.path.exists(file_path):
                backup_path = os.path.join(backup_dir, file_path.replace("/", "_").replace("\\", "_"))
                shutil.copy2(file_path, backup_path)
                logger.info(f"✓ 备份 {file_path} -> {backup_path}")
            else:
                logger.warning(f"文件不存在: {file_path}")
        
        logger.info("✓ 文件备份完成")
        return True
        
    except Exception as e:
        logger.error(f"备份文件失败: {str(e)}")
        return False

def force_fix_questionbank():
    """强制修复questionbank函数"""
    try:
        logger.info("=== 强制修复questionbank函数 ===")
        
        file_path = "API/Questionbank.py"
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
        
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否已经修复
        if "return None, 1  # 返回qnum=1以触发备用题库查询" in content:
            logger.info("✓ questionbank函数已经修复")
            return True
        
        # 强制修复
        old_pattern = "return None, 0"
        new_pattern = "return None, 1  # 返回qnum=1以触发备用题库查询"
        
        if old_pattern in content:
            content = content.replace(old_pattern, new_pattern)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            logger.success("✓ questionbank函数修复完成")
            return True
        else:
            logger.warning("未找到需要修复的代码")
            return False
        
    except Exception as e:
        logger.error(f"强制修复questionbank函数失败: {str(e)}")
        return False

def force_fix_fill_blank():
    """强制修复填空题处理逻辑"""
    try:
        logger.info("=== 强制修复填空题处理逻辑 ===")
        
        file_path = "data/Exam.py"
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
        
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否已经修复
        if "主题库填空题答案" in content and "备用题库填空题答案" in content:
            logger.info("✓ 填空题处理逻辑已经修复")
            return True
        
        # 查找填空题处理位置
        if "elif self.qtp == 2:" in content:
            # 找到填空题处理的开始位置
            lines = content.split('\n')
            start_line = -1
            end_line = -1
            
            for i, line in enumerate(lines):
                if "elif self.qtp == 2:" in line:
                    start_line = i
                elif start_line != -1 and ("elif self.qtp == 3:" in line or "else:" in line):
                    end_line = i
                    break
            
            if start_line != -1 and end_line != -1:
                # 替换填空题处理逻辑
                new_fill_blank_logic = '''            elif self.qtp == 2:
                # 填空题处理逻辑，不需要调用Answers方法（因为填空题没有选项）
                if answer and answer != "未收录答案":
                    # 主题库找到答案，直接使用
                    logger.success(f"ID:{self.username},主题库填空题答案: {answer}")
                    ans = answer.strip()
                    self.preview(ans)
                else:
                    # 主题库没有答案，尝试备用题库
                    logger.info(f"ID:{self.username},主题库未找到填空题答案，尝试备用题库")
                    try:
                        backup_answer, _ = questionbank2(clean_question)
                        if backup_answer and backup_answer != "未收录答案":
                            logger.success(f"ID:{self.username},备用题库填空题答案: {backup_answer}")
                            ans = backup_answer.strip()
                            self.preview(ans)
                        else:
                            # 备用题库也没有答案，尝试AI答题
                            logger.info(f"ID:{self.username},备用题库未找到填空题答案，尝试AI答题")
                            try:
                                ai_answer = self._get_ai_answer_for_exam(self.question, self.qtp)
                                if ai_answer:
                                    logger.success(f"ID:{self.username},AI生成填空题答案: {ai_answer}")
                                    ans = ai_answer.strip()
                                    self.preview(ans)
                                else:
                                    logger.warning(f"ID:{self.username},AI答题失败，填空题使用空答案")
                                    ans = ""
                                    self.preview(ans)
                            except Exception as e:
                                logger.error(f"ID:{self.username},AI答题异常: {str(e)}")
                                ans = ""
                                self.preview(ans)
                    except Exception as e:
                        logger.error(f"ID:{self.username},备用题库查询异常: {str(e)}")
                        ans = ""
                        self.preview(ans)'''
                
                # 重建内容
                new_lines = lines[:start_line] + new_fill_blank_logic.split('\n') + lines[end_line:]
                new_content = '\n'.join(new_lines)
                
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                
                logger.success("✓ 填空题处理逻辑修复完成")
                return True
            else:
                logger.error("无法找到填空题处理逻辑的位置")
                return False
        else:
            logger.error("未找到填空题处理分支")
            return False
        
    except Exception as e:
        logger.error(f"强制修复填空题处理逻辑失败: {str(e)}")
        return False

def verify_fixes():
    """验证修复是否成功"""
    try:
        logger.info("=== 验证修复结果 ===")
        
        # 验证questionbank修复
        with open("API/Questionbank.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "return None, 1  # 返回qnum=1以触发备用题库查询" in content:
            logger.info("✓ questionbank函数修复验证通过")
        else:
            logger.error("✗ questionbank函数修复验证失败")
            return False
        
        # 验证填空题修复
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            "主题库填空题答案",
            "备用题库填空题答案",
            "AI生成填空题答案"
        ]
        
        all_passed = True
        for check in checks:
            if check in content:
                logger.debug(f"✓ {check}")
            else:
                logger.error(f"✗ 缺少{check}")
                all_passed = False
        
        if all_passed:
            logger.info("✓ 填空题处理逻辑修复验证通过")
        else:
            logger.error("✗ 填空题处理逻辑修复验证失败")
            return False
        
        logger.success("✓ 所有修复验证通过")
        return True
        
    except Exception as e:
        logger.error(f"验证修复结果失败: {str(e)}")
        return False

def create_version_marker():
    """创建版本标记文件"""
    try:
        logger.info("=== 创建版本标记 ===")
        
        from datetime import datetime
        version_info = {
            "version": "9006_comprehensive_fix_v2",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "fixes": [
                "questionbank函数返回值修复",
                "填空题处理逻辑修复",
                "考试详细信息提取功能",
                "作答情况和重考机会信息提取改进"
            ]
        }
        
        with open("version_info.txt", "w", encoding="utf-8") as f:
            f.write(f"版本: {version_info['version']}\n")
            f.write(f"时间: {version_info['timestamp']}\n")
            f.write("修复内容:\n")
            for fix in version_info["fixes"]:
                f.write(f"- {fix}\n")
        
        logger.info("✓ 版本标记创建完成")
        return True
        
    except Exception as e:
        logger.error(f"创建版本标记失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("强制应用所有修复")
    logger.info("=" * 60)
    logger.info("目标: 确保用户运行的是最新版本的代码")
    
    steps = [
        ("备份原始文件", backup_files),
        ("强制修复questionbank函数", force_fix_questionbank),
        ("强制修复填空题处理逻辑", force_fix_fill_blank),
        ("验证修复结果", verify_fixes),
        ("创建版本标记", create_version_marker)
    ]
    
    passed = 0
    total = len(steps)
    
    for step_name, step_func in steps:
        logger.info(f"\n--- {step_name} ---")
        try:
            if step_func():
                passed += 1
            else:
                logger.warning(f"{step_name}: 失败")
                break  # 如果某个步骤失败，停止后续步骤
        except Exception as e:
            logger.error(f"{step_name}: 异常 - {str(e)}")
            traceback.print_exc()
            break
    
    logger.info("\n" + "=" * 60)
    logger.info(f"修复结果: {passed}/{total} 完成")
    
    if passed == total:
        logger.success("🎉 所有修复强制应用成功！")
        logger.info("\n修复总结:")
        logger.info("✅ questionbank函数返回值已修复")
        logger.info("✅ 填空题处理逻辑已修复")
        logger.info("✅ 所有修复已验证")
        logger.info("✅ 版本标记已创建")
        logger.info("\n现在用户应该能够正常使用所有功能了！")
        return True
    else:
        logger.warning(f"⚠ 有{total-passed}个步骤失败，修复可能不完整")
        return False

if __name__ == "__main__":
    main()
