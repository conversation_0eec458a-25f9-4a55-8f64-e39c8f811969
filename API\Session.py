import random
import time
import traceback
import requests
from bs4 import BeautifulSoup
from yarl import URL
import re
from loguru import logger
from ddddocr import DdddOcr
import urllib3
import os
import certifi

# 设置证书路径
os.environ["REQUESTS_CA_BUNDLE"] = certifi.where()
os.environ["SSL_CERT_FILE"] = certifi.where()

API_CAPTCHA_IMG = "https://mooc1-api.chaoxing.com/processVerifyPng.ac"
API_CAPTCHA_SUBMIT = "https://mooc1-api.chaoxing.com/html/processVerify.ac"
API_CAPTCHA_IMG2 = "https://mooc2-ans.chaoxing.com/antispiderShowVerify.ac"
API_CAPTCHA_SUBMIT2 = "https://mooc2-ans.chaoxing.com/html/processVerify.ac"
API_CAPTCHA_SUBMIT3 = "https://mooc1-api.chaoxing.com/json/processVerify.ac"
API_CAPTCHA_IMG3 = "https://mooc2-ans.chaoxing.com/processVerifyPng.ac"
API_CAPTCHA_IMG4 = "https://mooc1.chaoxing.com/exam-ans/processVerifyPng.ac"


class Proxies:
    def __init__(self):
        self.ip = self.getip()

    def getip(self):
        data = [
            "socks5://fmopu9282179:2479@**************:9104",
            "socks5://agsOZ5282189:2458@**************:9101",
            "socks5://ctwCK2282105:1238@*************:9138",
            "socks5://ksuFU0282106:0129@**************:9101",
            "socks5://rGHLU4282135:0689@**************:9134",
            "socks5://norFX8282136:1278@**************:9126",
            "socks5://dhsuCR282138:3459@**************:9117",
            "socks5://cmGIQV282107:2467@*************:9102",
            "socks5://fhiFP8282156:3569@*************:9120",
            "socks5://egoDRZ282117:1249@**************:9111",
            "socks5://eBHZ67282109:3567@*************:9103",
            "socks5://jrzLX5282168:2378@************:9105",
            "socks5://ablqHM282102:0258@*************:9108",
            "socks5://iAFY69282156:1248@**************:9110",
            "socks5://czQV37282126:3468@**************:9106",
            "socks5://uFPU26282134:0157@101.89.112.177:9114",
            "socks5://ajFM34282102:1279@180.163.88.148:9106",
            "socks5://hkuxMP282107:0568@101.226.17.170:9135",
            "socks5://bcgsNS282114:1245@61.171.118.19:9107",
            "socks5://ejFQ35282114:5678@61.171.78.176:9102",
            "socks5://bmqsCZ282147:1367@114.80.43.124:9106",
            "socks5://msxJY6282189:1468@180.163.75.18:9103",
            "socks5://egprwM282103:2457@101.91.147.122:9143",
            "socks5://bgjmPQ282118:0367@218.78.123.108:9103",
            "socks5://gowFKM282118:0256@61.171.107.249:9106",
            "socks5://bdADT9282128:0479@114.80.43.158:9101",
            "socks5://beiBFW282159:2689@218.78.123.102:9105",
            "socks5://hqwAJ6282127:1369@101.227.49.85:9101",
            "socks5://hvCGU8282112:0369@61.171.115.109:9102",
            "socks5://fiyJ07282114:0245@61.171.111.115:9104",
            "socks5://detCS5282126:3578@101.89.203.181:9103",
            "socks5://pvIS17282107:1239@61.171.1.32:9131",
            "socks5://efjnrD282117:1278@61.171.79.106:9103",
            "socks5://ekuBT3282101:0158@114.80.43.175:9104",
            "socks5://aloCFG282119:0478@101.89.109.109:9121",
            "socks5://bprvX0282136:0278@218.78.78.71:9115",
            "socks5://hkQY68282126:0189@101.226.23.184:9118",
            "socks5://ikyOY8282156:2679@61.171.97.164:9102",
            "socks5://ezABM1282103:0136@218.78.45.65:9119",
            "socks5://cejkuU282114:1279@180.163.90.107:9107",
            "socks5://gsFGKM282116:0138@101.226.22.30:9106",
            "socks5://cmAT18282115:1579@61.171.33.157:9102",
            "socks5://afkABQ282103:0348@114.80.43.145:9108",
            "socks5://chjJMO282115:1269@101.91.151.145:9115",
            "socks5://esANW1282124:3589@61.171.116.86:9109",
            "socks5://cjpqwD282103:1479@101.227.52.147:9105",
            "socks5://lnquL2282126:2459@114.80.43.179:9103",
            "socks5://kHL034282135:0269@101.227.55.10:9141",
            "socks5://hJM026282118:0357@218.78.133.233:9107",
            "socks5://mpqG25282108:0156@218.78.42.47:9133",
            "socks5://cgzGU5282148:2346@61.171.116.37:9107",
            "socks5://eghpF8282104:0157@101.227.50.231:9144",
            "socks5://jkqU79282113:2678@**************:9114",
            "socks5://uyA389282105:4589@*************:9109",
            "socks5://asDQW0282156:2389@**************:9118",
            "socks5://nvCJU6282118:1248@**************:9139",
            "socks5://citwyU282105:0246@*************:9105",
            "socks5://cgzM07282118:3478@**************:9106",
            "socks5://ciqyMQ282179:1579@**************:9110",
            "socks5://eiFLU3282178:0148@*************:9109",
        ]

        ip = random.choice(data)
        data = {"http": ip, "https": ip}
        return data


def post1(ip):
    try:
        page_url = "http://httpbin.org/ip"  # 'https://dev.kdlapi.com/testproxy'
        web = requests.get(page_url, proxies=ip, timeout=30, verify=False)
        # IP = web.text.replace("sucess! client ", "")

        r = requests.post(
            "https://passport2-api.chaoxing.com/fanyalogin",
            params={
                "fid": "-1",
                "uname": "NAFoYnTNPfuDshVnnfB/YA==",
                "password": "NAFoYnTNPfuDshVnnfB/YA==",
                "refer": "http%3A%2F%2Fi.mooc.chaoxing.com",
                "t": "true",
                "forbidotherlogin": "0",
                "validate": "",
                "doubleFactorLogin": "0",
                "independentId": "0",
                "independentNameId": "0",
            },
            proxies=ip,
            timeout=30,
            verify=False,
        )
        # logger.success(f"IP:{web.json()}正常")
        return True
    except:
        traceback.print_exc()
        logger.debug(f"IP：{ip} - 无效")
        return False


class SpecialPageType:
    NORMAL = False
    CAPTCHA = False
    FACE = False


class StartSession(object):
    def __init__(self, session):
        self.session = session
        # 禁用SSL证书验证
        self.session.verify = False
        # 关闭安全警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        self.logger = logger
        self.__captcha_max_retry = 5  #   最大验证码重试次数
        self.SpecialPageType = SpecialPageType()
        # 禁用代理，直接使用空字典
        self.proxies = {}

    def __cb_resolve_captcha_after(self, times: int):
        self.logger.info(f"正在识别验证码，第 {times} 次...")

    def __get_captcha_image(self):
        resp = self.get(API_CAPTCHA_IMG, params={"t": round(time.time() * 1000)})
        if not resp.ok or resp.headers["Content-Type"] != "image/png":
            self.logger.warning(f"验证码图片获取失败 {resp.status_code}")
            resp = self.get(API_CAPTCHA_IMG2, params={"t": round(time.time() * 1000)})
            if not resp.ok or resp.headers["Content-Type"] != "image/png":
                self.logger.warning(f"验证码图片获取失败 {resp.status_code}")
                resp = self.get(
                    API_CAPTCHA_IMG3, params={"t": round(time.time() * 1000)}
                )
                if not resp.ok or resp.headers["Content-Type"] != "image/png":
                    self.logger.warning(f"验证码图片获取失败 {resp.status_code}")
                    resp = self.get(
                        API_CAPTCHA_IMG4, params={"t": round(time.time() * 1000)}
                    )
                    if not resp.ok or resp.headers["Content-Type"] != "image/png":
                        self.logger.warning(f"验证码图片获取失败 {resp.status_code}")
                        return None
        self.logger.info(f"验证码图片已获取 (大小 {len(resp.content) / 1024:.2f}KB)")
        return resp.content

    def __submit_captcha(self, code: str) -> bool:
        resp = self.post(
            API_CAPTCHA_SUBMIT,
            data={
                "app": 0,
                "ucode": code,
            },
            allow_redirects=False,  # 阻止重定向，以进一步操作
        )

        # HTTP 302 即验证正确，HTTP 202 即验证错误
        if resp.status_code == 302:
            self.logger.info(f"验证码验证成功 {code}")
            return True
        resp = self.post(
            API_CAPTCHA_SUBMIT2,
            data={
                "app": 0,
                "ucode": code,
            },
            allow_redirects=False,  # 阻止重定向，以进一步操作
        )
        if resp.status_code == 302:
            self.logger.info(f"验证码验证成功 {code}")
            return True
        resp = self.post(
            API_CAPTCHA_SUBMIT3,
            data={
                "app": 0,
                "ucode": code,
            },
            allow_redirects=False,  # 阻止重定向，以进一步操作
        )
        if resp.status_code == 302:
            self.logger.info(f"验证码验证成功 {code}")
            return True
        self.logger.warning(f"验证码验证失败 {code}")
        return False

    def identify_captcha(self, img: bytes):
        code = DdddOcr(show_ad=False).classification(img)
        return code

    def log_code_cptcha(self, status, code) -> None:
        if status is True:
            self.logger.info(f"验证码识别成功：{code}，提交正确")
        else:
            self.logger.info(f"验证码识别成功：{code}，提交错误，10S 后重试")

    def __handle_anti_spider(self) -> None:
        """处理风控验证码"""
        self.logger.info("开始处理验证码")
        for retry_times in range(self.__captcha_max_retry):
            self.logger.info(f"验证码处理第 {retry_times + 1} 次")
            self.__cb_resolve_captcha_after(retry_times + 1)
            # 这里过快可能导致 capthca 拉取识别，故延时
            time.sleep(5.0)
            captcha_img = self.__get_captcha_image()
            if captcha_img is None:
                continue
            captcha_code = self.identify_captcha(captcha_img)
            self.logger.info(f"验证码已识别 {captcha_code}")
            status = self.__submit_captcha(captcha_code)

            self.log_code_cptcha(status, captcha_code)
            if status is True:
                # 识别成功退出 retry
                break
            # 失败后进行 retry
            time.sleep(5.0)

    def get_special_type(self, resp):
        try:
            resp_url = URL(resp.url)
            if "Location" in resp.headers:
                self.SpecialPageType.NORMAL = True
                return True

            # 检查是否是JSON格式的验证码响应
            if resp.headers.get("Content-Type", "").startswith("application/json"):
                try:
                    json_data = resp.json()
                    if (
                        json_data.get("error") == "invalid_verify"
                        or "verify_path" in json_data
                    ):
                        self.logger.info(f"检测到JSON格式验证码响应: {json_data}")
                        self.SpecialPageType.CAPTCHA = True
                        return True
                except Exception as json_err:
                    self.logger.error(f"解析JSON响应时出错: {str(json_err)}")

            if resp_url.path.endswith("/antispiderShowVerify.ac"):
                self.SpecialPageType.CAPTCHA = True
                return True
            elif resp.headers.get("Content-Type", "").startswith("text/html"):
                html = BeautifulSoup(resp.text, "lxml")
                if html.select_one("body.grayBg script"):
                    if re.search(
                        r"var url = \S+ \+ _CP_ \+ \"/knowledge/startface",
                        html.select_one("body.grayBg script").text,
                    ):
                        self.SpecialPageType.FACE = True
                        return True
            self.SpecialPageType.NORMAL = True
            return True
        except Exception as e:
            self.logger.error(f"解析响应类型时出错: {str(e)}")
            self.SpecialPageType.NORMAL = True
            return True

    def get(
        self,
        url,
        params=None,
        headers=None,
        cookies=None,
        data=None,
        json=None,
        auth=None,
        timeout=5,
        proxies=None,
        verify=None,
        allow_redirects=None,
        _is_retry=False,  # 内部参数，用于标记是否是验证码处理后的重试请求
    ):
        # 禁用代理，直接使用空字典
        proxies = {}
        # 默认禁用SSL验证
        if verify is None:
            verify = False
        try:
            if url is None:
                return None
            if (
                "https://captcha.chaoxing.com/captcha/get/conf?callback=cx_captcha_function&captchaId="
                in url
            ):
                proxies = {}
            res = self.session.get(
                url,
                params=params,
                headers=headers,
                cookies=cookies,
                data=data,
                json=json,
                auth=auth,
                timeout=timeout,
                proxies=proxies,
                verify=verify,
                allow_redirects=allow_redirects,
            )
        except Exception as e:
            self.logger.error(f"GET请求异常: {str(e)}")
            traceback.print_exc()
            time.sleep(20)
            return self.get(
                url,
                params=params,
                headers=headers,
                cookies=cookies,
                data=data,
                json=json,
                auth=auth,
                timeout=timeout,
                verify=verify,
                allow_redirects=allow_redirects,
            )
        if self.get_special_type(res):
            # 如果是验证码页面且不是重试请求，则处理验证码
            if self.SpecialPageType.CAPTCHA and not _is_retry:
                self.logger.info(f"检测到验证码，开始处理...")
                self.__handle_anti_spider()
                # 递归重发请求，但标记为重试请求，避免再次处理验证码
                self.logger.info(f"验证码处理完成，重新发送请求...")
                res = self.get(
                    url,
                    params=params,
                    headers=headers,
                    cookies=cookies,
                    data=data,
                    json=json,
                    auth=auth,
                    timeout=timeout,
                    proxies=proxies,
                    verify=verify,
                    allow_redirects=allow_redirects,
                    _is_retry=True,  # 标记为重试请求
                )
                return res

            if self.SpecialPageType.NORMAL:
                # 正常响应
                self.close()
                return res

            # 其他情况也返回响应
            return res

    def post(
        self,
        url,
        params=None,
        headers=None,
        cookies=None,
        data=None,
        json=None,
        auth=None,
        timeout=5,
        proxies=None,
        verify=None,
        allow_redirects=None,
        _is_retry=False,  # 内部参数，用于标记是否是验证码处理后的重试请求
    ):
        # 禁用代理，直接使用空字典
        proxies = {}
        # 默认禁用SSL验证
        if verify is None:
            verify = False
        try:
            if url is None:
                return None
            res = self.session.post(
                url,
                params=params,
                headers=headers,
                cookies=cookies,
                data=data,
                json=json,
                auth=auth,
                timeout=timeout,
                proxies=proxies,
                verify=verify,
                allow_redirects=allow_redirects,
            )
        except Exception as e:
            self.logger.error(f"POST请求异常: {str(e)}")
            traceback.print_exc()
            time.sleep(60)
            return self.post(
                url,
                params=params,
                headers=headers,
                cookies=cookies,
                data=data,
                json=json,
                auth=auth,
                timeout=timeout,
                verify=verify,
                allow_redirects=allow_redirects,
            )
        if self.get_special_type(res):
            # 如果是验证码页面且不是重试请求，则处理验证码
            if self.SpecialPageType.CAPTCHA and not _is_retry:
                self.logger.info(f"检测到验证码，开始处理...")
                self.__handle_anti_spider()
                # 递归重发请求，但标记为重试请求，避免再次处理验证码
                self.logger.info(f"验证码处理完成，重新发送请求...")
                res = self.post(
                    url,
                    params=params,
                    headers=headers,
                    cookies=cookies,
                    data=data,
                    json=json,
                    auth=auth,
                    timeout=timeout,
                    proxies=proxies,
                    verify=verify,
                    allow_redirects=allow_redirects,
                    _is_retry=True,  # 标记为重试请求
                )
                return res

            if self.SpecialPageType.NORMAL:
                # 正常响应
                self.close()
                return res

            # 其他情况也返回响应
            return res

    def close(self):
        try:
            self.session.close()  # 提供关闭会话的方法
        except Exception as e:
            self.logger.error(f"关闭会话时出错: {str(e)}")
            pass

    def Uid(self):
        try:
            return self.session.cookies.get_dict()["_uid"]
        except KeyError:
            self.logger.error("获取用户ID失败: cookies中不存在_uid")
            return None
        except Exception as e:
            self.logger.error(f"获取用户ID失败: {str(e)}")
            return None
