# 学习通作业自动填充系统分析

## 1. 系统概述

本文档分析了针对超星学习通平台ID 9004的作业处理流程，主要包括两个核心文件：`batch_fill_homework.py`和`chaoxing_homework_manager.py`。这两个文件共同实现了自动化获取作业列表、提取作业题目、使用AI生成答案并提交的功能。

## 2. 系统流程

整个作业处理流程可以分为以下几个关键步骤：

1. 用户登录
2. 获取课程列表
3. 选择课程获取作业列表
4. 选择特定作业获取详情
5. 提取作业题目
6. 使用AI生成答案
7. 提交答案

## 3. 核心组件分析

### 3.1 登录功能 (ChaoxingHomeworkManager.login)

登录功能通过向超星学习通的登录API发送请求实现：

```python
def login(self):
    url = "https://passport2.chaoxing.com/fanyalogin"
    
    payload = {
        "fid": "-1",
        "uname": self.username,  # 加密的用户名
        "password": self.password,  # 加密的密码
        "refer": "https%3A%2F%2Fi.chaoxing.com",
        "t": "true",
        "forbidotherlogin": "0",
        "validate": "",
        "doubleFactorLogin": "0",
        "independentId": "0",
        "independentNameId": "0",
    }
    
    response = self.session.post(url, data=payload, headers=headers)
```

登录成功后，会保存会话cookies以维持登录状态。

### 3.2 获取课程列表 (ChaoxingHomeworkManager.get_course_list)

获取课程列表通过访问课程列表API并解析HTML响应实现：

```python
def get_course_list(self):
    url = "https://mooc2-ans.chaoxing.com/visit/courselistdata"
    
    payload = {
        "courseType": "1",  # 1表示学生课程
        "courseFolderId": "0",
        "query": "",
        "pageHeader": "",
        "single": "0",
        "superstarClass": "0",
    }
    
    response = self.session.post(url, data=payload, headers=headers)
    soup = BeautifulSoup(response.text, "html.parser")
    course_items = soup.select(".learnCourse")
```

从每个课程项中提取关键信息：
- 课程ID (courseId)
- 班级ID (clazzId)
- 个人ID (personId)
- 课程名称
- 教师名称

### 3.3 获取作业列表 (ChaoxingHomeworkManager.get_homework_list)

获取作业列表是关键步骤，需要构造正确的URL和参数：

```python
def get_homework_list(self, course_id, clazz_id, person_id):
    # 首先获取enc参数
    enc_url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}"
    enc_response = self.session.get(enc_url, headers=self.base_headers, verify=False)
    enc_soup = BeautifulSoup(enc_response.text, "html.parser")
    enc_input = enc_soup.select_one("#enc")
    enc = enc_input.get("value") if enc_input else "默认值"
    
    # 构建作业列表URL
    url = f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&enc={enc}&status=0&pageNum=1&topicId=0"
    response = self.session.get(url, headers=headers, verify=False)
```

从作业列表页面中提取每个作业的信息：
- 作业标题
- 作业状态（已完成、未交、待批阅等）
- 作业链接
- 作业ID (workId)
- 答案ID (answerId)

### 3.4 获取作业详情 (get_homework_detail)

获取作业详情通过访问作业链接并解析页面内容实现：

```python
def get_homework_detail(session, homework_link):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    }
    
    response = session.get(homework_link, headers=headers, verify=False)
    return response.text
```

### 3.5 提取表单数据和题目 (extract_form_data_and_questions)

从作业页面HTML中提取表单数据和题目信息：

```python
def extract_form_data_and_questions(html_content):
    soup = BeautifulSoup(html_content, "html.parser")
    
    # 提取基本表单字段
    course_id = soup.select_one("#courseId").get("value")
    class_id = soup.select_one("#classId").get("value")
    cpi = soup.select_one("#cpi").get("value")
    work_id = soup.select_one("#workId").get("value")
    answer_id = soup.select_one("#answerId").get("value")
    standard_enc = soup.select_one("#standardEnc").get("value") if soup.select_one("#standardEnc") else ""
    enc_work = soup.select_one("#enc_work").get("value")
    total_question_num = soup.select_one("#totalQuestionNum").get("value")
    
    # 提取题目ID列表和题目内容
    question_ids = []
    question_contents = {}
    question_items = soup.select(".questionLi")
    
    for item in question_items:
        question_id = item.get("data")
        if question_id:
            question_ids.append(question_id)
            
            # 获取题目标题
            title_elem = item.select_one(".mark_name")
            title = title_elem.get_text(strip=True) if title_elem else f"题目 {question_id}"
            
            # 存储题目信息
            question_contents[question_id] = title
```

### 3.6 AI答案生成 (get_ai_answer)

使用AI接口获取题目答案：

```python
def get_ai_answer(question):
    try:
        # 准备请求数据
        data = {
            "key": AI_API_KEY,
            "model": AI_MODEL,
            "question": question,
            "prompt": "你是一个专业的学习助手，请根据问题提供准确、简洁的答案，不要有多余的解释。",
        }
        
        # 发送请求
        response = requests.post(AI_API_URL, data=data, headers=headers, timeout=30)
        result = response.json()
        
        if result.get("code") == 1 and "answer" in result:
            return result["answer"]
    except Exception as e:
        print(f"调用AI接口时出错: {e}")
```

### 3.7 提交答案 (submit_homework_answers)

将生成的答案提交到学习通平台：

```python
def submit_homework_answers(session, form_data, question_ids, answers, homework_link):
    # 添加答案到表单数据
    for qid in question_ids:
        if qid in answers:
            form_data[f"answer{qid}"] = answers[qid]
    
    # 构建提交URL
    url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"
    url += f"?_classId={form_data['classId']}&courseid={form_data['courseId']}"
    url += f"&token={form_data['enc_work']}&totalQuestionNum={form_data['totalQuestionNum']}"
    url += "&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"
    
    response = session.post(url, data=form_data, headers=headers, verify=False)
    return response.text
```

## 4. 平台ID 9004特殊处理

在学习通跑单启动文件中，对平台ID 9004有特殊处理逻辑：

```python
# 平台ID 9004 特殊处理
if self.cid == 9004:
    try:
        logger.info(f"ID:{self.username},平台ID 9004，使用特殊处理方式获取作业信息")
        # 直接构造课程信息
        course_info = {
            "kcname": "作业任务",  # 默认课程名称
            "courseid": self.courseid,
            "clazzid": (self.courseid.split("_")[0] if "_" in self.courseid else self.courseid),
            "cpi": (self.courseid.split("_")[1] if "_" in self.courseid else ""),
        }
        self.KcList.append(course_info)
        logger.success(f"ID:{self.username},成功构造作业课程信息: {course_info}")
        return True
    except Exception as e:
        logger.error(f"ID:{self.username},构造作业课程信息失败: {str(e)}")
        return False
```

平台ID 9004的作业处理流程与常规作业不同，主要区别在于：

1. 课程信息的构造方式不同
2. 作业信息的解析方式不同
3. 支持多种格式的作业ID解析，包括：
   - 竖线分隔格式: `courseId|classId|cpi|workId|answerId|enc`
   - 下划线分隔格式: `workId_answerId_enc`

### 4.1 AssignmentTask类

专门用于处理平台ID 9004的作业任务：

```python
class AssignmentTask:
    def __init__(self, session, list_info, assignment_info, username):
        self.session = session
        self.username = username
        self.list_info = list_info
        self.courseid = list_info[0]["courseid"]
        self.clazzid = list_info[0]["clazzid"]
        self.kcname = list_info[0]["kcname"]
        self.cpi = list_info[0]["cpi"]
        
        # 解析作业信息
        self.workid = assignment_info.get("workId", "")
        self.answerId = assignment_info.get("answerId", "")
        self.enc = assignment_info.get("enc", "")
        self.knowledgeId = assignment_info.get("knowledgeId", "0")
        
        # 支持standardEnc参数
        if "standardEnc" in assignment_info:
            self.standardEnc = assignment_info.get("standardEnc", "")
```

### 4.2 作业页面处理 (StaratWorkTaks类)

处理作业页面的核心逻辑：

```python
def Html_Wkrk(self, html):
    self.html = BeautifulSoup(html, 'html.parser')
    if '已批阅' in self.html.find("title").text.strip() or '待批阅' in self.html.find("title").text.strip():
        logger.success(f"ID:{self.username},本章节已提交")
    else:
        try:
            # 提取表单参数
            self.workAnswerId = self.html.find("input", {"id": "workAnswerId"}).get("value")
            self.totalQuestionNum = self.html.find("input", {"id": "totalQuestionNum"}).get("value")
            self.old = self.html.find("input", {"id": "oldWorkId"}).get("value")
            self.workRelationId = self.html.find("input", {"id": "workRelationId"}).get("value")
            self.enc_work = self.html.find("input", {"id": "enc_work"}).get("value")
            
            # 查找题目并处理
            question_divs = self.html.findAll("div", {"class": "questionLi"})
            for quest in question_divs:
                # 处理题目...
```

## 5. 关键API接口

### 5.1 作业列表API
```
https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}&classId={clazz_id}&cpi={person_id}&enc={enc}&status=0&pageNum=1&topicId=0
```

### 5.2 作业详情API
```
https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork?courseId={course_id}&classId={clazz_id}&cpi={cpi}&workId={work_id}&answerId={answer_id}&enc={enc}
```
或
```
https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId={course_id}&classId={clazz_id}&cpi={cpi}&workId={work_id}&answerId={answer_id}&enc={enc}
```

### 5.3 提交答案API
```
https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb?_classId={class_id}&courseid={course_id}&token={enc_work}&totalQuestionNum={total_question_num}&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1
```

## 6. 总结

针对超星学习通平台ID 9004的作业处理系统主要通过以下步骤工作：

1. 登录获取会话
2. 获取课程列表
3. 获取作业列表
4. 解析作业链接获取作业ID、答案ID和enc参数
5. 获取作业详情页面
6. 提取作业题目和表单数据
7. 使用AI生成答案
8. 构造提交数据
9. 提交答案

系统支持多种格式的作业ID解析，并针对不同的页面结构进行了适配处理，确保能够正确提取题目和提交答案。 