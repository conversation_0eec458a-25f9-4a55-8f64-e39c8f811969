import re
import traceback
from collections import OrderedDict

from loguru import logger
import requests
import urllib3


def contains_uppercase_letters(s):
    # 使用正则表达式匹配大写字母 A-Z
    pattern = r"[A-Z0-9]"
    # re.search() 方法查找字符串中是否存在匹配的字符
    return bool(re.search(pattern, s))


def remove_digits_and_symbols(text):
    # 使用正则表达式去掉所有的数字 (0-9) 和顿号 (、)
    modified_text = re.sub(r"[0-9、]", "", text)
    modified_text = re.sub(r"\s+", " ", modified_text).strip()
    return modified_text


def replace_src(a):
    return a.group(1)


def tiankong_quchong(text):
    if contains_uppercase_letters(text):
        return text
    else:
        items = text.split("###")
        # 使用 OrderedDict 去重并保持顺序
        unique_items = list(OrderedDict.fromkeys(item.strip() for item in items))
        # 将去重后的部分重新组合，使用 '###' 隔开
        result = "###".join(unique_items)

        return result


# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def questionbank(kcname, question):
    try:
        r = requests.get(
            f"http://l2.bb1a.cn:47407/api/v3/tk/answer?kcname={kcname}&question={question}"
        ).json()
        if r["code"] == 1:
            return r["data"], 1
        return questionbank2(question)
    except:
        return questionbank2(question)


def questionbank2(question):
    session = requests.session()
    try:
        r = requests.get(f"http://yx.yunxue.icu/api?token=admin&q={question}").json()
        if r["code"] == 1:  # 修正缩进
            return r["data"], 1
        return None, 2
    except:
        return None, 2

def fujia(question):
    """
    辅助题库查询函数
    """
    try:
        # 这里可以添加其他题库API，目前使用与questionbank2相同的逻辑
        r = requests.get(f"http://yx.yunxue.icu/api?token=admin&q={question}").json()
        if r["code"] == 1:
            return r["data"], 2
        return None, 3
    except:
        logger.debug(f"辅助题库查询失败: {question}")
        return None, 3
