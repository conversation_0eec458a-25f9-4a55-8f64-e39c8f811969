#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试平台ID9006判断题语义匹配修复效果
基于用户反馈的AI答题选项匹配问题
"""

def test_semantic_matching_logic():
    """测试语义匹配逻辑"""
    print("=== 测试判断题语义匹配逻辑 ===")
    
    # 模拟测试数据
    test_cases = [
        {
            "ai_answer": "正确",
            "options": [("A", "对"), ("false", "错")],
            "expected": ("A", "对"),
            "description": "AI回复'正确'应匹配到'对'选项"
        },
        {
            "ai_answer": "错误",
            "options": [("A", "对"), ("false", "错")],
            "expected": ("false", "错"),
            "description": "AI回复'错误'应匹配到'错'选项"
        },
        {
            "ai_answer": "对",
            "options": [("A", "对"), ("B", "错")],
            "expected": ("A", "对"),
            "description": "AI回复'对'应匹配到'对'选项"
        },
        {
            "ai_answer": "错",
            "options": [("A", "对"), ("B", "错")],
            "expected": ("B", "错"),
            "description": "AI回复'错'应匹配到'错'选项"
        },
        {
            "ai_answer": "是",
            "options": [("true", "对"), ("false", "错")],
            "expected": ("true", "对"),
            "description": "AI回复'是'应匹配到'对'选项"
        },
        {
            "ai_answer": "否",
            "options": [("true", "对"), ("false", "错")],
            "expected": ("false", "错"),
            "description": "AI回复'否'应匹配到'错'选项"
        }
    ]
    
    # 语义匹配逻辑（从修复代码中提取）
    def semantic_match(ai_answer, options):
        positive_keywords = ["正确", "对", "是", "T", "True", "true", "yes", "√", "正确答案"]
        negative_keywords = ["错误", "错", "否", "F", "False", "false", "no", "×", "错误答案"]
        
        # 判断AI回复的语义
        is_positive = False
        is_negative = False
        
        # 检查正面关键词
        for keyword in positive_keywords:
            if keyword in ai_answer:
                # 对于"对"字符，需要更精确的匹配，避免"不对"被误判
                if keyword == "对" and ("不对" in ai_answer or "错对" in ai_answer):
                    continue
                is_positive = True
                break
        
        # 检查负面关键词
        if not is_positive:
            for keyword in negative_keywords:
                if keyword in ai_answer:
                    is_negative = True
                    break
        
        # 根据语义匹配选项
        if is_positive or is_negative:
            for x, a in options:
                option_text = a.lower().strip()
                # 匹配正确选项
                if is_positive and (option_text in ["对", "正确", "是", "t", "true", "yes"] or "对" in option_text):
                    return (x, a)
                # 匹配错误选项
                elif is_negative and (option_text in ["错", "错误", "否", "f", "false", "no"] or "错" in option_text):
                    return (x, a)
        
        return None
    
    # 执行测试
    passed = 0
    total = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {case['description']}")
        print(f"AI回复: '{case['ai_answer']}'")
        print(f"选项: {case['options']}")
        
        result = semantic_match(case['ai_answer'], case['options'])
        
        if result == case['expected']:
            print(f"✅ 通过 - 匹配结果: {result}")
            passed += 1
        else:
            print(f"❌ 失败 - 期望: {case['expected']}, 实际: {result}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    return passed == total

def test_edge_cases():
    """测试边缘情况"""
    print("\n=== 测试边缘情况 ===")
    
    edge_cases = [
        {
            "ai_answer": "不对",
            "options": [("A", "对"), ("B", "错")],
            "expected": None,  # 应该不匹配"对"，因为是"不对"
            "description": "AI回复'不对'不应匹配到'对'选项"
        },
        {
            "ai_answer": "错对",
            "options": [("A", "对"), ("B", "错")],
            "expected": None,  # 应该不匹配"对"，因为包含"错对"
            "description": "AI回复'错对'不应匹配到'对'选项"
        },
        {
            "ai_answer": "True",
            "options": [("A", "对"), ("B", "错")],
            "expected": ("A", "对"),
            "description": "AI回复'True'应匹配到'对'选项"
        },
        {
            "ai_answer": "False",
            "options": [("A", "对"), ("B", "错")],
            "expected": ("B", "错"),
            "description": "AI回复'False'应匹配到'错'选项"
        }
    ]
    
    # 使用相同的语义匹配逻辑
    def semantic_match(ai_answer, options):
        positive_keywords = ["正确", "对", "是", "T", "True", "true", "yes", "√", "正确答案"]
        negative_keywords = ["错误", "错", "否", "F", "False", "false", "no", "×", "错误答案"]
        
        is_positive = False
        is_negative = False
        
        for keyword in positive_keywords:
            if keyword in ai_answer:
                if keyword == "对" and ("不对" in ai_answer or "错对" in ai_answer):
                    continue
                is_positive = True
                break
        
        if not is_positive:
            for keyword in negative_keywords:
                if keyword in ai_answer:
                    is_negative = True
                    break
        
        if is_positive or is_negative:
            for x, a in options:
                option_text = a.lower().strip()
                if is_positive and (option_text in ["对", "正确", "是", "t", "true", "yes"] or "对" in option_text):
                    return (x, a)
                elif is_negative and (option_text in ["错", "错误", "否", "f", "false", "no"] or "错" in option_text):
                    return (x, a)
        
        return None
    
    passed = 0
    total = len(edge_cases)
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n边缘测试 {i}: {case['description']}")
        print(f"AI回复: '{case['ai_answer']}'")
        print(f"选项: {case['options']}")
        
        result = semantic_match(case['ai_answer'], case['options'])
        
        if result == case['expected']:
            print(f"✅ 通过 - 匹配结果: {result}")
            passed += 1
        else:
            print(f"❌ 失败 - 期望: {case['expected']}, 实际: {result}")
    
    print(f"\n边缘测试结果: {passed}/{total} 通过")
    return passed == total

def test_real_scenario():
    """测试真实场景"""
    print("\n=== 测试真实场景 ===")
    
    print("基于用户提供的日志场景:")
    print("题目: 在WEB3.0时代,用户是核心资产,而用户产生的数据更是用户和平台的核心资产。")
    print("选项: A. 对  B. 错")
    print("AI回复: 正确")
    print("期望匹配: A. 对")
    
    # 模拟真实场景
    ai_answer = "正确"
    options = [("A", "对"), ("B", "错")]
    
    # 语义匹配
    def semantic_match(ai_answer, options):
        positive_keywords = ["正确", "对", "是", "T", "True", "true", "yes", "√", "正确答案"]
        negative_keywords = ["错误", "错", "否", "F", "False", "false", "no", "×", "错误答案"]
        
        is_positive = False
        is_negative = False
        
        for keyword in positive_keywords:
            if keyword in ai_answer:
                if keyword == "对" and ("不对" in ai_answer or "错对" in ai_answer):
                    continue
                is_positive = True
                break
        
        if not is_positive:
            for keyword in negative_keywords:
                if keyword in ai_answer:
                    is_negative = True
                    break
        
        if is_positive or is_negative:
            for x, a in options:
                option_text = a.lower().strip()
                if is_positive and (option_text in ["对", "正确", "是", "t", "true", "yes"] or "对" in option_text):
                    return (x, a)
                elif is_negative and (option_text in ["错", "错误", "否", "f", "false", "no"] or "错" in option_text):
                    return (x, a)
        
        return None
    
    result = semantic_match(ai_answer, options)
    expected = ("A", "对")
    
    if result == expected:
        print(f"✅ 真实场景测试通过!")
        print(f"AI回复'{ai_answer}'成功匹配到选项{result[0]}('{result[1]}')")
        print("现在系统会提交正确的答案选项!")
        return True
    else:
        print(f"❌ 真实场景测试失败")
        print(f"期望: {expected}, 实际: {result}")
        return False

def verify_code_changes():
    """验证代码修改"""
    print("\n=== 验证代码修改 ===")
    
    try:
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            "平台ID9006的判断题特殊处理：语义匹配",
            "positive_keywords = [\"正确\", \"对\", \"是\"",
            "negative_keywords = [\"错误\", \"错\", \"否\"",
            "判断题语义匹配逻辑",
            "hasattr(self.session, 'cid') and self.session.cid == 9006 and self.qtp == 3"
        ]
        
        all_checks_passed = True
        for check in checks:
            if check in content:
                print(f"✅ {check}")
            else:
                print(f"❌ 缺少: {check}")
                all_checks_passed = False
        
        if all_checks_passed:
            print("✅ 所有代码修改已正确应用")
        else:
            print("❌ 代码修改不完整")
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ 验证代码修改时出错: {e}")
        return False

def test_platform_isolation():
    """测试平台隔离"""
    print("\n=== 测试平台隔离 ===")
    
    print("验证修改只影响平台ID9006:")
    
    try:
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查平台隔离条件
        isolation_checks = [
            "hasattr(self.session, 'cid') and self.session.cid == 9006 and self.qtp == 3",
            "# 平台ID9006的判断题特殊处理",
            "continue  # 语义匹配完成，跳过文本相似度匹配"
        ]
        
        all_checks_passed = True
        for check in isolation_checks:
            if check in content:
                print(f"✅ 平台隔离检查: {check}")
            else:
                print(f"❌ 缺少平台隔离: {check}")
                all_checks_passed = False
        
        if all_checks_passed:
            print("✅ 平台隔离正确实现，不会影响其他平台")
        else:
            print("❌ 平台隔离实现不完整")
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ 测试平台隔离时出错: {e}")
        return False

if __name__ == "__main__":
    print("平台ID9006判断题语义匹配修复测试")
    print("=" * 50)
    
    results = []
    
    # 测试语义匹配逻辑
    results.append(test_semantic_matching_logic())
    
    # 测试边缘情况
    results.append(test_edge_cases())
    
    # 测试真实场景
    results.append(test_real_scenario())
    
    # 验证代码修改
    results.append(verify_code_changes())
    
    # 测试平台隔离
    results.append(test_platform_isolation())
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    test_names = [
        "语义匹配逻辑测试",
        "边缘情况测试",
        "真实场景测试",
        "代码修改验证",
        "平台隔离测试"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过，判断题语义匹配修复成功！")
        print("\n关键修复总结:")
        print("1. ✅ 为平台ID9006添加了判断题语义匹配逻辑")
        print("2. ✅ AI回复'正确'现在能正确匹配到'对'选项")
        print("3. ✅ AI回复'错误'现在能正确匹配到'错'选项")
        print("4. ✅ 支持多种语义关键词（正确/对/是 vs 错误/错/否）")
        print("5. ✅ 精确处理边缘情况（如'不对'不会匹配'对'）")
        print("6. ✅ 保持平台隔离，不影响其他平台")
        print("\n现在用户反馈的问题应该完全解决了！")
        print("AI回复'正确'时，会被正确匹配到'A. 对'选项并成功提交。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
