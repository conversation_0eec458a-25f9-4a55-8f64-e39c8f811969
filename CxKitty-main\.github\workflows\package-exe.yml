name: Package Executable

on:
  release:
    types: [ published ]
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        os: [ macos-latest, ubuntu-latest, windows-latest ]

    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
            python-version: '3.10'
            architecture: 'x64'
            cache: 'pip'
      - name: Pip Install
        run: python -m pip install --upgrade pip
      - name: Poetry Install
        run: |
          pip install poetry
          poetry config virtualenvs.in-project true --local
          poetry install
      - name: Install Pyinstaller
        run: poetry add pyinstaller
      - name: Create Executable File (Windows)
        if: runner.os == 'Windows'
        run: poetry run pyinstaller -F "main.py" --clean --dist ./out --name "CxKitty"  --add-data ".venv\Lib\site-packages\onnxruntime\capi\onnxruntime_providers_shared.dll;onnxruntime\capi"  --add-data ".venv\Lib\site-packages\ddddocr\common.onnx;ddddocr" --add-data ".venv\Lib\site-packages\ddddocr\common_det.onnx;ddddocr" --add-data ".venv\Lib\site-packages\ddddocr\common_old.onnx;ddddocr"
      - name: Create Executable File (Linux)
        if: runner.os == 'Linux'
        run: poetry run pyinstaller -F "main.py" --clean --dist ./out --name "CxKitty"  --add-data ".venv/lib/python3.10/site-packages/onnxruntime/capi/libonnxruntime_providers_shared.so:onnxruntime\capi"  --add-data ".venv/lib/python3.10/site-packages/ddddocr/common.onnx:ddddocr" --add-data ".venv/lib/python3.10/site-packages/ddddocr/common_det.onnx:ddddocr" --add-data ".venv/lib/python3.10/site-packages/ddddocr/common_old.onnx:ddddocr"
      - name: Create Executable File (macOS)
        if: runner.os == 'macOS'
        run: poetry run pyinstaller -F "main.py" --clean --dist ./out --name "CxKitty" --add-data ".venv/lib/python3.10/site-packages/ddddocr/common.onnx:ddddocr" --add-data ".venv/lib/python3.10/site-packages/ddddocr/common_det.onnx:ddddocr" --add-data ".venv/lib/python3.10/site-packages/ddddocr/common_old.onnx:ddddocr"


      - name: Copy Required Files
        run: |
          cp config.yml out/
          cp pyproject.toml out/
      - name: Upload Artifacts
        uses: actions/upload-artifact@v3
        with:
          name: CxKitty For ${{ runner.os }} x64
          path: |
            out
