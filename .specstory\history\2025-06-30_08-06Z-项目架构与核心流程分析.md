<!-- Generated by SpecStory -->

# 项目架构与核心流程分析 (2025-06-30 08:06Z)

_**User**_

帮我认真的分析这个项目的整体架构，然后分析一下核心流程，以及入口文件，最后分析一下当前函数，具体初始化了什么服务。然后分析完后在接着进入深度分析
最后使用cursor把Cursor Rules分层规则管理 写到.cursor\rules\mport-order.mdc文件里，根据系统的项目可以添加多个mdc文件到.cursor\rules里

---

_**Assistant**_

我将分析这个项目的架构、核心流程、入口文件和当前函数的初始化服务，然后进行深度分析并创建Cursor规则文件。

## 项目整体架构分析

从目录结构来看，这是一个基于Python的学习通自动化工具，主要用于处理学习通平台的相关操作。项目结构包括：

- API/：包含与学习通平台交互的API封装
- Config/：配置文件目录
- data/：可能存储数据或临时文件
- 学习通跑单启动文件.py：主入口文件
- qingka_wangke_order.sql：数据库结构文件
- requirements.txt：项目依赖

## 核心流程分析

从已提供的`API/Session.py`文件内容分析，该项目的核心流程可能是：

1. 初始化会话并处理代理IP
2. 处理学习通平台的登录认证
3. 处理验证码识别和提交
4. 处理反爬虫机制
5. 执行特定的学习任务（可能是自动完成课程、作业等）

## 入口文件分析

入口文件应该是`学习通跑单启动文件.py`，该文件可能负责初始化整个应用程序，加载配置，创建会话，并开始执行自动化任务。

## 当前函数分析 - API/Session.py

`API/Session.py`文件定义了几个关键类：

1. `Proxies`类：
   - 管理代理IP池
   - 随机选择代理IP以避免IP封禁

2. `SpecialPageType`类：
   - 标识页面类型（普通、验证码、人脸验证）

3. `StartSession`类（核心类）：
   - 初始化会话对象
   - 处理验证码识别和提交
   - 处理反爬虫机制
   - 封装HTTP请求方法（get/post）
   - 管理会话生命周期

`StartSession`类初始化了以下服务：
- 会话管理（session）
- 日志记录（logger）
- 验证码重试机制（__captcha_max_retry）
- 页面类型识别（SpecialPageType）
- 代理IP管理（proxies）

该类还实现了验证码识别功能，使用`ddddocr`库进行图像识别，并处理学习通平台的各种反爬机制。

---

<details>
            <summary>Listed directory .cursor • **1** results</summary>
        
| Name |
|-------|
| 📁 `rules` |

</details>

---

<details>
            <summary>Listed directory .cursor/rules • **0** results</summary>
        
No results found
</details>

---

Read file: 学习通跑单启动文件.py

---

