# -*- coding: utf-8 -*-
"""
诊断当前代码状态，检查修复是否真正生效
"""

import sys
import traceback
from loguru import logger

def check_questionbank_fix():
    """检查questionbank函数修复状态"""
    try:
        logger.info("=== 检查questionbank函数修复状态 ===")
        
        with open("API/Questionbank.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查修复
        if "return None, 1  # 返回qnum=1以触发备用题库查询" in content:
            logger.info("✓ questionbank函数返回值已修复")
            return True
        elif "return None, 0" in content:
            logger.error("✗ questionbank函数仍然返回None, 0")
            return False
        else:
            logger.warning("? questionbank函数返回值状态不明")
            return False
        
    except Exception as e:
        logger.error(f"检查questionbank函数修复状态失败: {str(e)}")
        return False

def check_fill_blank_fix():
    """检查填空题修复状态"""
    try:
        logger.info("=== 检查填空题修复状态 ===")
        
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键修复
        checks = [
            {"check": "elif self.qtp == 2:", "description": "填空题处理分支"},
            {"check": "主题库填空题答案", "description": "主题库成功日志"},
            {"check": "主题库未找到填空题答案，尝试备用题库", "description": "备用题库尝试日志"},
            {"check": "AI生成填空题答案", "description": "AI答题成功日志"}
        ]
        
        all_passed = True
        for check in checks:
            if check["check"] in content:
                logger.debug(f"✓ {check['description']}")
            else:
                logger.error(f"✗ 缺少{check['description']}")
                all_passed = False
        
        if all_passed:
            logger.info("✓ 填空题修复完整")
        else:
            logger.error("✗ 填空题修复不完整")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"检查填空题修复状态失败: {str(e)}")
        return False

def check_exam_details_extraction():
    """检查考试详细信息提取功能"""
    try:
        logger.info("=== 检查考试详细信息提取功能 ===")
        
        with open("学习通跑单启动文件.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查新函数
        if "def extract_exam_details(html_content):" in content:
            logger.info("✓ extract_exam_details函数存在")
        else:
            logger.error("✗ extract_exam_details函数不存在")
            return False
        
        # 检查调用
        if "extract_exam_details(result_response.text)" in content:
            logger.info("✓ extract_exam_details函数被调用")
        else:
            logger.error("✗ extract_exam_details函数未被调用")
            return False
        
        # 检查remarks格式
        if "作答情况: {answer_status};{retake_info}" in content:
            logger.info("✓ remarks格式已更新")
        else:
            logger.error("✗ remarks格式未更新")
            return False
        
        logger.info("✓ 考试详细信息提取功能完整")
        return True
        
    except Exception as e:
        logger.error(f"检查考试详细信息提取功能失败: {str(e)}")
        return False

def analyze_user_logs():
    """分析用户日志，找出问题"""
    try:
        logger.info("=== 分析用户日志 ===")
        
        # 用户报告的问题
        issues = [
            {
                "issue": "AI答题fallback没有触发",
                "log": "所有题库均未找到答案 -> answer:None",
                "expected": "应该触发AI答题"
            },
            {
                "issue": "填空题答案没有填充",
                "log": "主题库查询成功: 数字化 -> answer:None",
                "expected": "应该填充答案"
            },
            {
                "issue": "作答情况和重考机会信息不完整",
                "log": "作答情况: 未知/未知, 重考机会: 未知次重考机会",
                "expected": "应该提取到具体信息"
            }
        ]
        
        logger.info("用户报告的问题:")
        for i, issue in enumerate(issues, 1):
            logger.info(f"{i}. {issue['issue']}")
            logger.info(f"   实际: {issue['log']}")
            logger.info(f"   期望: {issue['expected']}")
        
        # 可能的原因
        logger.info("\n可能的原因:")
        logger.info("1. 用户运行的代码版本不是最新的")
        logger.info("2. 修复有遗漏或错误")
        logger.info("3. 运行环境或配置问题")
        logger.info("4. 代码逻辑有bug")
        
        return True
        
    except Exception as e:
        logger.error(f"分析用户日志失败: {str(e)}")
        return False

def check_ai_answer_conditions():
    """检查AI答题条件"""
    try:
        logger.info("=== 检查AI答题条件 ===")
        
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查AI答题条件
        conditions = [
            {"check": "hasattr(self.session, 'cid') and self.session.cid == 9006", "description": "平台ID检查"},
            {"check": "HOMEWORK_AI_AVAILABLE", "description": "AI可用性检查"},
            {"check": "elif self.qnum == 4:", "description": "qnum=4触发条件"}
        ]
        
        all_passed = True
        for condition in conditions:
            if condition["check"] in content:
                logger.debug(f"✓ {condition['description']}")
            else:
                logger.error(f"✗ 缺少{condition['description']}")
                all_passed = False
        
        if all_passed:
            logger.info("✓ AI答题条件完整")
        else:
            logger.error("✗ AI答题条件不完整")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"检查AI答题条件失败: {str(e)}")
        return False

def suggest_solutions():
    """建议解决方案"""
    try:
        logger.info("=== 建议解决方案 ===")
        
        solutions = [
            "1. 确认用户运行的是最新版本的代码",
            "2. 检查代码修复是否真正生效",
            "3. 改进作答情况和重考机会信息的提取逻辑",
            "4. 添加更多调试日志来跟踪问题",
            "5. 创建一个强制更新脚本来确保修复生效"
        ]
        
        for solution in solutions:
            logger.info(solution)
        
        return True
        
    except Exception as e:
        logger.error(f"建议解决方案失败: {str(e)}")
        return False

def main():
    """主诊断函数"""
    logger.info("=" * 60)
    logger.info("诊断当前代码状态")
    logger.info("=" * 60)
    logger.info("目标: 检查修复是否真正生效，分析用户问题")
    
    tests = [
        ("questionbank函数修复状态检查", check_questionbank_fix),
        ("填空题修复状态检查", check_fill_blank_fix),
        ("考试详细信息提取功能检查", check_exam_details_extraction),
        ("AI答题条件检查", check_ai_answer_conditions),
        ("用户日志分析", analyze_user_logs),
        ("解决方案建议", suggest_solutions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                logger.warning(f"{test_name}: 检查失败")
        except Exception as e:
            logger.error(f"{test_name}: 异常 - {str(e)}")
            traceback.print_exc()
    
    logger.info("\n" + "=" * 60)
    logger.info(f"诊断结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.success("🎉 所有检查通过，代码状态正常")
        logger.info("问题可能在于用户运行的代码版本或环境配置")
    else:
        logger.warning(f"⚠ 有{total-passed}个检查失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    main()
