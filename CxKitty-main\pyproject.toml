[tool.poetry]
name = "CxKitty"
version = "0.4.5"
description = "超星学习通答题姬. 媒体播放、章节测验、课程考试, 集成式自动化工具"
license = "GPL-3.0"
authors = ["SocialSisterYi <<EMAIL>>"]
readme = "README.md"
repository = "https://github.com/SocialSisterYi/CxKitty"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
requests = "^2.28.1"
qrcode = "^7.3.1"
rich = "^12.6.0"
pycryptodome = "^3.15.0"
lxml = "^4.9.1"
pyyaml = "^6.0"
jsonpath-python = "^1.0.6"
beautifulsoup4 = "^4.11.1"
ddddocr = "^1.4.10"
numpy = "^1.24.3"
opencv-python = "^********"
dataclasses-json = "^0.5.7"
yarl = "^1.9.2"


[tool.poetry.group.dev.dependencies]
black = "^22.12.0"
isort = "^5.12.0"

[tool.black]
line-length = 100

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
