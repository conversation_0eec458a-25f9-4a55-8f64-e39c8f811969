import difflib
import re
from collections import OrderedDict
from loguru import logger
from API.Re import strip_options


def match_answer(quest, answers, qtp, username, bidui=0.95):
    """
    增强版答案匹配函数，支持多种HTML结构

    参数:
    quest - BeautifulSoup对象，包含题目HTML
    answers - 题库中的答案
    qtp - 题目类型
    username - 用户名，用于日志
    bidui - 匹配阈值，默认0.95

    返回:
    (选项列表, 答案文本)
    """
    Xlist = []  # 选择的选项
    Alist = []  # 匹配到的答案文本

    if answers is None:
        return ["A"], "答案匹配失败"

    # 检查是否有正确答案标记（已提交过的作业）
    correct_answer_elem = quest.select_one("i.fontWeight.custom-style")
    if correct_answer_elem and "正确答案" in correct_answer_elem.text:
        # 尝试从正确答案标记后面获取答案
        try:
            correct_answer_text = correct_answer_elem.parent.text.strip()
            correct_answer_match = re.search(
                r"正确答案：\s*([A-Z]+)", correct_answer_text
            )
            if correct_answer_match:
                correct_options = correct_answer_match.group(1)
                logger.success(f"ID:{username},找到正确答案标记: {correct_options}")
                return list(correct_options), "已提交过的作业"
        except Exception as e:
            logger.warning(f"ID:{username},解析正确答案标记失败: {str(e)}")

    # 尝试多种可能的选项容器
    option_containers = [
        quest.find("div", {"class": "stem_answer"}),
        quest.find("ul", {"class": "Zy_ulTop"}),
        quest.find("div", {"class": "clearfix answerBg"}),
        quest.find("div", {"class": "Py_answer clearfix"}),
        quest.find("div", {"class": "Py_tk"}),
        quest.find("div", {"class": "answer_p"}),
    ]

    option_container = None
    for container in option_containers:
        if container:
            option_container = container
            break

    # 如果仍然找不到选项容器，尝试更广泛的选择器
    if not option_container:
        # 尝试查找包含选项的任何元素
        for selector in ["li", "div.fl.answer_p", "div.fl.answer", "div.answer"]:
            elements = quest.select(selector)
            if elements:
                # 如果找到了可能的选项元素，使用其父元素作为容器
                option_container = elements[0].parent
                break

    if not option_container:
        logger.warning(f"ID:{username},未找到选项容器")
        return ["A"], "答案匹配失败"

    # 尝试多种可能的选项元素
    option_elements = []
    for selector in [
        "div.clearfix.answerBg",
        "li",
        "div.fl.answer_p",
        "div.fl.answer",
        "div.answer",
    ]:
        elements = option_container.select(selector)
        if elements:
            option_elements = elements
            break

    # 如果仍然找不到选项元素，尝试直接使用选项容器的子元素
    if not option_elements and option_container:
        option_elements = option_container.find_all(["li", "div"], recursive=False)

    if not option_elements:
        logger.warning(f"ID:{username},未找到选项元素")
        return ["A"], "答案匹配失败"

    # 收集所有选项信息，用于后续匹配
    all_options = {}

    # 处理每个选项
    for option_elem in option_elements:
        try:
            # 尝试多种方式获取选项标识
            option_id = None
            if option_elem.find("span") and option_elem.find("span").get("data"):
                option_id = option_elem.find("span").get("data")
            elif option_elem.get("data"):
                option_id = option_elem.get("data")
            elif option_elem.find("input") and option_elem.find("input").get("value"):
                option_id = option_elem.find("input").get("value")
            # 尝试从class或id属性中提取选项标识
            elif option_elem.get("class") and any(
                "opt" in c.lower() for c in option_elem.get("class")
            ):
                class_name = [
                    c for c in option_elem.get("class") if "opt" in c.lower()
                ][0]
                option_id = re.search(r"[A-Z]", class_name.upper())
                if option_id:
                    option_id = option_id.group(0)
            # 尝试从文本中提取选项标识
            elif option_elem.text and re.match(
                r"^[A-Z][\s\.、]", option_elem.text.strip()
            ):
                option_id = option_elem.text.strip()[0]

            if not option_id:
                continue

            # 尝试多种方式获取选项文本
            option_text = ""
            text_elem = (
                option_elem.find("div", {"class": "fl answer_p"})
                or option_elem.find("div")
                or option_elem
            )
            if text_elem:
                option_text = strip_options(text_elem.text.strip())

            # 处理选项中的图片
            img_elements = option_elem.find_all("img", src=True)
            if img_elements:
                option_text += "".join(
                    [img.get("src") for img in img_elements if img.get("src")]
                )

            # 保存选项信息
            all_options[option_id] = option_text

        except Exception as e:
            logger.warning(f"ID:{username},处理选项时出错: {str(e)}")
            continue

    # 如果没有找到任何选项，返回匹配失败
    if not all_options:
        logger.warning(f"ID:{username},未找到任何有效选项")
        return ["A"], "答案匹配失败"

    # 记录找到的所有选项
    logger.debug(f"ID:{username},找到选项: {all_options}")

    # 尝试直接匹配选项字母
    letter_match = re.search(r"[A-Z]", answers)
    if letter_match:
        answer_letter = letter_match.group(0)
        logger.success(f"ID:{username},答案文本直接包含选项字母: {answer_letter}")
        return [answer_letter], f"直接匹配选项字母: {answer_letter}"

    # 尝试匹配选项内容
    for ans in answers.split("###"):
        # 对于单选题
        if qtp == 0:
            best_match = None
            best_score = 0

            for option_id, option_text in all_options.items():
                # 计算答案与选项的相似度
                similarity = difflib.SequenceMatcher(None, option_text, ans).ratio()
                logger.debug(f"ID:{username},选项 {option_id} 相似度: {similarity}")

                if similarity > best_score:
                    best_score = similarity
                    best_match = option_id

            # 如果找到最佳匹配且相似度超过阈值
            if best_match and best_score >= bidui:
                logger.success(
                    f"ID:{username},找到最佳匹配选项: {best_match}, 相似度: {best_score}"
                )
                return [best_match], all_options[best_match]

        # 对于多选题
        elif qtp == 1:
            for option_id, option_text in all_options.items():
                # 计算答案与选项的相似度
                similarity = difflib.SequenceMatcher(None, option_text, ans).ratio()

                if similarity >= bidui:
                    Xlist.append(option_id)
                    Alist.append(option_text)
                    break

    # 如果是多选题且找到了答案
    if qtp == 1 and Xlist:
        return Xlist, ", ".join(Alist)

    # 如果没有匹配到答案，尝试降低匹配阈值
    if not Xlist and bidui > 0.7:
        return match_answer(quest, answers, qtp, username, bidui - 0.1)

    # 最终没有匹配到答案，返回默认值
    return ["A"], "答案匹配失败"


def sort_answers(answer_list):
    """
    对答案选项进行排序

    参数:
    answer_list - 答案选项列表

    返回:
    排序后的答案字符串
    """
    order = {"A": 1, "B": 2, "C": 3, "D": 4, "E": 5, "F": 6, "G": 7, "H": 8}

    # 确保答案是列表
    if isinstance(answer_list, str):
        answer_list = list(answer_list)

    # 排序
    sorted_answers = sorted(answer_list, key=lambda x: order.get(x, 99))

    # 去重
    unique_answers = []
    for ans in sorted_answers:
        if ans not in unique_answers and ans in order:
            unique_answers.append(ans)

    return "".join(unique_answers) if unique_answers else "A"
