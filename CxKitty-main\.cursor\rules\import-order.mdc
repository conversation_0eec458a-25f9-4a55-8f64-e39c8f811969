# Import Order

## Groups
- Standard library imports
- Third-party library imports
- Local application imports

## Order
1. Standard library imports (e.g., `import sys`, `from os import PathLike`)
2. Third-party library imports (e.g., `import requests`, `from bs4 import BeautifulSoup`)
3. Local application imports (e.g., `import config`, `from cxapi import ChaoXingAPI`)

## Format
- Imports should be grouped by type with a blank line between groups
- Within each group, imports should be sorted alphabetically
- `from` imports should come after direct imports
- Multiple imports from the same module should be on a single line

## Example
```python
import json
import sys
import time
from os import PathLike

import requests
from bs4 import BeautifulSoup
from rich.console import Console

import config
from cxapi import ChaoXingAPI
from logger import Logger
```
description:
globs:
alwaysApply: false
---
