#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试平台ID9006的最终修复效果 - 更新版本
基于用户最新要求：30秒超时、简化AI输出、只显示题目和答案
"""

import requests
import json
from urllib.parse import urlencode

def test_tiku_query_with_options():
    """测试题库查询接口（包含选项）"""
    print("=== 测试题库查询接口（包含选项） ===")
    
    # 模拟多选题（包含选项）
    question_with_options = """(多选题) 常见的店铺推广渠道有 ()。

A. 社交平台推广
B. 直播推广
C. 矩阵推广
D. 利用交流活动推广
"""
    
    url = "http://tk.mixuelo.cc/api.php?act=query"
    
    data = {
        "key": "zXPX828s29Kk7Yj2",
        "question": question_with_options,
        "type": 1,  # 多选题
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json, text/javascript, */*; q=0.01",
    }
    
    print(f"发送题库查询请求...")
    print(f"题目: {question_with_options[:50]}...")
    
    try:
        response = requests.post(url, data=data, headers=headers, timeout=30)
        result = response.json()
        
        print(f"响应状态: {response.status_code}")
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("code") == 1:
            data_field = result.get("data", "")
            if isinstance(data_field, dict):
                answer = data_field.get("answer", "")
                print(f"✅ 题库查询成功（新格式）: {answer}")
            elif isinstance(data_field, str):
                answer = data_field.strip()
                print(f"✅ 题库查询成功（旧格式）: {answer}")
            else:
                print(f"⚠️ 未知的data格式: {type(data_field)}")
        else:
            print(f"❌ 题库查询失败: {result.get('msg', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def test_ai_model_with_options():
    """测试AI模型接口（包含选项）"""
    print("\n\n=== 测试AI模型接口（包含选项） ===")
    
    # 模拟多选题（包含选项）
    question_with_options = """(多选题) 常见的店铺推广渠道有 ()。

A. 社交平台推广
B. 直播推广
C. 矩阵推广
D. 利用交流活动推广
"""
    
    url = "http://tk.mixuelo.cc/api.php?act=aimodel"
    
    data_str = urlencode({
        'key': 'zXPX828s29Kk7Yj2',
        'model': 'deepseek-chat',
        'question': question_with_options
    })
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    print(f"发送AI模型请求...")
    print(f"题目: {question_with_options[:50]}...")
    
    try:
        response = requests.post(url, data=data_str, headers=headers, timeout=30)
        result = response.json()
        
        print(f"响应状态: {response.status_code}")
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('code') == 1:
            ai_answer = result.get('answer', '') or result.get('data', '')
            if ai_answer:
                print(f"✅ AI模型生成成功: {ai_answer}")
                
                # 检查是否包含###分隔符（多选题格式）
                if "###" in ai_answer:
                    options = ai_answer.split("###")
                    print(f"✅ 多选题答案解析: {options}")
                else:
                    print(f"✅ 单一答案: {ai_answer}")
            else:
                print(f"⚠️ AI模型返回空答案")
        else:
            print(f"❌ AI模型调用失败: {result.get('msg', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def test_fill_question():
    """测试填空题"""
    print("\n\n=== 测试填空题 ===")
    
    question = "标准的直播团队包括主播、助播、()、场控和运营五个岗位。"
    
    # 测试题库查询
    print("--- 题库查询 ---")
    url = "http://tk.mixuelo.cc/api.php?act=query"
    data = {
        "key": "zXPX828s29Kk7Yj2",
        "question": question,
        "type": 2,  # 填空题
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json, text/javascript, */*; q=0.01",
    }
    
    try:
        response = requests.post(url, data=data, headers=headers, timeout=30)
        result = response.json()
        print(f"题库查询结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"题库查询异常: {str(e)}")
    
    # 测试AI模型
    print("\n--- AI模型 ---")
    url = "http://tk.mixuelo.cc/api.php?act=aimodel"
    data_str = urlencode({
        'key': 'zXPX828s29Kk7Yj2',
        'model': 'deepseek-chat',
        'question': question
    })
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        response = requests.post(url, data=data_str, headers=headers, timeout=30)
        result = response.json()
        print(f"AI模型结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"AI模型异常: {str(e)}")

def test_answer_format():
    """测试答案格式化"""
    print("\n\n=== 测试答案格式化 ===")
    
    test_answers = [
        "社交平台推广###直播推广###矩阵推广###利用交流活动推广",
        "直播策划",
        "答案1|答案2|答案3",
        "  带空格的答案  "
    ]
    
    def format_fill_answer(answer):
        """模拟_format_fill_answer方法"""
        if not answer:
            return ""
        
        answer = answer.strip()
        
        if "###" in answer:
            fill_answers = [ans.strip() for ans in answer.split("###") if ans.strip()]
            if len(fill_answers) > 1:
                formatted_answer = "\n".join(fill_answers)
                print(f"  多填空答案处理(换行格式): {fill_answers} -> {repr(formatted_answer)}")
            else:
                formatted_answer = fill_answers[0] if fill_answers else ""
                print(f"  单填空答案处理: {fill_answers} -> {formatted_answer}")
            return formatted_answer
        elif "|" in answer:
            fill_answers = [ans.strip() for ans in answer.split("|") if ans.strip()]
            if len(fill_answers) > 1:
                formatted_answer = "\n".join(fill_answers)
                print(f"  竖线分隔答案处理(换行格式): {fill_answers} -> {repr(formatted_answer)}")
            else:
                formatted_answer = fill_answers[0] if fill_answers else ""
                print(f"  竖线分隔单答案处理: {fill_answers} -> {formatted_answer}")
            return formatted_answer
        else:
            print(f"  单个填空答案: {repr(answer)}")
            return answer
    
    for i, test_answer in enumerate(test_answers, 1):
        print(f"\n--- 测试用例 {i} ---")
        print(f"原始答案: {repr(test_answer)}")
        formatted = format_fill_answer(test_answer)
        print(f"格式化后: {repr(formatted)}")

if __name__ == "__main__":
    print("平台ID9006最终修复效果测试")
    print("=" * 50)
    
    # 测试题库查询接口（包含选项）
    test_tiku_query_with_options()
    
    # 测试AI模型接口（包含选项）
    test_ai_model_with_options()
    
    # 测试填空题
    test_fill_question()
    
    # 测试答案格式化
    test_answer_format()
    
    print("\n" + "=" * 50)
    print("测试完成")
