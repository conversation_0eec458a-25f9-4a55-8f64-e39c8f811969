
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<script>
    _HOST_ = "//mooc1.chaoxing.com";
    _CP_ = "/mooc-ans";
    _HOST_CP1_ = "//mooc1.chaoxing.com/mooc-ans";
    // _HOST_CP2_ = _HOST_ + _CP_;
    _HOST_CP2_ = _CP_;
</script><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>作业作答</title>
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/pop.css?v=2023-0711-1500"/>
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/common.css?v=2025-0424-1038" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/marking_icon.css" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/icomoon.css?v=2025-0424-1038" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/viewStudent.css?v=2025-0526-1542" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/oralTest.css?v=2022-0726-1100" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/newBuilt.css?v=2023-0904-0900" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/englishWriting.css?v=2024-0117-2000" />
<link href="//mooc1.chaoxing.com/mooc-ans/css/questionBank/questionBankUsual.css?v=2019-1120-1400" type="text/css" rel="stylesheet"/><link href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/font.css?v=2025-0403-1500" rel="stylesheet" type="text/css" /><script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/common/jquery.min.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/common/jquery-migrate.min.js"></script><script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/dowork-select.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/views/exam/phone/s/v3/js/textareaHeightAuto-test.js?v=2024-0125-1600"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/common.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/poptoast.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/poplayout.js?v=2022-0218-1500"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/viewStudent.js?v=2020-0821-1100"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/oraltest.js?v=2025-0523-1700"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/workEnglishWriting.js?v=2024-0116-2000"></script>
<script src="//mooc1.chaoxing.com/mooc-ans/space/work/js/attach-mark.js?v=2023-1026-1600"></script><script src="//mooc1.chaoxing.com/mooc-ans/space/work/js/preview-attach.js?v=2023-1102-1900"></script><link href="//mooc1.chaoxing.com/mooc-ans/css/work/viewer.min.css?v=2021-0830-1700" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/js/jquery.md5.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/space/work/js/viewer-jquery.min.js?v=2024-0124-0200"></script>
<script type="text/javascript">
try{
	$(function(){
		var imgList = $(".TiMu").find("div img:not(.workAttach img, .attach img, .attachNew img, .stuAnswerArea img, .popClose img, .ans-formula-moudle, .no_view)");
		for (var i = 0, len = imgList.size(); i < len; i++) {
			 var src = imgList.eq(i).attr("src");
			 if(src){
    			var index = src.indexOf("375_1024");
    			if (index != -1) {
    				src = src.replace("375_1024", "origin");
    			}
                 var index2 = src.indexOf("750_1024");
                 if (index2 != -1) {
                     src = src.replace("750_1024", "origin");
                 }
    			imgList.eq(i).attr("data-original", src);
		   }
		}
        $(".TiMu").find("div img:not(.workAttach img, .attach img, .attachNew img, .stuAnswerArea img, .popClose img, .no_view)").viewer({
			url : 'data-original',
		});
	})
  }catch(error){}
</script><link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/animationQuestion.css" />
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/js/jquery.nicescroll.min.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/animation-question.js?v=2024-0319-1800"></script>


<input type="hidden" id="currentClockId" value="" /><script type="text/javascript">window.SUPPORT_AUDIO_SETTING = true; window.SUPPORT_AUDIO_CONTROL = true;</script>
<script src="//mooc1.chaoxing.com/mooc-ans/js/work/work-audio-playtimes.js?v=2024-0104-1500"></script>

<input type="hidden" id="fromDevice" value="mooc2"/>

<div class="maskDiv" style="display:none;z-index:1000;" id="audioLimitTimesWinNew">
    <div class="popSetDiv wid440">
        <div class="popHead RadisTop">
            <a href="javascript:;" class="popClose fr" onclick="$('#audioLimitTimesWinNew').fullFadeOut();">
                <img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/popClose.png"/>
            </a>
            <p class="fl fs18 color1">提示</p>
        </div>
        <div class="het62"></div>
        <p class="popWord fs16 color2 audioLimitTimesTip">此附件仅支持打开 <span></span> 次，你已打开 <span></span> 次，不能再次打开</p>
        <div class="popBottom RadisBom">
            <a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="$('#audioLimitTimesWinNew').fullFadeOut();">知道了</a>
        </div>
        <div class="het72"></div>
    </div>
</div>

<div class="AlertCon02" style="width:400px; height:200px;display:none;" id="audioLimitTimesWin">
    <h3 class="clearfix">
        <a href="javascript:;" class="closed02 fr" onclick="WAY.box.hide();$('#audioLimitTimesWin').css('display','none')" style="margin-top:1px;"></a>
    </h3>
    <div class="con03">
        <p class="audioLimitTimesTip" style="margin-top:16px;text-align:left;">此附件仅支持打开 <span></span> 次，你已打开 <span></span> 次，不能再次打开</p>
        <div style="margin-top:60px;">
            <a class="bluebtn" href="javascript:;" onclick="WAY.box.hide();$('#audioLimitTimesWin').css('display','none')">知道了</a>
        </div>
    </div>
</div><script>
function editorPaste(o, html) {
	html.html = "";
	$.toast({
		type : 'notice',
		content : "只能录入不能粘贴！"
	});
	return false;
}
window["uid"] = '221880762';
window["currentTime"] = '1751485797771';
window["uploadEnc"] = '43f5d73b76609539eb971486bc2bba2d';
</script>
</head>
<style>
.questionLi img{max-width:100%;}
.dtk .answerBg{display:inline-block;}
.dtk .answerBg:hover{background:none}
.borblue{border:1px solid #3A8BFF !important;}
.borred{border:1px solid #F33F34 !important;}
.blankInpDiv {width: 100%;height: 38px;border-radius: 4px;border: 1px solid #E1EAF4;box-sizing: border-box;-moz-box-sizing: border-box;-webkit-box-sizing: border-box;line-height: 38px;text-overflow: ellipsis;overflow: hidden;padding: 0 10px;white-space: nowrap;}
*{outline: none;}
</style>
	<style>
		.workTextWrap * {
			white-space: normal !important;
		}
	</style>
<body>
<style>
    .TipsverifyBox{padding:30px;}
    .TipsverifyBox input{display:block;width:160px;height:36px;padding:6px 14px;box-sizing: border-box;border-radius: 4px;}
    .verifyDiv{width:135px;height:36px;margin:0 16px}
    .changeOne{line-height:36px;}
    .border_color {border: 1px solid #D5D7D9;}
    .verifyInput:hover {border: 1px solid #3A8BFF;}
    .verifyInput:focus {border: 1px solid #3A8BFF;}
</style>
<div class="maskDiv verify-mask" style="z-index: 1001;display: none;">
    <div class="popDiv wid440 Marking">
        <div class="popHead">
            <a href="javascript:;" class="popClose fr" onclick="hideWorkVerify();"><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/popClose.png" /></a>
            <p class="fl fs18 colorDeep">提示</p>
        </div>
        <div class="het62"></div>
        <div class="fs14 TipsverifyBox">
            <input type="text" class="border_color fl verifyInput" id="inputCode" placeholder="请输入验证码" />
            <div class="fl verifyDiv"><img width="100%" height="100%"  id="workVerifyImg" src="" onclick="workVerifyCode();"/></div>
            <a href="javascript:;" class="color0 changeOne" onclick="workVerifyCode();">换一张</a>
            <div class="clear"></div>
        </div>

        <div class="popBottom">
            <a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="verifyCode();">确定</a>
            <a href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="hideWorkVerify();">取消</a>
        </div>
        <div class="het72"></div>
    </div>
</div>

<script>
function showWorkVerify() {
    workVerifyCode();
    $(".verify-mask").css("display", "block");
}

function hideWorkVerify() {
    $(".verify-mask").css("display", "none");
}

function submitCheckTimes() {
    var addTimes = $("#addTimes").val() || 0;
    var limitWorkSubmitTimes = $("#limitWorkSubmitTimes").val() || 100;
    if (parseInt(addTimes) > parseInt(limitWorkSubmitTimes)) {
        showWorkVerify();
    } else {
        setTimeout("submitWork()", 500);
    }
}

function workVerifyCode() {
    $("#workVerifyImg").attr("src", _HOST_CP2_ + "/verifyCode/stuWork?" + new Date().getTime());
}

function verifyCode() {

    var courseId = $("#courseId").val() || 0;
    var classId = $("#classId").val() || 0;
    var cpi = $("#cpi").val() || 0;
    var workId = $("#workId").val() || 0;
    var answerId = $("#answerId").val() || 0;
    var inputCode = $("#inputCode").val() || "";

    $.ajax({
        type: "get",
        url: _HOST_CP2_ + "/work/verify/code",
        cache: false,
        dataType: "json",
        async: false,
        data: {
            "courseId": courseId,
            "classId": classId,
            "cpi": cpi,
            "workId": workId,
            "answerId": answerId,
            "inputCode": inputCode
        },
        success: function (data) {
            if (!data.status) {
                workVerifyCode();
                $("#inputCode").val("");
                $.toast({
                    type : 'failure',
                    content : data.msg
                });
            } else {
                var enc = data.enc;
                $("#workTimesEnc").val(enc);
                submitWork();
            }
        }
    });
}
</script><div class="subNav">
	<div class="sub-button fr" id="submitFocus" tabindex="-1">
				<a href="javascript:;" onclick="saveWork();" class="fl" tabindex="0">暂时保存</a>
											<a href="javascript:;" onclick="submitValidate();" class="completeBtn fl" tabindex="0" role="button">提交</a>
						</div>作业</div>
<div class="het40"></div>

<script type="text/javascript" src="/mooc2/js/codemirror/js/codemirror.js"></script>

<script type="text/javascript" src="/mooc2/js/codemirror/js/clike.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/go.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/css.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/python.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/sql.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/xml.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/highlight.min.js"></script>

<script type="text/javascript" src="/mooc2/js/codemirror/js/anyword-hint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/beautify.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/brace-fold.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/clike.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/closebrackets.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/closetag.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/comment-fold.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/foldcode.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/foldgutter.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/go.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/indent-fold.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript-hint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript-lint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/jshint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/lint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/matchbrackets.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/php.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/powershell.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/python.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/ruby.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/show-hint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/swift.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/vb.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/xml.min.js"></script>

<link rel="stylesheet" type="text/css" href="/mooc2/js/codemirror/lib/codemirror.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/foldgutter.min.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/lint.min.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/show-hint.min.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/common.css?v=2025-0314-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/selfTesting.css?v=2025-0427-1801"/>

<script src="//mooc1.chaoxing.com/mooc-ans/js/jquery.jqote2.min.js" ></script>

<script type="text/javascript" src="/mooc2/js/codemirror/codeEditor.js?v=2025-0320-1130"></script>
<script>
    window.noNeedCodemirrorInUeditor = true;
</script>
<script type="text/x-jqote-template" id="codeEditorTpl">
    <%
    const currLanguageName = this.currLanguageName || '请选择程序语言';
    const currLanguageIndex = this.currLanguageIndex || -1;
    const languages = this.languages;
    const businessId = this.businessId;
    %>

    <!--弹窗-->
    <div class="maskBox reset-mask" data-business-id="<%= businessId %>" style="display:none">
        <div class="promptPop">
            <div class="popHeadNew">
                <a href="#" class="popClose fr" data-business-id="<%= businessId %>"><img src="//mooc1.chaoxing.com/mooc-ans/images/popClose.png"></a>
                <p>提示</p>
            </div>
            <p class="popWord2">代码框将重置为初始状态，正在编辑的代码将清空，确认重置代码？</p>
            <div class="popBottomNew">
                <a href="#" class="popbtn_bg btnReset fr" data-business-id="<%= businessId %>">重置代码</a>
                <a href="#" class="popbtn_border btnCancle fr" data-business-id="<%= businessId %>">取消</a>
            </div>
        </div>
    </div>

    <div class="maskBox setting-mask" data-business-id="<%= businessId %>" style="display:none">
        <div class="promptPop">
            <div class="popHeadNew">
                <a href="#" class="popClose fr" data-business-id="<%= businessId %>"><img src="//mooc1.chaoxing.com/mooc-ans/images/popClose.png"></a>
                <p>代码编辑器设置</p>
            </div>
            <div class="popWord2">
                <div class="flex-start">
                    <span>字体设置</span>
                    <strong class="selectBox fontList" data-business-id="<%= businessId %>">
                        <p value="0"><span>14px</span><i class="icon-arrow-down2"></i></p>
                        <ul class="options optionsCon" style="display:none">
                            <li><a href="javascript:;">12px</a></li>
                            <li><a href="javascript:;">14px</a></li>
                            <li><a href="javascript:;">16px</a></li>
                            <li><a href="javascript:;">18px</a></li>
                            <li><a href="javascript:;">20px</a></li>
                            <li><a href="javascript:;">24px</a></li>
                            <li><a href="javascript:;">36px</a></li>
                            <li><a href="javascript:;">48px</a></li>
                        </ul>
                    </strong>
                </div>
            </div>
            <div class="popBottomNew">
                <a href="#" class="popbtn_bg btnSetting fr" data-business-id="<%= businessId %>">确认</a>
                <a href="#" class="popbtn_border btnCancle fr" data-business-id="<%= businessId %>">取消</a>
            </div>
        </div>
    </div>

    <div class="mrconBx" data-business-id="<%= businessId %>">
        <div class="mrconTop">
            <div>
                <i class="icon-com fr setting" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>设置</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>
                <i class="icon-com fr copy" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>复制</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>
                <i class="icon-com fr refresh" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>重置</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>
                <i class="icon-com fr format" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>格式化</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>

                <% if(currLanguageIndex!== -1) { %>
                <!--mooc2创建的程序题-->
                <strong class="selectBoxLang fl langList" codenum="<%= currLanguageIndex %>" codename="<%= currLanguageName %>" data-business-id="<%= businessId %>">
                    <p value="0"><span><%= currLanguageName %></span><i class="icon-arrow-down2"></i></p>
                    <ul class="options " style="display:none">
                        <% for(const pair of languages) { %>
                        <li><a href="javascript:void(0)" codename="<%= pair.codeName %>" code="<%= pair.code %>" codenum="<%= pair.codenum %>"><%= pair.codeName %></a></li>
                        <% } %>
                    </ul>
                </strong>
                <% } else { %>
                <!--mooc1创建的程序题 || 新建的程序题-->
                <strong class="selectBoxLang fl langList" data-business-id="<%= businessId %>">
                    <p value="0"><span>请选择程序语言</span><i class="icon-arrow-down2"></i></p>
                    <ul class="options " style="display:none">
                        <% for(const pair of languages) { %>
                        <li><a href="javascript:void(0)" codename="<%= pair.codeName %>" code="<%= pair.code %>" codenum="<%= pair.codenum %>"><%= pair.codeName %></a></li>
                        <% } %>
                    </ul>
                </strong>
                <% } %>

                <p class="clear"></p>
            </div>
        </div>
    </div>


</script>
<script type="text/javascript" charset="utf-8" src="//mooc1.chaoxing.com/mooc-ans/js/ServerHost.js?v=2020-1225-1627"></script>
<script type="text/javascript" charset="utf-8" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/editor/ueditor.config.js?v=**************"></script>
<script type="text/javascript" charset="utf-8" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/editor/ueditor.all.min.js?v=**************"></script>
<script>
    function getcookie(objname){
    	var arrstr = document.cookie.split("; ");
    	for(var i = 0;i < arrstr.length;i ++){
    		var temp = arrstr[i].split("=");
    		if(temp[0] == objname){ 
    			return unescape(temp[1]);
    		}
    	}
    }
	window["uid"] = '221880762';
	window["currentTime"] = '1751485797771';
	window["uploadEnc"] = '43f5d73b76609539eb971486bc2bba2d';
	window.UEDITOR_CONFIG.forbidDownload = '';
	window.UEDITOR_CONFIG.scaleEnabled = false;
	window.UEDITOR_CONFIG.imageUrl = ServerHost.uploadDomain + "/ueditorupload/upload?t=1751485797771&enc2=43f5d73b76609539eb971486bc2bba2d&uid=221880762";
	window.UEDITOR_CONFIG.fileUrl = ServerHost.uploadDomain + "/ueditorupload/attachment";
	window.UEDITOR_CONFIG.lang = 'zh-cn';
	window.fycourseDomain = "mobile3.chaoxing.com";

	var isMirror = "false";
	if (isMirror == "true") {
		window.UEDITOR_CONFIG.toolbars = [['removeformat', 'formatmatch', 'paragraph', 'fontfamily', 'fontsize', 'bold', 'italic', 'underline', 'forecolor', 'justifyleft', 'justifycenter', 'justifyright', 'spechars', 'inserttable', 'mathml', 'edrawmath', 'insertimage', 'attachment_new', 'recording', 'audio', 'drawingboard', 'insertcodedefined']];
	}

	    	var toolbars = window.UEDITOR_CONFIG.toolbars[0];
		if($.inArray('mathml', toolbars) >= 0){
            toolbars.splice($.inArray('mathml', toolbars) , 1,'latex');
            window.UEDITOR_CONFIG.toolbars[0] = toolbars;
            window.UEDITOR_CONFIG.latexMode = 1;
		}
	
		    window.UEDITOR_CONFIG._mathpix2latex = '228836418-64009643-282371395-s_358b9621080c6b2e7b70c99ec324a55e';
	
	</script>
<script type="text/javascript" charset="utf-8">
	if ("1" == 1 || 'true' == 'false') {
		window.UEDITOR_CONFIG.disableDraggable = true;
		window.UEDITOR_CONFIG.disablePasteImage = true;
	}else {
		var toolbars = window.UEDITOR_CONFIG.toolbars[0];
		toolbars.push('insertparseword');
		window.UEDITOR_CONFIG.toolbars[0] = toolbars;
	}
	window.UEDITOR_CONFIG.imagePopup = false;
</script>
<input type="hidden" id="limitWorkSubmitTimes" value="100"/>
<input type="hidden" id="addTimes" value="0"/>
<input type="hidden" id="oralEvalSync" value="0" />
<div class="fanyaMarking TiMu" id="fanyaMarking">
	<div class="fanyaMarking_left whiteBg">
		<div class="padBom20 detailsHead">
			<h2 class="mark_title" tabindex="0" role="option" >立面图的识读</h2>
			<div class="infoHead" tabindex="0" role="option" >
				<span>题量: 10</span>
				<span>满分: 100</span>
									<p>作答开始时间:<em>2022-12-15 08:45</em></p>
											</div>
		</div>

		
				
							<div class="mark_table padTop20 ans-cc">
						<form action="/mooc-ans/work/addStudentWorkNewWeb?_classId=64009643&courseid=228836418&token=fdb8e5d170da272670346f3bf4cf7ad1&totalQuestionNum=6eaca1bf84b6b650bc4b09d8f8805f1e" method="post" id="submitForm" name="submitForm" onkeydown="if(event.keyCode==13){ if(event.srcElement.type=='textarea'){ return true; } else {return false;}}">
			<input type="hidden" id="workObj" value="false" />
			<input type="hidden" id="courseId" name="courseId" value="228836418" />
			<input type="hidden" id="classId" name="classId" value="64009643" />
			<input type="hidden" id="knowledgeId" name="knowledgeid" value="0" />
			<input type="hidden" id="cpi" name="cpi" value="282371395"/>
			<input type="hidden" id="workId" name="workRelationId" value="24875828" />
			<input type="hidden" id="answerId" name="workAnswerId" value="52094560"/>
			<input type="hidden" id="jobid" name="jobid" value=""/>
			<input type="hidden" id="standardEnc" name="standardEnc" value="eba6203955dc26a6781808c842f604b7"/>
			<input type="hidden" id="enc_work" name="enc_work" value="fdb8e5d170da272670346f3bf4cf7ad1"/>
			<input type="hidden" id="totalQuestionNum" name="totalQuestionNum" value="6eaca1bf84b6b650bc4b09d8f8805f1e" />
			<input type="hidden" id="pyFlag" name="pyFlag" value="" />
			<input type="hidden" id="questionIds" name="answerwqbid" value="" />
			<input type="hidden" id="mooc2" name="mooc2" value="1" />
			<input type="hidden" id="uploadTimeStamp" value="1751485797771" />
			<input type="hidden" id="userId" value="221880762" />
			<input type="hidden" id="uploadEnc" value="43f5d73b76609539eb971486bc2bba2d" />
			<input type="hidden" id="uploadtype" value="work" />
			<input type="hidden" id="creatorId" value="282371395" />
			<input type="hidden" id="enc" value="efc4c54fbc52fb7e756541b1331f72d2" />
			<input type="hidden" id="matchEnc" value="b67c232497402196d45595544b92b261" />
			<input type="hidden" id="submitStep" value="1" />
			<input type="hidden" id="cfid" value="2790" />
			<input type="hidden" id="randomOptions" name="randomOptions" value="false"/>
			<input type="hidden" id="workTimesEnc" name="workTimesEnc" value=""/>
			
				
				
								<div class="whiteDiv">
                                    <h2 class="type_tit" tabindex="0" role="option"  >一. 简答题（共10题，100分）</h2>
                																													<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691861" data="211691861" tabindex="0" role="option"  aria-label="题目 1. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >1.
																<span class="colorShallow">(简答题)</span> 6~1轴立面、A~D轴立面分别指的是哪两个方向？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691861" name="answertype211691861" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691861" name="answer211691861"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691861",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691861").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691861").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691861").options.minimumWords);
													if (count == 0) {
														$("#answer211691861").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691861").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691861").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691861").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691861").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691861").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691861").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691861").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691861, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691862" data="211691862" tabindex="0" role="option"  aria-label="题目 2. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >2.
																<span class="colorShallow">(简答题)</span> 本工程外立面有哪些颜色？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691862" name="answertype211691862" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691862" name="answer211691862"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691862",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691862").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691862").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691862").options.minimumWords);
													if (count == 0) {
														$("#answer211691862").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691862").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691862").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691862").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691862").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691862").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691862").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691862").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691862, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691863" data="211691863" tabindex="0" role="option"  aria-label="题目 3. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >3.
																<span class="colorShallow">(简答题)</span> 本工程外立面装饰用到了哪些材料？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691863" name="answertype211691863" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691863" name="answer211691863"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691863",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691863").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691863").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691863").options.minimumWords);
													if (count == 0) {
														$("#answer211691863").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691863").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691863").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691863").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691863").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691863").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691863").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691863").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691863, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691864" data="211691864" tabindex="0" role="option"  aria-label="题目 4. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >4.
																<span class="colorShallow">(简答题)</span> 一层层高是多少？雨篷顶标高是多少？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691864" name="answertype211691864" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691864" name="answer211691864"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691864",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691864").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691864").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691864").options.minimumWords);
													if (count == 0) {
														$("#answer211691864").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691864").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691864").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691864").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691864").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691864").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691864").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691864").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691864, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691865" data="211691865" tabindex="0" role="option"  aria-label="题目 5. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >5.
																<span class="colorShallow">(简答题)</span> C2424窗台高度是多少？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691865" name="answertype211691865" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691865" name="answer211691865"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691865",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691865").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691865").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691865").options.minimumWords);
													if (count == 0) {
														$("#answer211691865").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691865").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691865").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691865").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691865").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691865").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691865").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691865").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691865, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691866" data="211691866" tabindex="0" role="option"  aria-label="题目 6. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >6.
																<span class="colorShallow">(简答题)</span> D~A立面上二层的两个窗户编号是？属于哪个房间？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691866" name="answertype211691866" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691866" name="answer211691866"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691866",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691866").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691866").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691866").options.minimumWords);
													if (count == 0) {
														$("#answer211691866").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691866").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691866").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691866").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691866").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691866").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691866").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691866").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691866, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691867" data="211691867" tabindex="0" role="option"  aria-label="题目 7. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >7.
																<span class="colorShallow">(简答题)</span> 6~1立面中，入口门的开启方向是内开还是外开？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691867" name="answertype211691867" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691867" name="answer211691867"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691867",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691867").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691867").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691867").options.minimumWords);
													if (count == 0) {
														$("#answer211691867").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691867").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691867").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691867").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691867").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691867").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691867").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691867").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691867, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691868" data="211691868" tabindex="0" role="option"  aria-label="题目 8. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >8.
																<span class="colorShallow">(简答题)</span> 立面门板上的是三角形代表什么？窗户上的小箭头代表什么？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691868" name="answertype211691868" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691868" name="answer211691868"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691868",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691868").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691868").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691868").options.minimumWords);
													if (count == 0) {
														$("#answer211691868").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691868").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691868").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691868").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691868").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691868").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691868").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691868").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691868, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691869" data="211691869" tabindex="0" role="option"  aria-label="题目 9. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >9.
																<span class="colorShallow">(简答题)</span> 从建筑外侧看，一层窗户的窗台高度是？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691869" name="answertype211691869" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691869" name="answer211691869"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691869",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691869").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691869").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691869").options.minimumWords);
													if (count == 0) {
														$("#answer211691869").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691869").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691869").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691869").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691869").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691869").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691869").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691869").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691869, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
																																			<div class="padBom50 questionLi fontLabel singleQuesId" typeName="简答题" id="question211691871" data="211691871" tabindex="0" role="option"  aria-label="题目 10. ">
														<h3 aria-hidden="true"  class="mark_name colorDeep fontLabel workTextWrap" tabindex="0" role="option" >10.
																<span class="colorShallow">(简答题)</span> 入口处台阶有几级？每级高度是？
							</h3>
							<div class="clear"></div>
														<input type="hidden" id="answertype211691871" name="answertype211691871" value="4" />

																						
																								
																								
								<div class="stem_answer">
									<div class="eidtDiv">
																					<textarea id="answer211691871" name="answer211691871"></textarea>
											<script>
												var wordNum = "";
												if (wordNum == "") {
													wordNum = 0;
												}
												var wordMinNum = "";
												if (wordMinNum == "") {
													wordMinNum = 0;
												}
												var qtype = "4";
												// var wordCount = false;
												// if (qtype == 4) {
												// 	wordCount = true;
												// }
												window.UEDITOR_CONFIG.initialFrameHeight = 150;
												var wordCount = true;
												if (parseInt(wordNum) == 0 && parseInt(wordMinNum) == 0) {
													wordCount = false;
												}
												window.UEDITOR_CONFIG.wordCount = wordCount;
												window.UEDITOR_CONFIG.wordCountTimer = false;
												// window.UEDITOR_CONFIG.maximumWords = wordNum;
												// window.UEDITOR_CONFIG.minimumWords = wordMinNum;
												var editor1 = UE.getEditor("answer211691871",{'pasteplain':true});
												editor1.options.maximumWords = wordNum;
												editor1.options.minimumWords = wordMinNum;
												editor1.addListener('blur',function(){
													var count = UE.getEditor("answer211691871").pureWordCount();
													var max = parseInt(UE.getEditor("answer211691871").options.maximumWords);
													var min = parseInt(UE.getEditor("answer211691871").options.minimumWords);
													if (count == 0) {
														$("#answer211691871").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691871").parent().find(".edui-editor.edui-default").removeClass("borred");
													}else if ((max > 0 && count > max) || (min > 0 && min > count)) {
														$("#answer211691871").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691871").parent().find(".edui-editor.edui-default").addClass("borred");
														return;
													} else {
														$("#answer211691871").parent().find(".edui-editor.edui-default").removeClass("borblue");
														$("#answer211691871").parent().find(".edui-editor.edui-default").removeClass("borred");
													}
												});
												editor1.addListener('focus',function(){
													$("#answer211691871").parent().find(".edui-editor.edui-default").addClass("borblue")
													$("#answer211691871").parent().find(".edui-editor.edui-default").removeClass("borred")
												});
												var allowPaste = "1";
												if (parseInt(allowPaste) == 1) {
													editor1.addListener('beforepaste', editorPaste); 
												}
												editor1.addListener('contentChange', function() {
													loadEditorAnswerd(211691871, 4);
													answerContentChange();
												});
												window.UEDITOR_CONFIG.wordCount = false;
											</script>
																			</div>		
								</div>
								
																
																
														
						</div>
															</div>
										</form>
			</div>
			</div>
	
		<div class="fanyaMarking_right" id="rightHeight">
		<div class="topicNumber" id="topicNumberScroll" tabindex="-1">
			                                    
						
						
                        <div class="topicNumber_checkbox colorDeep fs14" ><span class="numRight fr"></span>一. 简答题（100分）</div>
                        <ul class="topicNumber_list clearfix" >
                                                            <li id="answerSheet211691861" data="211691861"  >1</li>
                                                            <li id="answerSheet211691862" data="211691862"  >2</li>
                                                            <li id="answerSheet211691863" data="211691863"  >3</li>
                                                            <li id="answerSheet211691864" data="211691864"  >4</li>
                                                            <li id="answerSheet211691865" data="211691865"  >5</li>
                                                            <li id="answerSheet211691866" data="211691866"  >6</li>
                                                            <li id="answerSheet211691867" data="211691867"  >7</li>
                                                            <li id="answerSheet211691868" data="211691868"  >8</li>
                                                            <li id="answerSheet211691869" data="211691869"  >9</li>
                                                            <li id="answerSheet211691871" data="211691871"  >10</li>
                                                    </ul>
                    									</div>
	</div>
		<div id="ariaHtmlEnd" tabindex="0" aria-label="作业作答结尾" role="row" onkeydown="toSubmitFocus();"></div>
</div>
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/prompt.css?v=2023-0711-1500"/>
<style>
	.mask-no-bg{background:none!important;}
	.mask-no-bg .popDiv{box-shadow: 0px 4px 36px rgba(0, 0, 0, 0.08);}
</style>
<div class="maskDiv" style="display:none;" id="workpop">
	<div class="popDiv wid440 Marking">
		<div class="popHead" id="workpopFocus" role="alertdialog" tabindex="0">
			<a id="popCloseFocus"  href="javascript:;" class="popClose fr" onclick="popClickFunc()" tabindex="-1"><img src="//mooc1.chaoxing.com/mooc-ans/images/popClose.png" id="popCloseFocusImg" onkeydown="if(event.keyCode == 13){popCloseFocus();}" role="button" tabindex="-1" aria-label="关闭"/></a>
			<p class="fl fs18 colorDeep" tabindex="0" role="option">提示</p>
		</div>
		<div class="het62"></div>
		<p class="popWord fs16 colorIn" id="popcontent" tabindex="0"></p>

		<div class="popBottom">
			<a href="javascript:;" id="popok" class="jb_btn jb_btn_92 fr fs14" tabindex="0" role="button" aria-label="确定"></a>
			<a href="javascript:;" id="popno" class="btnBlue btn_92_cancel fr fs14" onkeydown="popnoFocus();" tabindex="0" role="button" aria-label="取消"></a>
		</div>
		<div class="het72"></div>
			</div>
</div>

<div class="mask-no-bg" style="display:none;" id="worktoast">
	<div class="popDiv wid440 Marking" style="top: 30%;">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr" onclick="toastClickFunc()"><img src="//mooc1.chaoxing.com/mooc-ans/images/popClose.png" /></a>
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<p class="popWord fs16 colorIn" id="toastcontent"></p>

		<div class="popBottom">
			<a href="javascript:;" id="toastok" class="jb_btn jb_btn_92 fr fs14"></a>
			<a href="javascript:;" id="toastno" class="btnBlue btn_92_cancel fr fs14"></a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;" id="hintPop">
	<div class="popDiv wid440 Marking">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<p class="popWord fs16 colorIn" id="hintCon"></p>
		<div class="popBottom">
			<a href="javascript:;" id="hintOk" class="jb_btn jb_btn_92 fr fs14"></a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<script>
function popClickFunc() {
	$('#workpop').hide();
	if($('.popSetupShowHide').length > 0){
		$('.popSetupShowHide').fullFadeOut();
	}
}

function toastClickFunc() {
	$('#worktoast').hide();
	if($('.popSetupShowHide').length > 0){
		$('.popSetupShowHide').fullFadeOut();
	}
}


function workPop(content, okTent, cancelTent, okcallback, cancelCallback) {
	var ta = $("#workpop");

	if($('.popSetupShowHide').length > 0){
		$(".popSetupShowHide").fullFadeIn();
	}

	ta.show();
	
	$("#popcontent").html(content);
	var popokHiddenRead = $("#popokHiddenRead");

	var ok = $("#popok");
	ok.html(okTent);
	ok.unbind();
	ok.on("click", function() {
		// 无障碍单位走定制,延迟语音读取处理
		if("0" == "1") {
			popokHiddenRead.attr("tabindex","0");
			if (content && content != '很遗憾，未达到及格线，请重做') {
				popokHiddenRead.focus();
			}
			setTimeout(function() {
				okcallback && okcallback(ta);
				popokHiddenRead.attr("tabindex","-1")
				ta.hide();
				if($('.popSetupShowHide').length > 0) {
					$(".popSetupShowHide").fullFadeOut();
				}
			}, 1000)
		} else {
			okcallback && okcallback(ta);
			ta.hide();

			if($('.popSetupShowHide').length > 0) {
				$(".popSetupShowHide").fullFadeOut();
			}
		}
	});

	var cancel = $("#popno");
	cancel.html(cancelTent);
	cancel.unbind();
	cancel.on("click", function() {
		cancelCallback && cancelCallback(ta);
		ta.hide();
		if($('.popSetupShowHide').length > 0) {
			$(".popSetupShowHide").fullFadeOut();
		}
	});
	if("0" == "1") {
		$("#workpopFocus").focus();
	}
}

function workToast(content, okTent, cancelTent, okcallback, cancelCallback) {
	var ta = $("#worktoast");

	if($('.popSetupShowHide').length > 0) {
		$(".popSetupShowHide").fullFadeIn();
	}

	ta.show();

	$("#toastcontent").html(content);

	var ok = $("#toastok");
	ok.html(okTent);
	ok.unbind();
	ok.on("click", function() {
		okcallback && okcallback(ta);
		ta.hide();

		if($('.popSetupShowHide').length > 0) {
			$(".popSetupShowHide").fullFadeOut();
		}
	});

	var cancel = $("#toastno");
	cancel.html(cancelTent);
	cancel.unbind();
	cancel.on("click", function() {
		cancelCallback && cancelCallback(ta);
		ta.hide();
		if($('.popSetupShowHide').length > 0) {
			$(".popSetupShowHide").fullFadeOut();
		}
	});
}

function workHint(content, okTent, okcallback) {
	var ta = $("#hintPop");

	if($('.popSetupShowHide').length > 0) {
		$(".popSetupShowHide").fullFadeIn();
	}

	ta.show();

	$("#hintCon").html(content);

	var ok = $("#hintOk");
	ok.html(okTent);
	ok.unbind();
	ok.on("click", function() {
		okcallback && okcallback(ta);
		ta.hide();

		if($('.popSetupShowHide').length > 0) {
			$(".popSetupShowHide").fullFadeOut();
		}
	});
}

function popFocus() {
	if("0" != "1") {
		return;
	}
	$("#popCloseFocusImg").attr("tabindex","0");
	$("#popCloseFocus").focus();
}
function popCloseFocus() {
	if("0" != "1") {
		return;
	}
	$('#workpop').hide();
	if($('.popSetupShowHide').length > 0) {
		$('.popSetupShowHide').fullFadeOut();
	}
	$("#popCloseFocusImg").attr("tabindex","-1");
}
function popnoFocus() {
	if("0" != "1") {
		return;
	}
	if(event.keyCode == 9){
		popFocus();
	}
	if(event.keyCode == 13){
		$("#popCloseFocusImg").attr("tabindex","-1");
	}
}

</script><script>
var courseId = $("#courseId").val();
var classId = $("#classId").val();
var cpi = $("#cpi").val();
var isContentChanged = false;
function addChoice(obj) {
	var qid = $(obj).attr("qid");
	var qt = $(obj).attr("qtype");

	if ($(obj).find(".num_option").hasClass("check_answer")) {
		$(obj).find(".num_option").removeClass("check_answer");
		$(obj).attr("aria-checked","false");
		$(obj).attr("aria-pressed","false");
	} else {
		$(".choice" + qid).removeClass("check_answer");
		$(obj).find(".num_option").addClass("check_answer");
		$(obj).attr("aria-checked","true");
		$(obj).attr("aria-pressed","true");
	}
	
	// 无障碍支持
	$(obj).siblings().attr("aria-checked","false");
	$(obj).siblings().attr("aria-pressed","false");

	var choiceContent = "";
	$(".choice" + qid).each(function(index, ele) {
		if ($(this).hasClass("check_answer")) {
			choiceContent = choiceContent + $(this).attr("data");
		}
	});
	$("#answer" + qid).val(choiceContent);

	answerContentChange();
	
	if (qt == 14) {
		var questionId = $(obj).attr("questionId");
		setClozeTextAnswer(questionId);
	} else if (qt == 15 || qt == 19) {
		var questionId = $(obj).attr("questionId");
		loadEditorAnswerd(questionId, qt);
	} else {
		loadAnswerSheet(qid, choiceContent);
	}
}

function addMultipleChoice(obj) {
	var qid = $(obj).attr("qid");
	var qt = $(obj).attr("qtype");
	if(qt == 21 &&  !checkEvaluationQuesNum(obj)){
	  return;
	}
	if ($(obj).find(".num_option_dx").hasClass("check_answer_dx")) {
		$(obj).find(".num_option_dx").removeClass("check_answer_dx");
		$(obj).attr("aria-checked","false");
		$(obj).attr("aria-pressed","false");
	} else {
		$(obj).find(".num_option_dx").addClass("check_answer_dx");
		$(obj).attr("aria-checked","true");
		$(obj).attr("aria-pressed","true");
	}

	var choiceContent = "";
	$(".choice" + qid).each(function(index, ele) {
		if ($(this).hasClass("check_answer_dx")) {
			choiceContent = choiceContent + $(this).attr("data");
		}
	});
	var randomOptions = $("#randomOptions").val();
	choiceContent = randomOptions ? sortMultiAnswer(choiceContent) : choiceContent;

	$("#answer" + qid).val(choiceContent);
	answerContentChange();
	
	if (qt == 15 || qt == 19) {
		var questionId = $(obj).attr("questionId");
		loadEditorAnswerd(questionId, qt);
	} else {
		loadAnswerSheet(qid, choiceContent);
	}
}

function sortMultiAnswer(answer) {
	if ( !answer || answer.trim().length == 0) {
		return "";
	}
	var answerArray = answer.trim().split('');
	var sortedAnswerArray = answerArray.sort();
	return sortedAnswerArray.join('');
}

function loadAnswerSheet(questionId, content) {
	if($("#answerSheet" + questionId).hasClass("active") && content.length == 0) {
		$("#answerSheet" + questionId).removeClass("active");
	} else if(content.length > 0) {
		$("#answerSheet" + questionId).addClass("active");
	}
}

$(function() {
	$("textarea").autoHeight({
		len : 53
	});

	$(document).ready(function() {
		$(".optionsCon").niceScroll({
			cursorborder : "",
			cursorwidth : "8px",
			cursorcolor : "#E6ECF5",
			boxzoom : false
		});
	});
});


function setConnLineAnswer(qid) {
	var answerArr = [];
	$(".connlineSelect" + qid).each(function() {
		var name = $(this).attr("data");
		var content = $(this).attr("value");
		var obj = {};
		obj.name = name;
		obj.content = content;
		answerArr.push(obj);
	});
	var answer = JSON.stringify(answerArr);
	$("#answer" + qid).val(answer);
	answerContentChange();
	loadEditorAnswerd(qid, 11);

}

function setSortAnswer(qid) {
	var answer = "";
	$(".sortSelect" + qid).each(function() {
		var content = $(this).attr("value");
		answer = answer + content;
	});
	$("#answer" + qid).val(answer);
	
	loadEditorAnswerd(qid, 13);
}

function wrapCode(code) {
	const preElement = document.createElement('pre');
	preElement.setAttribute('class', 'line-numbers hover newProcedure');

	const codeElement = document.createElement('code');
	codeElement.setAttribute('class', 'language-plain');
	codeElement.setAttribute('lang', 'Plain Text');
	codeElement.textContent = code;

	preElement.appendChild(codeElement);

	return preElement.outerHTML;
}

function setProceduralAnswer() {
	$(".proceduralQue").each(function() {
		var answer = [];
		var qid = $(this).attr("data");
		var language = $("#languageSelect" + qid).val();

		var editor = codeEditors[qid];
		var richcode = editor.getValue();

		richcode = wrapCode(richcode);
		var code = editor.getValue();
		code = UE.utils.html(code);
		var item = {};
		item.language = parseInt(language);
		item.answer = richcode;
		item.answerTxt = code;
		answer[0] = item;
		var answerStr = JSON.stringify(answer);
		$("#answer" + qid).val(answerStr);
	});
}


function setAllClozeAnswer() {
	$(".clozeTextQues").each(function() {
		var qid = $(this).attr("data");
		var answerObj = {};
		$(".clozeTextItem" + qid).each(function() {
			var itemId = $(this).attr("data");
			var content = $("#answer" + qid + itemId).val();
			var info = {};
			info.answer = content;
			answerObj[itemId] = info;
		});
		var answer = [];
		answer.push(answerObj);
		var answerStr = JSON.stringify(answer);
		$("#answer" + qid).val(answerStr);
	});
}

function setClozeTextAnswer(qid) {
	var answerObj = {};
	$(".clozeTextItem" + qid).each(function() {
		var itemId = $(this).attr("data");
		var content = $("#answer" + qid + itemId).val();
		var info = {};
		info.answer = content;
		answerObj[itemId] = info;
	});
	var answer = [];
	answer.push(answerObj);
	var answerStr = JSON.stringify(answer);
	$("#answer" + qid).val(answerStr);
	
	loadEditorAnswerd(qid, 14);
}

function setReadComprehensionAnswer() {
	var isAccessibleCustomFid = "0";
	$(".readComprehensionQues").each(function() {
		var answerObj = {};
		var qid = $(this).attr("data");
		$(".readingItem" + qid).each(function() {
			var qtid = $(this).attr("data");
			var itemId = $(this).attr("itemId");
			var type = $(this).attr("qtype");
			var itemAnswer = "";
			switch (parseInt(type)) {
				case 0 :
				case 1 : {
					itemAnswer = $("#answer" + qtid).val();
					break;
				}
				case 3 : {
					itemAnswer = $("#answer" + qtid).val();
					itemAnswer = itemAnswer ? (itemAnswer == "true" ? true : false) : itemAnswer;
					break;
				}
				case 2 : {
					var answerItem = [];
					var blankNum = $("#blankNum" + qtid).val();
					for (var i = 1; i <= blankNum; i++) {
	                    var answerEditorId = "answerEditor" + qtid + i;
						var answerEditorStr;
						if (isAccessibleCustomFid && isAccessibleCustomFid == "1") {
							answerEditorStr = $("#" + answerEditorId).val();
						} else {
							answerEditorStr = UE.getEditor(answerEditorId).getContent();	
						}
	                    
	                    var item = {};
						item.content = answerEditorStr;
						item.name = i.toString();
						answerItem.push(item);
					}
					itemAnswer = answerItem;
					break;
				}
				case 4 : {
					var answerEditorId = "answer" + qtid;
					var answerEditorStr;
					if (isAccessibleCustomFid && isAccessibleCustomFid == "1") {
						answerEditorStr = $("#" + answerEditorId).val();
					} else {
						answerEditorStr = UE.getEditor(answerEditorId).getContent();
					}
					itemAnswer = answerEditorStr;
					break;
				}
				default : {}
			}
			var info = {};
			info.type = parseInt(type);
			info.answer = (itemAnswer != undefined ? itemAnswer : "");
			answerObj[itemId] = info;
		});
		var answer = [];
		answer.push(answerObj);
		var answerStr = JSON.stringify(answer);
		$("#answer" + qid).val(answerStr);
	});
}

function submitValidate(pyFlag) {
	$("#pyFlag").val(pyFlag);
	$.ajax({
		type : "get",
		url : _HOST_CP2_ + "/work/validate",
		data : {
			courseId : courseId,
			classId : classId,
			cpi : cpi
		},
		success : function(data) {
			if (data.status == 1) {
				$.toast({
					type : 'failure',
					content : "账号发生异常，请重新登录！"
				});
				return;
			}
			if (data.status == 2) {
				// 弹出验证码
				ready2Submit();
			}
			if (data.status == 3) {
				ready2Submit();
			}
		}
	});
}

// 避免答案丢失
function syncAnswer(questionId, questionType) {
    var isAccessibleCustomFid = "0";
    if (isAccessibleCustomFid == "1") {
        return;
    }

    if (questionType == 4 || questionType == 5 || questionType == 6 || questionType == 7 || questionType == 8 || questionType == 18) {
        var itemEditor = UE.getEditor("answer" + questionId);
        try {
            itemEditor && itemEditor.sync();
        } catch (ex) {
            console.log(ex);
        }
    } else if (questionType == 2 || questionType == 9 || questionType == 10) {
        var blankCount = $("input[name=tiankongsize" + questionId + "]").val();
        for (var i = 1; i <= parseInt(blankCount); i++) {
            var itemEditor = UE.getEditor("answerEditor" + questionId + i);
            try {
                itemEditor && itemEditor.sync();
            } catch (ex) {
                console.log(ex);
            }
        }
    }
}

function ready2Submit() {
	var qids = "211691861,211691862,211691863,211691864,211691865,211691866,211691867,211691868,211691869,211691871,";
	$("#questionIds").val(qids);
	var isAccessibleCustomFid = "0";

	var wordNumCheck = true;
	var wordNumCheckQids = "211691861,211691862,211691863,211691864,211691865,211691866,211691867,211691868,211691869,211691871";
	if (wordNumCheckQids.length > 0 && isAccessibleCustomFid != "1") {
		var wordNumCheckQidArr = wordNumCheckQids.split(",");
		for (var i = 0; i < wordNumCheckQidArr.length; i++) {
			var qid = wordNumCheckQidArr[i];
			if (qid.length == 0) {
				continue;
			}
			var type = $("#answertype" + qid).val() || "";

			try {
				var subject = $("#answer" + qid).attr("subject") || 0;
				var grade = $("#answer" + qid).attr("grade") || "";
				var isCollegeWriting = subject == 1 && grade =="十三";
				if (type == 26 && !isCollegeWriting) {
					var questionContent = $("#answer" + qid).val();
					var count = UE.Editor.prototype.pureWordCount(questionContent);
					var minWordCount = $("#answer" + qid).attr("wordminnum") || 0;
					var maxWordCount = $("#answer" + qid).attr("wordmaxnum") || 0;
					if (maxWordCount > 0 && count > maxWordCount) {
						$.toast({
							type: 'failure',
							content: "答案超出教师限制字数，请限定在" + maxWordCount + "字以内"
						});
						wordNumCheck = false;
						break;
					}
					if (minWordCount > 0 && minWordCount > count) {
						$.toast({
							type: 'failure',
							content: "答案字数未达到教师设置的最少字数要求，最少需输入" + minWordCount + "字"
						});
						wordNumCheck = false;
						break;
					}
				} else {
				    if(type == 17){
						continue;
					}
					var optEditor = UE.getEditor("answer" + qid);
					var maxWordCount = optEditor.options.maximumWords;
					var minWordCount = optEditor.options.minimumWords;
					var count = optEditor.pureWordCount();
					if (maxWordCount > 0 && count > maxWordCount) {
						$.toast({
							type: 'failure',
							content: "答案超出教师限制字数，请限定在" + maxWordCount + "字以内"
						});
						wordNumCheck = false;
						break;
					}
					if (minWordCount > 0 && minWordCount > count) {
						$.toast({
							type: 'failure',
							content: "答案字数未达到教师设置的最少字数要求，最少需输入" + minWordCount + "字"
						});
						wordNumCheck = false;
						break;
					}
				}
			} catch (exception) {
			}
		}
	}
	if (!wordNumCheck) {
		return;
	}

	setAllClozeAnswer();
	setReadComprehensionAnswer();
	setProceduralAnswer();
	
	var questionIds = qids.split(",");
	var flag = false;
	var tipStr = "";
	for (var i = 0; i < questionIds.length - 1; i++) {
		var id = questionIds[i];
		var type = $("#answertype" + id).val();
        tipStr =  $("#answertype" + id).parents('.questionLi').attr("typeName");

        syncAnswer(id, type);
		var subject = $("#answer" + id).attr("subject") || 0;
		var grade = $("#answer" + id).attr("grade") || "";
		var isCollegeWriting = subject == 1 && grade =="十三";
        if (type == "0") {
			var answer = $("#answer" + id).val();
			if ( typeof (answer) == "undefined" || removeAllSpace(answer).length == 0) {
				flag = true;
				break;
			}
		} else if (type == "1" || type == "21") {
			var answer = $("#answer" + id).val();
			if ( typeof (answer) == "undefined" || removeAllSpace(answer).length == 0) {
				flag = true;
				break;
			}
		} else if (type == "11" || type == "20") {
			var answer = $("#answer" + id).val();
			if(answer == "" || answer.length == 0){
			    flag = true;
				break;
			}
			var answerArr = JSON.parse(answer);
			for ( var index in answerArr) {
				var content = answerArr[index].content;
				if (content == "" || content.length == 0) {
					flag = true;
					break;
				}
			}
			if (flag) {
				break;
			}
		} else if (type == "13") {
			var answer = $("#answer" + id).val();
			if ( typeof (answer) == "undefined" || removeAllSpace(answer).length == 0) {
				flag = true;
				break;
			}
		} else if (type == "14" || type == "15" || type == "16" || type == "19") {

			var answer = $("#answer" + id).val();
			var answerArr = JSON.parse(answer);
			var answerContent = answerArr[0];
			for ( var key in answerContent) {
				var content = answerContent[key];
				var answerStr = content.answer;
				var innerType = content.type;
				if (innerType && innerType == 2) {
					for ( var index in answerStr) {
						var item = answerStr[index];
						var content = item.content;
						if (content == "" || content.length == 0) {
							flag = true;
							break;
						}
					}
				}
				if (innerType == 3) {
					answerStr = answerStr.toString();
				}
				if (flag || answerStr == "" || answerStr.length == 0) {
					flag = true;
					break;
				}
			}

			if (flag) {
				break;
			}
		} else if (type == "17") {
			try {
				var language = $("#procedural-" + id + " .languageSelect").val();
				if (language == "-1") {
					flag = true;
					break;
				}

				var editor = codeEditors[id];
				var pureTxt = editor.getValue();
				if (pureTxt.length == 0 || pureTxt == "") {
					flag = true;
					break;
				}
			} catch (exception) {}
		} else if (type == "2" || type == "9" || type == "10") {
			var tem = true;
			var blankNum = $("input[name=tiankongsize" + id + "]").val();
			for (var j = 1; j <= parseInt(blankNum); j++) {
				var answer;
				try {
					var optEditor = null;
					if (isAccessibleCustomFid != "1") {
						optEditor = UE.getEditor("answerEditor" + id + j);
					}
					if (optEditor != null) {
						answer = optEditor.getContent();
					}
					if (isAccessibleCustomFid == "1") {
						answer = $("#answerEditor" + id + j).val();
					}
				} catch (exception) {
				}
				if (typeof (answer) == "undefined" || removeAllSpace(answer).length == 0) {
					tem = false;
					break;
				}
			}
			if (!tem) {
				flag = true;
				break;
			}
		} else if (type == "3") {
			var answer = $("#answer" + id).val();
			if ( typeof (answer) == "undefined" || removeAllSpace(answer).length == 0) {
				flag = true;
				break;
			}
		} else if (type == "4" || type == "5" || type == "6" || type == "7" || type == "8" || type == "18"
				|| (type == "26" && isCollegeWriting)) {
			var answer = $("#answer" + id).val();
			var editorAnswer;
			try {
				var optEditor = null;
				if (isAccessibleCustomFid != "1"){
					optEditor = UE.getEditor("answer" + id);
				}
				if (optEditor != null) {
					editorAnswer = optEditor.getContent();
				}
			} catch (exception) {}
			answer = (editorAnswer && editorAnswer.length != 0 ? editorAnswer : answer);
			if ( typeof (answer) == "undefined" || removeAllSpace(answer).length == 0) {
				flag = true;
				break;
			}
		}else if (type == 22) {
			var objectid = $("#answer" + id).attr("objectid");
			if ( typeof (objectid) == "undefined" || removeAllSpace(objectid).length == 0) {
				flag = true;
				break;
			}
		} else if (type == 26 && !isCollegeWriting) {
			var questionContent = $("#answer" + id).val();
			var count = UE.Editor.prototype.pureWordCount(questionContent);
			if (count == 0) {
				flag = true;
				break;
			}
		}
	}
	
	var showTips = "确认提交？";
	if (flag) {
		showTips = "您还有未做完的" + tipStr + "，确认提交吗？";
	}
	workPop(showTips, "提交", "取消", function() {
		submitCheckTimes();
	});
}

var submitLock = 0;
function submitWork() {
	if (submitLock != 0) {
		return false;
	}
	submitLock = 1;
	$.toast({
		type : 'loading',
		time : 0,
		content : "正在提交..."
	});

	confirmSubmitWork();
}

function version(url) {
	url += '&ua=' + clientType();
	url += '&formType=' + getformType();
	url += '&saveStatus=' + 1;
	var re = /&version=(\d+)/;
	var version = 1;
	if (re.test(url)) {
		var arr = re.exec(url);
		version += parseInt(arr[1]);
		url = url.replace(re, '&version=' + version);
	} else {
		url = url + '&version=1';
	}
	return url;
}

function confirmSubmitWork (){
	var ajax_url = version($("#submitForm").attr("action"));
	var ajax_type = $("#submitForm").attr("method");
	var ajax_data = $("#submitForm").serialize();

	$.ajax({
		type : ajax_type,
		url : ajax_url,
		data : ajax_data,
		dataType : "json",
		success : function(data) {
			if ( !data.status) {
				$.toast({
					type : 'failure',
					content : data.msg
				});
				submitLock = 0;
				return false;
			}

			answerContentSave();
			$("#dialogToast").fadeOut();
	
			if (data.stuStatus == 5) {
				workPop("很遗憾，未达到及格线，请重做", "确定", "取消", function() {
					window.location.href = data.backUrl;
				}, function() {
					submitStep();
				});
			} else {
				submitStep();
			}
		}
	});
}

function submitStep() {
	var submitStep = $("#submitStep").val() || 0;
	if (submitStep == 1) {
		doStep();
	} else {
		closeDo();
	}
}

function doStep() {
	var courseId = $("#courseId").val();
	var classId = $("#classId").val();
	var cpi = $("#cpi").val();
	var workId = $("#workId").val();
	var answerId = $("#answerId").val();
	var enc = $("#enc").val();
	location.href = "/mooc-ans/mooc2/work/prompt?courseId=" + courseId + "&classId=" + classId + "&cpi=" + cpi + "&workId="
			+ workId + "&answerId=" + answerId + "&enc=" + enc;
}

function closeDo() {
	window.opener = null;
	window.open('', '_self');
	window.close();
}

var saveLock = false;
function saveWork() {
	if (saveLock) {
		return;
	}
	saveLock = true;

	$.toast({
		type : 'loading',
		time : 0,
		content : "正在保存..."
	});

	$("#questionIds").val("211691861,211691862,211691863,211691864,211691865,211691866,211691867,211691868,211691869,211691871,");
	$("#pyFlag").val("1");

    var qidString = $("#questionIds").val();
    var qidArr = qidString.split(",");
    for (var i = 0; i < qidArr.length - 1; i++) {
        var id = qidArr[i];
        var type = $("#answertype" + id).val();
        syncAnswer(id, type);
    }

			setAllClozeAnswer();
	setReadComprehensionAnswer();
	setProceduralAnswer();

	saveAnswer();
}
function saveAnswer(){
	var ajax_url = $("#submitForm").attr("action") + "&pyFlag=" + $("#pyFlag").val();
	var ajax_type = $("#submitForm").attr("method");
	var ajax_data = $("#submitForm").serialize();
	ajax_url += '&ua=' + clientType();
	ajax_url += '&formType=' + getformType();
	ajax_url += '&saveStatus=' + 1;
	ajax_url += '&version=1';
	$.ajax({
		type : ajax_type,
		url : ajax_url,
		data : ajax_data,
		dataType : "json",
		success : function(data) {
			if (data.status) {
				$.toast({
					type : 'success',
					content : "保存成功！完成后请及时提交作业！"
				});
				ariaSubmitRead(1);
				answerContentSave();
			} else {
				$.toast({
					type : 'failure',
					content : data.msg
				});
				ariaSubmitRead(0);
			}
		},
		complete : function() {
			saveLock = false;
		}
	});
}

function clientType() {
	var userAgent = navigator.userAgent;
	var ua = "pc";
	try {
		if (userAgent.indexOf("ChaoXingStudy") > -1) {
			ua = "app";
		}
	} catch (e) {}

	return ua;
}

function getformType() {
	var form_type = $("#form1").attr('method'); //提交方法 
	if (form_type) {
		return form_type;
	}
	return "post";
}

function removeAllSpace(str, value) {
	return str.replace(/<\s?img[^>]*>/gi, "图片").replace(/<\s?iframe[^>]*>/gi, "录音").replace(/<[^>]+>/g, "").replace(/&nbsp;/ig, "").replace(/\s+/g, "");
}

//点击输入框出现编辑器
$(document).on('click', '.blankInpDiv', function() {
	// try {
	// 	$(this).parent().parent().parent().find(".blankInpDiv").show();
	// 	$(this).parent().parent().parent().find(".blankInpDiv").next().hide();
	// }catch (e) {
	// 	console.log(e)
	// }
	$(".blankInpDiv").show();
	$(".blankInpDiv").next().hide();
	$(this).hide();
	$(this).next().show()
	var id = $(this).next().find("textarea").attr("id");
	UE.getEditor(id).focus();
})

function loadEditorAnswerd(questionId, type) {
	
	var active = 1;
	var isAccessibleCustomFid = "0";
	
	if (type == 2 || type == 9 || type == 10) {
		var blankNum = $("input[name=tiankongsize" + questionId + "]").val();
		for (var j = 1; j <= parseInt(blankNum); j++) {
			var editor = UE.getEditor("answerEditor" + questionId + j);
			var content = editor.getContent();
			if (content.length == 0) {
				active = 0;
				break;
			}
		}
	} else if (type == 11) {
		var answer = $("#answer" + questionId).val();
		var answerArr = JSON.parse(answer);
		for (var index in answerArr) {
			var content = answerArr[index].content;
			if (content.length == 0) {
				active = 0;
				break;
			}
		}
	} else if (type == 13) {
		var answer = $("#answer" + questionId).val();
		if (answer.length == 0 || answer.length < $(".sortSelect" + questionId).size()) {
			active = 0;
		}
	} else if (type == 14) {
		var answer = $("#answer" + questionId).val();
		var answerArr = JSON.parse(answer);
		var answerContent = answerArr[0];
		for (var key in answerContent) {
			var content = answerContent[key];
			var answerStr = content.answer;
			if (answerStr.length == 0) {
				active = 0;
				break;
			}
		}
	} else if (type == 15 || type == 19) {
		$(".readingItem" + questionId).each(function(i, obj) {
			var itemId = $(obj).attr("data");
			var type = $(obj).attr("qtype");
			var itemAnswer = "";
			switch (parseInt(type)) {
				case 0 :
				case 1 : {
					itemAnswer = $("#answer" + itemId).val();
					break;
				}
				case 3 : {
					itemAnswer = $("#answer" + itemId).val();
					break;
				}
				case 2 : {
					var blankNum = $("#blankNum" + itemId).val();
					for (var i = 1; i <= blankNum; i++) {
						var answerEditorId = "answerEditor" + itemId + i;
						if (isAccessibleCustomFid && isAccessibleCustomFid == "1") {
							itemAnswer = $("#" + answerEditorId).val();
						} else {
							itemAnswer = UE.getEditor(answerEditorId).getContent();
						}
						
						if (itemAnswer.length == 0) {
							break;
						}
					}
					break;
				}
				case 4 : {
					var answerEditorId = "answer" + itemId;
					var answerEditorStr;
					if (isAccessibleCustomFid && isAccessibleCustomFid == "1") {
						answerEditorStr = $("#" + answerEditorId).val();
					} else {
						answerEditorStr = UE.getEditor(answerEditorId).getContent();
					}
					itemAnswer = answerEditorStr;
					break;
				}
			}
			
			if(itemAnswer.length == 0) {
				active = 0;
				return false
			}
		});
	} else if (type == 17) {
		var editor = codeEditors[questionId];
		if (editor) {
			var content = editor.getValue();
			if (content.length === 0) {
				active = 0;
			}
		}
	} else if (type == 26) {
		var questionContent = $("#answer" + questionId).val();
		var count = UE.Editor.prototype.pureWordCount(questionContent);
		if (count == 0) {
			active = 0;
		}
	} else {
		var editor = UE.getEditor("answer" + questionId);
		var content = editor.getContent();
		if (content.length == 0) {
			active = 0;
		}
	}
	
	if (active == 1) {
		$("#answerSheet" + questionId).addClass("active");
	} else {
		$("#answerSheet" + questionId).removeClass("active");
	}
}

$(".B-answerCon span").click(function(){
	answerContentChange();
    var answerCon = $(this).parent();
	answerCon.find("span").removeClass("check_answer");
	$(this).addClass("check_answer");
	setBType(answerCon.parents('.bTypeWrap'));
	
	function setBType(bTypeWrap) {
        var answerCons = bTypeWrap.find('.B-answerCon');
        var answer = [];
        answerCons.each(function(index,obj) {
			var choice = $(this).find("span.check_answer").attr("data") || "";
			var item = {"name" : (index + 1), "content" : choice};
			answer.push(item);
        });
        var answerStr = JSON.stringify(answer);
        bTypeWrap.prev().val(answerStr);
		loadAnswerSheet(bTypeWrap.attr("data"),answerStr);
   }
});

function checkEvaluationQuesNum(object){
	var questionWrap = $(object).parent();
	var qid = $(object).attr("qid");
	var selectNum = $('#selectNum' + qid).val() || 0;
	if(selectNum > 0){
		var alreadyCheckedNum = questionWrap.find('.check_answer_dx').length;
		if($(object).find('.check_answer_dx').length == 0){
			if(parseInt(alreadyCheckedNum) >= parseInt(selectNum)){
				 $.toast({type : 'notice',content : '最多选' + selectNum + '项'});
				return false;
			}
		}
	}
	return true;
}
</script>
<!-- 代码高亮开始 start -->
<link type="text/css" href="//mooc1.chaoxing.com/mooc-ans/views/mooc2/ueditor/insertcode/css/default.min.css" rel="stylesheet" />
<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/highlight.min.js" charset="utf-8"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/highlightjs-line-numbers.js" charset="utf-8"></script>
<style>
    pre,code {display: block;background: #F7F8FA;border: 1px solid #E1E3E5; font-size: 14px; border-radius: 4px; padding: 30px 20px; margin: 20px 0; font-family: CXHackSafariFont,CXEmojiFont,CXChineseQuote,-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Tahoma, Arial,Segoe UI,PingFang SC, Hiragino Sans GB,Microsoft YaHei,sans-serif,Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,Noto Color Emoji; }
    pre{overflow-x: auto;margin:.5em 0;padding:.4em .6em;border-radius:8px;background:#f8f8f8;}
    pre.autolinebreak{white-space: normal; }
    code.hljs{background: #F7F8FA;}
    pre code,pre pre{ margin: 0; background: none;border: none;}/*padding: 0;*/
    .code-tool-wrap{padding-bottom: 1em;left: 20px; top: 2px; width: calc(100% - 40px); height: 24px; font-size: 14px; user-select: none;}
    .code-lang{ display:inline-block; color: #ACB4BF; }
    .ans-cc .ans-noborder th,.ans-cc .ans-noborder td{border-color:transparent;}
    .ans-cc pre,.ans-cc code{ position: relative; padding: 0px 20px 30px; margin: 20px 0;border: 1px solid #E1E3E5; border-radius: 4px; color: #474C59; background: #F7F8FA; font-size:14px; font-family: CXHackSafariFont,CXEmojiFont,CXChineseQuote,-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Tahoma, Arial,Segoe UI,PingFang SC, Hiragino Sans GB,Microsoft YaHei,sans-serif,Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,Noto Color Emoji;  }
    .ans-cc pre{overflow: auto}
    .ans-cc pre code,.ans-cc pre pre{ padding: 0; border:none; margin: 0; background: none;border-radius: 0;}
    .ans-cc pre.autolinebreak{white-space: pre-wrap;word-wrap: break-word;}
    .hljs-ln-numbers {text-align: center;color: #ccc;border-right: 1px solid #CCC;vertical-align: top;padding-right: 5px !important;}
    .hljs-ln-code {padding-left: 5px !important;}
    /* 全局去掉表格样式 */
    .ans-cc pre code table tr td {border: solid 0px #cfd4dc;text-align: left}
    /* 作业表格适配，考试表格适配 */
    .ans-cc pre table{table-layout: unset;}
    .ans-cc pre code table{table-layout: unset;}
    /* pre 标签未换行 */
    .ans-cc pre.line-numbers{clear:both}
    .ans-cc pre{clear:both}

    /* app作业库 考试库 查看样式 */

    .hljs-link, .hljs-operator, .hljs-regexp, .hljs-selector-attr, .hljs-selector-pseudo, .hljs-symbol, .hljs-template-variable, .hljs-variable {
        color: #ab5656 !important;
    }
    .hljs-deletion, .hljs-number, .hljs-quote, .hljs-selector-class, .hljs-selector-id, .hljs-string, .hljs-template-tag, .hljs-type {
        color: #800 !important;
    }
    /* 章节作业 */
    .hljs-ln span{margin-right:0}

    /* 学习通颜色覆盖 */
    .hljs-type{color:#800 !important;}
    .hljs-section,.hljs-title{color:#800 !important;font-weight:700}
    /* 背景颜色 */
    .ans-cc pre code td{background-color: unset;}
</style>
<link type="text/css" href="//mooc1.chaoxing.com/mooc-ans/css/newEditor.css?v=2023-0609-1900" rel="stylesheet" />
<script>
    /**
     * 保证最后加载
     */
    $(function () {
        var pre = $(".ans-cc").find('pre')
        if(pre){
            pre.each(function () {
                // debugger
                var me = this;
                if ($(me).hasClass('CodeMirror-line')) { //代码编辑器
                    return;
                }
                var lang = me.firstChild && me.firstChild.lang;
                if (typeof (lang) == 'undefined' || lang == null || lang == "" ) {
                    lang = me.firstChild && me.firstChild.className;
                    if (typeof (lang) != 'undefined' && lang != null && lang != "" ) {
                        var split = lang.split("-");
                        lang = split[1];
                        if (lang == 'plain') {
                            lang = 'Plain Text';
                        }
                    } else {
                        lang = 'Plain Text';
                    }
                }
                me.innerHTML = '<div class="code-tool-wrap clearfix"><div class="code-lang fl">' + lang
                        + '</div><div class="copy1 fr"><i></i><span></span></div></div>' + me.innerHTML;
                me.innerHTML = me.innerHTML.replace(/<br\s*\/?>/g, "\n");
            })
        }

        try {
            hljs.initHighlightingOnLoad();
            hljs.initLineNumbersOnLoad({ singleLine:true });
        } catch (e) {

        }
    })
</script>
<!-- 代码高亮结束 end --><script type="text/javascript">
	// 作业结尾下次tab键聚焦到保存按钮
	function toSubmitFocus() {
		try {
			// 非无障碍单位不处理
			if("0" != "1"){
				return;
			}
			if(event.keyCode == 9  && !event.shiftKey){
				$("#ariaHtmlEnd").attr('aria-label', '作业作答结尾');
				$("#submitFocus").focus();
				var findAElement = $("#submitFocus").find('a')[0];
				if (findAElement) {
					$(findAElement).attr("onkeydown","backHtmlEndFocus()")
				}
			}
		} catch (e) {}
	}

	// shift+tab聚焦到作业结尾
	function backHtmlEndFocus() {
		try {
			// 非无障碍单位不处理
			if("0" != "1"){
				return;
			}
			if(event.keyCode == 9 && event.shiftKey){
				$("#ariaHtmlEnd").focus();
				$("#ariaHtmlEnd").attr('aria-label', '作业作答结尾');
			}
		} catch (e) {}
	}
	
	// 保存后无障碍读取处理
	function ariaSubmitRead(type) {
		// 非无障碍单位不处理
		if("0" != "1"){
			return;
		}
		if (type == 1) {
			$("#ariaHtmlEnd").attr('aria-label', '保存成功');
		} else {
			$("#ariaHtmlEnd").attr('aria-label', '保存失败');
		}
		$("#ariaHtmlEnd").focus();
		setTimeout(function() {
			$("#ariaHtmlEnd").attr('aria-label', '作业作答结尾');
		},500);
	}
	
	// 无障碍单选处理,已选中后,再次回车空格还是选中功能实现
	function addChoiceKeyDown(obj) {
		if('0' == 1 && (event.keyCode == 13 || event.keyCode == 32)) {
			event.preventDefault();
			if ($(obj).find(".num_option").hasClass("check_answer")) {
				return;
			}
			addChoice(obj);
		}
	}
	// 多障碍单选处理
	function addMultipleChoiceKeyDown(obj) {
		if('0' == 1 && (event.keyCode == 13 || event.keyCode == 32)) {
			event.preventDefault();
			addMultipleChoice(obj);
		}
	}

</script>
<script type="text/javascript">
		window.addEventListener('beforeunload', function(event) {
		if (isContentChanged) {
			var closeTip =  '系统可能不会保存您所做的更改，确定离开当前页面吗？';
			event.preventDefault();
			event.returnValue = closeTip;
			return closeTip;
		}
		return null;
	});
		function answerContentChange() {
		isContentChanged = true;
	}
	function answerContentSave() {
		isContentChanged = false;
	}
</script>
</body>
</html>