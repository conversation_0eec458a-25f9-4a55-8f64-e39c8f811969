---
description: 学习通自动化系统架构设计规范
globs: ["**/*.py"]
alwaysApply: true
---
# 学习通自动化系统架构设计规范

## 系统概述
学习通自动化系统采用分层架构设计，支持多平台、多任务类型的自动化处理。系统具有高并发、强容错、易扩展的特点。

## 架构层次

### 1. 表示层 (Presentation Layer)
- **主入口文件**: `学习通跑单启动文件.py`
- **职责**: 系统启动、线程管理、订单调度
- **关键组件**: ThreadManager, order_get(), Run()

### 2. 业务逻辑层 (Business Logic Layer)
- **目录**: `API/`
- **核心模块**:
  - `Session.py` - 会话管理和反爬处理
  - `TaskDo.py` - 任务分发处理器
  - `HomeworkAI.py` - AI自动答题系统
  - `VideoTask.py` - 视频任务处理
  - `WorkTask.py` - 作业和测验处理

### 3. 数据访问层 (Data Access Layer)
- **目录**: `data/`, `Config/`
- **核心模块**:
  - `Porgres.py` - 进度跟踪和状态管理
  - `Exam.py` - 考试数据处理
  - `UserSql.py` - 数据库连接池管理

## 设计模式应用

### 1. 工厂模式 (Factory Pattern)
```python
class ThreadManager:
    @staticmethod
    def register_thread(oid, thread):
        # 线程工厂管理
        pass
```

### 2. 策略模式 (Strategy Pattern)
```python
def task(self):
    if self.type == "video":
        self.VideoTask()
    elif self.type == "workid":
        self.WorkTask()
    elif self.type == "assignment":
        self.AssignmentTask()
```

### 3. 装饰器模式 (Decorator Pattern)
```python
class StartSession:
    def __init__(self, session):
        self.session = session  # 装饰原始session
        # 添加反爬虫、验证码处理等功能
```

## 编码规范

### 1. 异常处理规范
```python
def handle_error(self, error_msg, update_status=True):
    """统一处理错误，支持重试机制"""
    self.retry_count += 1
    if self.retry_count <= self.max_retries:
        logger.warning(f"ID:{self.username}, 遇到错误: {error_msg}, 第{self.retry_count}次重试...")
        time.sleep(2 * self.retry_count)  # 指数退避
        return False  # 继续重试
    else:
        logger.error(f"ID:{self.username}, 重试{self.max_retries}次后失败")
        return True  # 停止重试
```

### 2. 日志记录规范
```python
# 使用loguru进行结构化日志
logger.info(f"ID:{self.username},开始处理任务")
logger.success(f"ID:{self.username},任务完成")
logger.warning(f"ID:{self.username},遇到警告: {warning_msg}")
logger.error(f"ID:{self.username},处理失败: {error_msg}")
```

### 3. 线程安全规范
```python
# 使用锁保护共享资源
with thread_lock:
    active_threads[oid] = thread

# 账号级锁防止冲突
def account_lock(username, cid):
    key = f"{username}_{cid}"
    return account_locks[key].acquire(blocking=False)
```

## 平台适配规范

### 1. 平台ID处理
```python
# 针对不同平台ID的特殊处理
if self.cid == 9004:
    # 平台9004特殊逻辑
    pass
elif self.cid == 9003:
    # 平台9003特殊逻辑
    pass
else:
    # 通用处理逻辑
    pass
```

### 2. 参数格式处理
```python
# 支持多种参数格式
if "|" in self.courseid:
    # 竖线分隔格式处理
    parts = self.courseid.split("|")
elif "_" in self.courseid:
    # 下划线分隔格式处理
    parts = self.courseid.split("_")
```

## 性能优化规范

### 1. 连接池配置
```python
self.mysql_pool = PooledDB(
    creator=pymysql,
    maxconnections=15,  # 根据服务器性能调整
    autocommit=True,
    cursorclass=pymysql.cursors.DictCursor
)
```

### 2. 并发控制
```python
MAX_WORKERS = 100  # 最大线程数
# 动态调整可用线程槽位
available_slots = MAX_WORKERS - active_count
```

### 3. 内存管理
```python
# 及时清理已完成的任务
done_oids = [oid for oid, future in futures.items() if future.done()]
for oid in done_oids:
    del futures[oid]
```

## 安全规范

### 1. 数据加密
```python
def encrypt_by_aes(message):
    key = "u2oh6Vu^HWe4_AES"
    cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, key.encode("utf-8"))
    padded_data = pad(message.encode("utf-8"), AES.block_size)
    encrypted_data = cipher.encrypt(padded_data)
    return b64encode(encrypted_data).decode("utf-8")
```

### 2. SQL注入防护
```python
# 对用户输入进行转义
error_msg = str(e).replace("'", '"')
if len(error_msg) > 200:
    error_msg = error_msg[:197] + "..."
```

### 3. 会话安全
```python
# 禁用SSL验证（适应学习通环境）
self.session.verify = False
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
```

## 扩展性规范

### 1. 新任务类型添加
```python
# 在TaskDo.py中添加新的任务处理器
if self.type == "new_task_type":
    self.NewTaskHandler()
```

### 2. 新平台支持
```python
# 在订单查询中添加新的平台ID
WHERE cid IN (9000,9001,9002,9003,9004,NEW_PLATFORM_ID)
```

### 3. 模块化设计
- 每个功能模块独立，便于单独测试和维护
- 使用依赖注入减少模块间耦合
- 提供清晰的接口定义

## 测试规范

### 1. 单元测试
```python
def test_encrypt_by_aes():
    message = "test_message"
    encrypted = encrypt_by_aes(message)
    assert len(encrypted) > 0
    assert encrypted != message
```

### 2. 集成测试
```python
def test_login_flow():
    session = StartSession(requests.session())
    main_xxt = MainXxt(session, school, username, password, courseid, oid, cid, pool)
    result = main_xxt.fanyalogin()
    assert result is True
```

## 部署规范

### 1. 环境要求
- Python 3.8+
- MySQL 5.7+
- 内存: 最少2GB，推荐4GB+
- 网络: 稳定的互联网连接

### 2. 配置管理
- 数据库连接信息
- 线程池大小配置
- 日志级别设置
- 平台ID支持列表

### 3. 监控指标
- 活跃线程数量
- 任务处理成功率
- 数据库连接池状态
- 内存使用情况
