import requests

url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew?_classId=91297170&courseid=229349196&token=763d80fe634005db95b0596302aa819a&totalQuestionNum=fa7b891349fa608eae0b751926d519c9&ua=pc&formType=post&saveStatus=1&version=1&tempsave=1"

payload = {
  'pyFlag': "1",
  'courseId': "229349196",
  'classId': "91297170",
  'api': "1",
  'workAnswerId': "53963883",
  'answerId': "53963883",
  'totalQuestionNum': "fa7b891349fa608eae0b751926d519c9",
  'fullScore': "100.0",
  'knowledgeid': "670797765",
  'oldSchoolId': "",
  'oldWorkId': "a8ad3f5a38444685819417aeda819434",
  'jobid': "work-a8ad3f5a38444685819417aeda819434",
  'workRelationId': "32036466",
  'enc': "",
  'enc_work': "763d80fe634005db95b0596302aa819a",
  'userId': "221880762",
  'cpi': "282371395",
  'workTimesEnc': "",
  'randomOptions': "false",
  'isAccessibleCustomFid': "0",
  'answer402241887': "A",
  'answertype402241887': "0",
  'answer402241888': "B",
  'answertype402241888': "0",
  'answer402241889': "B",
  'answertype402241889': "0",
  'answer402241890': "BCE",
  'answertype402241890': "1",
  'answer402241891': "BCE",
  'answertype402241891': "1",
  'answerwqbid': "402241887,402241888,402241889,402241890,402241891,"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  'Accept': "application/json, text/javascript, */*; q=0.01",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'sec-ch-ua-platform': "\"Windows\"",
  'X-Requested-With': "XMLHttpRequest",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'Origin': "https://mooc1.chaoxing.com",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Referer': "https://mooc1.chaoxing.com/mooc-ans/work/doHomeWorkNew?courseId=229349196&workId=32036466&api=1&knowledgeid=670797765&classId=91297170&oldWorkId=a8ad3f5a38444685819417aeda819434&jobid=work-a8ad3f5a38444685819417aeda819434&type=&isphone=false&enc=7da22c092a47ef4e39b124874055bccb&cpi=282371395&mooc2=1&skipHeader=true&originJobId=work-a8ad3f5a38444685819417aeda819434&fromType=",
  'Accept-Language': "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  'Cookie': "fid=2790; xxtenc=c4885b4f5d02c8246c0260b0959ca202; tl=1; k8s=1753595024.103.94.10338; route=ac9a7739314fa6817cbac7e56032374b; _uid=221880762; _d=1753595224521; UID=221880762; vc3=LZqkq4n%2FmbGW9ojvfKbVwkeWrCvt43v6TssVxQiTuRb8lR0H%2Fr%2Fcl5i7YLP5Unr2sNfaaRoXSbf99DNlm02jyNK0e9S4SgBZq47RYkeUdzXJ8YKM%2B38jmDttyAILT39MP8BatFW7KysFHFP%2FbxlhG8i3%2Fz3un7Hv80WHD1ztjNA%3D9e67a08e20826a7e7b55cbfc79254d9c; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f9908c5484245322b1b4a51eca42191e4c281a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc04b3f0f3044052d31d0162f79a878cce2d78753423d2e6ab386902898d8d6fb7f825025e192a05e94df7ff280fcb29d10d8a4c92b12beb4b67de9b6e0f51cc5d5c91709759e5c36668a63fc9f8959a8ee7fafd565af53bf2; cx_p_token=4d337d0fc5dda05af0b598f2d53b0fe0; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTM1OTUyMjQ1MjIsImV4cCI6MTc1NDIwMDAyNH0.KBG5_sbS0AIza9qW-JGyQzWTkYHMT_HxDTrBcFvO91Y; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1753595224523; jrose=1130147DD05DB330779CCC28A1522EA5.mooc-3888860364-7pjnt; spaceFid=2790; spaceRoleId=3"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)