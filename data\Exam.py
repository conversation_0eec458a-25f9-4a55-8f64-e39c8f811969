import difflib
import json
import random
import time
import traceback
import parsel
import py_mini_racer
from collections import OrderedDict
from bs4 import BeautifulSoup
from datetime import datetime
from ddddocr import DdddOcr
from loguru import logger
from API.Re import strip_title, strip_options
from urllib.parse import urlparse, parse_qs
from API.Questionbank import *

# 尝试导入HomeworkAI，如果失败则设置标志
try:
    from API.HomeworkAI import HomeworkAI
    HOMEWORK_AI_AVAILABLE = True
except ImportError:
    HOMEWORK_AI_AVAILABLE = False


class ExamTimeoutException(Exception):
    """考试时间已用完异常"""
    pass


def clean_question_text(question_text):
    """
    清理题目文本，移除题型、分数、序号等信息，用于题库查询
    """
    import re

    if not question_text:
        return question_text

    clean_text = question_text

    # 移除题目序号（如"1."、"2."等）
    clean_text = re.sub(r'^\s*\d+[\.\、\:：]\s*', '', clean_text)

    # 移除题目类型标识（如"(单选题, 1.0 分)"、"（多选题, 2.0 分）"等）
    clean_text = re.sub(r'\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*\s*分?\s*[\)）\]\】]?\s*', '', clean_text)

    # 移除单独的分数标识（如"(1.0 分)"、"（2.0分）"等）
    clean_text = re.sub(r'\s*[\(（]\s*\d+\.\d+\s*分\s*[\)）]\s*', '', clean_text)

    # 移除开头的"分)"字符（可能是清理后的残留）
    clean_text = re.sub(r'^\s*分\s*[\)）]\s*', '', clean_text)

    # 合并多个空格为一个，移除首尾空格
    clean_text = re.sub(r'\s+', ' ', clean_text)
    clean_text = clean_text.strip()

    return clean_text


def t():
    return int(time.time() * 1000)


def time_s(target_time_str):
    for date_format in ("%m-%d %H:%M", "%Y-%m-%d %H:%M"):
        try:
            target_time = datetime.strptime(target_time_str, date_format)
            current_time_str = datetime.now().strftime(date_format)
            current_time = datetime.strptime(current_time_str, date_format)
            if current_time > target_time:
                comparison = "大于"
            elif current_time < target_time:
                comparison = "小于"
            else:
                comparison = "等于"
            if comparison == "大于":
                return True
            return False
        except ValueError:
            continue
    return False


def get_q(i2, q):
    a = 0
    b = 0
    qlist = ["(1.0)", "(2.0)", "(3.0)", "（1.0）", "（2.0）", "（3.0）"]
    for i in qlist:
        b += 1
        if i in q:
            a = 1
            break
    if a == 1:
        a = 0
        for i in q[::-1]:
            a += 1
            if b > 3:
                if i == "（":
                    q = q[:-a]
                    break
            else:
                if i == "(":
                    q = q[:-a]
                    break
    try:
        if i2.find("h3", {"class": "mark_name colorDeep"}).find_all("img", src=True):
            q += "".join(
                [
                    img["src"]
                    for img in i2.find("h3", {"class": "mark_name colorDeep"}).find_all(
                        "img", src=True
                    )
                    if img.get("src")
                ]
            )
    except:
        pass
    return q


def get_a(answer):
    order = {"A": 1, "B": 2, "C": 3, "D": 4, "E": 5, "F": 6, "G": 7}

    def custom_sort(x):
        return order.get(x, 0)

    sorted_str = "".join(sorted(answer, key=custom_sort))
    return sorted_str


class EXAM:
    def __init__(self, session, username, list_info, open=1):
        self.session = session
        self.open = open
        self.username = username
        self.kcname = list_info[0]["kcname"]
        self.clazzid = list_info[0]["clazzid"]
        self.cpi = list_info[0]["cpi"]
        self.courseid = list_info[0]["courseid"]

        # 记录考试开始时间，用于计算耗时
        import datetime
        self.start_time = datetime.datetime.now()

        # 添加题目数量跟踪
        self.total_questions = 0
        self.answered_questions = 0

    def get_data(self):
        res = self.session.get(
            f"https://stat2-ans.chaoxing.com/stat2/exam-stastics/stu-exams?clazzid={self.clazzid}&courseid={self.courseid}&cpi={self.cpi}&ut=s&pEnc=&page=1&pageSize=99&personId="
        ).json()
        a = 0
        for i in res["data"]:
            title = i["titleAll"]
            self.examtid = i["wrId"]
            self.examid = i["examRelationAnswerId"]
            if i["statusStr"] == "待解答":
                if time_s(i["startTime"]):
                    logger.success(
                        f"ID：{self.username},考试名称:{title} - 准备进行答题"
                    )
                    # 保存考试名称，用于异常处理时生成备注
                    self.exam_title = title
                    try:
                        html = self.OpenExam()
                        self.Do(html)
                        a = 1
                    except ExamTimeoutException:
                        # 考试时间已用完，重新抛出异常让上层处理
                        logger.warning(f"ID:{self.username},考试 {title} 时间已用完")
                        raise
        if a == 1:
            time.sleep(random.randint(10, 15))
            return True
        return False

    def Captcha(self):
        with open("data/Captcha.js", "r", encoding="utf-8") as fp:
            js_code = fp.read()
        ctx = py_mini_racer.MiniRacer()
        ctx.eval(js_code)
        res = BeautifulSoup(
            self.session.get(
                f"https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={self.courseid}&classId={self.clazzid}&examId={self.examtid}&cpi={self.cpi}"
            ).text,
            "html.parser",
        )
        CaptchaId = res.find("input", {"id": "captchaCaptchaId"}).get("value").strip()
        self.openid = parse_qs(
            urlparse(
                res.find("div", {"class": "next_btn_div fr pos_bottom"})
                .find("a")
                .get("data")
            ).query
        ).get("openc", [None])[0]
        _ = json.loads(
            self.session.get(
                f"https://captcha.chaoxing.com/captcha/get/conf?callback=cx_captcha_function&captchaId={CaptchaId}&_={t()}",
                proxies={},
            )
            .text.replace("cx_captcha_function(", "")
            .replace(")", "")
        )["t"]
        logger.debug(f"ID:{self.username},获取到验证码配置参数: {_}")
        result = ctx.call("GetCaptcha", _, CaptchaId)
        res = json.loads(
            self.session.get(
                "https://captcha.chaoxing.com/captcha/get/verification/image",
                params={
                    "callback": "cx_captcha_function",
                    "captchaId": CaptchaId,
                    "type": "slide",
                    "version": "1.1.20",
                    "captchaKey": result["captchaKey"],
                    "token": result["token"],
                    "referer": "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes",
                    "iv": result["IV"],
                    "_": _,
                },
                proxies={},
            ).text[20:][:-1]
        )
        img1 = self.session.get(
            res["imageVerificationVo"]["cutoutImage"], proxies={}
        ).content
        img2 = self.session.get(
            res["imageVerificationVo"]["shadeImage"], proxies={}
        ).content
        x = DdddOcr(show_ad=False).slide_match(img1, img2, simple_target=True)[
            "target"
        ][0]
        resp = json.loads(
            self.session.get(
                "https://captcha.chaoxing.com/captcha/check/verification/result",
                params={
                    "callback": "cx_captcha_function",
                    "captchaId": CaptchaId,
                    "type": "slide",
                    "token": res["token"],
                    "textClickArr": '[{"x":' + str(x) + "}]",
                    "coordinate": "[]",
                    "runEnv": "10",
                    "version": "1.1.20",
                    "t": "a",
                    "iv": result["IV"],
                    "_": _,
                },
                headers={
                    "Host": "captcha.chaoxing.com",
                    "Referer": "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes",
                },
                proxies={},
            ).text[20:][:-1]
        )
        if resp["result"]:
            v = json.loads(resp["extraData"])["validate"]
            logger.success(
                f"ID:{self.username},考试滑块距离计算正确:{x} 滑块验证码返回:{v}"
            )
            return v
        logger.debug(f"ID:{self.username},考试滑块距离计算错误 正在准备重新精准计算")
        time.sleep(random.randint(1, 2))
        return self.Captcha()

    def GetEnc(self, v):
        res = self.session.get(
            "https://mooc1.chaoxing.com/exam-ans/exam/test/examcheck",
            params={
                "view": "json",
                "answerId": self.examid,
                "examId": self.examtid,
                "classId": self.clazzid,
                "courseId": self.courseid,
                "cpi": self.cpi,
                "code": "",
                "sdlkey": "",
                "facekey": "",
                "captchavalidate": v,
                "_signcode": "",
            },
        ).json()["enc"]
        return res

    def OpenExam(self):
        try:
            v = self.Captcha()
        except Exception as e:
            logger.error(f"ID:{self.username},滑块验证失败: {str(e)}")
            traceback.print_exc()
            raise e  # 重新抛出异常，避免继续执行
        open_enc = self.GetEnc(v)
        params = {
            "courseId": self.courseid,
            "classId": self.clazzid,
            "tId": self.examtid,
            "id": self.examid,
            "tag": "1",
            "enc": open_enc,
            "cpi": self.cpi,
            "openc": self.openid,
            "newMooc": "true",
        }
        resop = BeautifulSoup(
            self.session.get(
                "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew",
                params=params,
            ).text,
            "html.parser",
        )
        reszheng = self.session.get(
            "https://mooc1.chaoxing.com/exam-ans/mooc2/exam/preview",
            params={
                "courseId": self.courseid,
                "classId": self.clazzid,
                "start": "0",
                "cpi": self.cpi,
                "examRelationId": self.examtid,
                "examRelationAnswerId": self.examid,
                "newMooc": "true",
                "openc": "",
                "monitorStatus": "0",
                "monitorOp": "-1",
                "remainTimeParam": resop.find("input", {"id": "remainTime"}).get(
                    "value"
                ),
                "relationAnswerLastUpdateTime": resop.find(
                    "input", {"id": "encLastUpdateTime"}
                ).get("value"),
                "enc": resop.find("input", {"id": "enc"}).get("value"),
            },
        ).text
        return reszheng

    def post_cs(self, html):
        txt = BeautifulSoup(html, "html.parser")
        self.testPaperId = txt.find("input", {"id": "testPaperId"}).get("value")
        self.examCreateUserId = txt.find("input", {"id": "examCreateUserId"}).get(
            "value"
        )
        self.testUserRelationId = txt.find("input", {"id": "testUserRelationId"}).get(
            "value"
        )
        self.encRemainTime = txt.find("input", {"id": "encRemainTime"}).get("value")
        self.encLastUpdateTime = txt.find("input", {"id": "encLastUpdateTime"}).get(
            "value"
        )
        self.enc = txt.find("input", {"id": "enc"}).get("value")
        self.examRelationId = txt.find("input", {"id": "examRelationId"}).get("value")
        self.enterPageTime = txt.find("input", {"id": "enterPageTime"}).get("value")

    def Do(self, html):
        self.post_cs(html)

        # 统计题目总数
        questions = parsel.Selector(html).xpath("//div[@class='whiteDiv']/div")
        self.total_questions = len(questions)
        logger.info(f"ID:{self.username},题目总数: {self.total_questions}")

        for js, i in enumerate(questions):
            self.s = (
                "true"
                if js
                < len(parsel.Selector(html).xpath("//div[@class='whiteDiv']/div")) - 1
                else "false"
            )
            i2 = BeautifulSoup(i.get(), "html.parser")
            self.qid = i.xpath("@data").get()
            self.qtp = int(
                i2.find("input", {"name": f"type{self.qid}"}).get("value").strip()
            )
            q = strip_title(
                i2.find("h3", {"class": "mark_name colorDeep"}).text
            ).replace(
                strip_title(
                    i2.find("span", {"class": "colorShallow"}).get("aria-label")
                ),
                "",
            )
            self.question = get_q(i2, q)

            # 清理题目文本用于题库查询（移除题型、分数、序号等信息）
            clean_question = clean_question_text(self.question)

            # 调用题库查询，传递清理后的题目文本和题型参数
            # 为平台ID9006设置20秒超时时间（进一步优化）
            timeout = 20 if hasattr(self.session, 'cid') and self.session.cid == 9006 else 10

            # 平台ID9006添加性能监控
            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                query_start_time = time.time()

            # 平台ID9006增加重试机制
            max_retries = 2 if hasattr(self.session, 'cid') and self.session.cid == 9006 else 1
            answer = None
            self.qnum = 2

            for retry in range(max_retries):
                try:
                    answer, self.qnum = questionbank(clean_question, self.qtp, timeout=timeout, platform_id=getattr(self.session, 'cid', None))
                    if answer:  # 如果获取到答案，跳出重试循环
                        break
                    elif retry < max_retries - 1:  # 如果没有答案且还有重试机会
                        if hasattr(self.session, 'cid') and self.session.cid == 9006:
                            logger.info(f"ID:{self.username},题库查询无结果，第{retry + 1}次重试...")
                        time.sleep(1)  # 短暂等待后重试
                except Exception as e:
                    if retry < max_retries - 1:
                        if hasattr(self.session, 'cid') and self.session.cid == 9006:
                            logger.warning(f"ID:{self.username},题库查询异常，第{retry + 1}次重试: {str(e)}")
                        time.sleep(2)  # 异常后等待更长时间
                    else:
                        if hasattr(self.session, 'cid') and self.session.cid == 9006:
                            logger.error(f"ID:{self.username},题库查询重试失败: {str(e)}")
                        answer = None
                        self.qnum = 2

            # 平台ID9006记录查询性能
            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                query_duration = time.time() - query_start_time
                if answer:
                    logger.debug(f"ID:{self.username},题库查询成功，耗时: {query_duration:.2f}秒")
                else:
                    logger.debug(f"ID:{self.username},题库查询无结果，耗时: {query_duration:.2f}秒")
            self.ans = answer
            if self.qtp == 0 or self.qtp == 1:
                Alist, Xlist = self.Answers(i2, answer)
                # print(Alist,Xlist)

                # 平台ID9006：检查是否使用了默认答案，如果是则跳过题目
                if hasattr(self.session, 'cid') and self.session.cid == 9006:
                    if Alist == "答案匹配失败" or Alist == "AI答题失败":
                        logger.warning(f"ID:{self.username},未找到可靠答案，跳过题目（不作答）")
                        return  # 直接返回，不调用preview()，不增加answered_questions计数

                ans = "".join(list(OrderedDict.fromkeys(get_a(Xlist))))
                self.preview(ans)
            elif self.qtp == 2:
                # 填空题处理逻辑，不需要调用Answers方法（因为填空题没有选项）
                if answer and answer != "未收录答案":
                    # 题库找到答案，直接使用（questionbank函数已经包含了主题库和备用题库查询）
                    logger.success(f"ID:{self.username},题库填空题答案: {answer}")
                    ans = self._format_fill_answer(answer)
                    self.preview(ans)
                else:
                    # 题库没有答案，尝试AI答题（避免重复查询题库）
                    logger.info(f"ID:{self.username},题库未找到填空题答案，尝试AI答题")
                    try:
                        ai_answer = self._get_ai_answer_for_exam(self.question, self.qtp, i2)
                        if ai_answer:
                            # 平台ID9006输出简化的题目和答案信息
                            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                logger.info(f"ID:{self.username},题目: {self.question[:50]}...")
                                logger.info(f"ID:{self.username},答案: {ai_answer}")
                            else:
                                logger.success(f"ID:{self.username},AI生成填空题答案: {ai_answer}")
                            ans = self._format_fill_answer(ai_answer)
                            self.preview(ans)
                        else:
                            # 平台ID9006：AI答题失败时跳过题目，不使用空答案
                            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                logger.warning(f"ID:{self.username},AI答题失败，跳过填空题（不作答）")
                                return  # 直接返回，不调用preview()，不增加answered_questions计数
                            else:
                                logger.warning(f"ID:{self.username},AI答题失败，填空题使用空答案")
                                ans = ""
                                self.preview(ans)
                    except Exception as e:
                        # 平台ID9006：AI答题异常时跳过题目，不使用空答案
                        if hasattr(self.session, 'cid') and self.session.cid == 9006:
                            logger.error(f"ID:{self.username},AI答题异常，跳过填空题（不作答）: {str(e)}")
                            return  # 直接返回，不调用preview()，不增加answered_questions计数
                        else:
                            logger.error(f"ID:{self.username},AI答题异常: {str(e)}")
                            ans = ""
                            self.preview(ans)
            elif self.qtp == 3:
                # 判断题处理逻辑，调用Answers方法以触发AI答题fallback机制
                Alist, Xlist = self.Answers(i2, answer)

                # 平台ID9006：检查是否使用了默认答案，如果是则跳过题目
                if hasattr(self.session, 'cid') and self.session.cid == 9006:
                    if Alist == "答案匹配失败" or Alist == "AI答题失败":
                        logger.warning(f"ID:{self.username},未找到可靠答案，跳过判断题（不作答）")
                        return  # 直接返回，不调用preview()，不增加answered_questions计数

                if Xlist:
                    ans = Xlist[0] if Xlist else "false"
                else:
                    ans = "false"
                self.preview(ans)
            # 不需要每题都等待，只在最后一题提交时等待
            if self.s == "false":
                time.sleep(0.5)
            else:
                time.sleep(0.05)

    def preview(self, answer):
        # 增加已答题数计数
        self.answered_questions += 1

        # 根据题型确定参数名称和格式
        if self.qtp == 1:  # 多选题
            ans = f"answers{self.qid}"
            answer_data = {ans: answer}
        elif self.qtp == 2:  # 填空题 - 特殊处理（平台ID9006需要特殊格式）
            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                # 平台ID9006使用特殊的填空题格式
                ans = f"answerEditor{self.qid}1"
                formatted_answer = f"<p>{answer}</p>" if answer else "<p></p>"
                answer_data = {
                    ans: formatted_answer,
                    f"blankNum{self.qid}": "1,",
                    f"typeName{self.qid}": "填空题"
                }
                logger.debug(f"ID:{self.username},平台ID9006填空题参数: {ans}={formatted_answer}")
            else:
                # 其他平台使用原有格式
                ans = f"answer{self.qid}"
                answer_data = {ans: answer}
        else:  # 单选题、判断题等
            ans = f"answer{self.qid}"
            answer_data = {ans: answer}
        # if self.s == 'false':
        #     time.sleep(random.randint(600,900))
        # 构造基础数据
        base_data = {
            "courseId": self.courseid,
            "testPaperId": self.testPaperId,
            "examCreateUserId": self.examCreateUserId,
            "feedbackEnc": "",
            "testUserRelationId": self.testUserRelationId,
            "classId": self.clazzid,
            "type": "0",
            "remainTime": "",
            "tempSave": self.s,
            "timeOver": "false",
            "encRemainTime": self.encRemainTime,
            "encLastUpdateTime": self.encLastUpdateTime,
            "enc": self.enc,
            "userId": "",
            "cpi": self.cpi,
            "examRelationId": self.examRelationId,
            "enterPageTime": self.enterPageTime,
            "exitdtime": "0",
            "monitorforcesubmit": "0",
            f"type{self.qid}": self.qtp,
            "questionId": self.qid,
            "start": "0",
        }

        # 合并答案数据
        base_data.update(answer_data)

        # 平台ID9006使用不同的提交URL和参数
        if hasattr(self.session, 'cid') and self.session.cid == 9006:
            # 平台ID9006使用reVersionSubmitTestNew接口
            submit_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionSubmitTestNew"
            # 需要添加额外的参数
            base_data.update({
                "paperId": getattr(self, 'paperId', ''),
                "examCreateUserId": getattr(self, 'examCreateUserId', ''),
                "examsystem": "0",
                "randomOptions": "false",
                "openc": getattr(self, 'openc', ''),
                "answeredView": "0",
                "paperGroupId": "0",
                "subCount": "",
                f"score{self.qid}": "3.0",  # 默认分数，可以根据实际情况调整
            })
            referer = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"
        else:
            # 其他平台使用原有接口
            submit_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/preview-save"
            referer = "https://mooc1.chaoxing.com/exam-ans/mooc2/exam/preview"

        res = self.session.post(
            submit_url,
            data=base_data,
            headers={
                "Referer": referer
            },
        ).text
        logger.info(
            f"ID:{self.username},question:{self.question},answer:{self.ans},status:{res}"
        )

        # 检查是否考试时间已用完或限时提交错误
        try:
            res_json = json.loads(res)
            if res_json.get("msg") == "考试时间已用完,不允许提交答案!":
                logger.warning(f"ID:{self.username},考试时间已用完，停止答题")
                # 抛出自定义异常，用于在上层处理
                raise ExamTimeoutException("考试时间已用完,不允许提交答案!")
            elif "限时提交" in res_json.get("msg", "") and res_json.get("status") == "error":
                logger.warning(f"ID:{self.username},限时提交错误: {res_json.get('msg')}")
                # 抛出自定义异常，用于在上层处理
                raise ExamTimeoutException(res_json.get("msg"))
        except json.JSONDecodeError:
            # 如果不是JSON格式，检查文本内容
            if "考试时间已用完,不允许提交答案!" in res:
                logger.warning(f"ID:{self.username},考试时间已用完，停止答题")
                raise ExamTimeoutException("考试时间已用完,不允许提交答案!")
        except ExamTimeoutException:
            # 重新抛出自定义异常
            raise

    def Answers(self, html, answer, bd=0.95):
        # 平台ID9006不输出调试日志
        if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
            logger.debug(f"ID:{self.username},Answers方法调用 - qnum:{self.qnum}, answer:{answer}, qtp:{self.qtp}")

        Xlist = list()
        Alist = list()
        # 平台ID9006不输出详细检查日志
        if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
            logger.debug(f"ID:{self.username},检查answer是否为None: answer={answer}")
        if answer is not None:
            for i in html.find("div", {"class": "stem_answer"}).find_all(
                "div", {"class": "clearfix answerBg"}
            ):
                x = i.find("span").get("data")
                a = strip_options(i.find("div").text.strip())
                try:
                    if i.find("div", {"class": "fl answer_p"}).find_all("img"):
                        a += "".join(
                            [
                                img.get("src")
                                for img in i.find(
                                    "div", {"class": "fl answer_p"}
                                ).find_all("img")
                                if img.get("src")
                            ]
                        )
                except:
                    pass
                # 首先尝试提取选项标识（A、B、C、D等）
                option_labels = self._extract_option_labels_from_answer(answer)
                if option_labels:
                    # 如果答案中包含选项标识，直接使用这些标识
                    if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.debug(f"ID:{self.username},从答案中提取到选项标识: {option_labels}")
                    for label in option_labels:
                        if label == x:  # 匹配当前选项标识
                            Xlist.append(x)
                            Alist.append(a)
                            if self.qtp == 0:  # 单选题找到一个就返回
                                return Alist, Xlist
                            break  # 多选题继续查找其他选项
                else:
                    # 如果答案中没有选项标识，使用文本匹配
                    for ans in answer.split("###"):
                        if self.qtp == 0:
                            if difflib.SequenceMatcher(None, a, ans).ratio() > bd:
                                Xlist.append(x)
                                Alist.append(a)
                                return Alist, Xlist
                        if self.qtp == 1:
                            if difflib.SequenceMatcher(None, a, ans).ratio() > bd:
                                Xlist.append(x)
                                Alist.append(a)
                                break
                    if self.qtp == 2:  # 填空题处理
                        # 填空题直接使用答案，支持多个填空用###分隔
                        if "###" in ans:
                            # 多个填空答案
                            fill_answers = ans.split("###")
                            for fill_answer in fill_answers:
                                Xlist.append(fill_answer.strip())
                                Alist.append(fill_answer.strip())
                        else:
                            # 单个填空答案
                            Xlist.append(ans.strip())
                            Alist.append(ans.strip())
                        return Alist, Xlist
                    elif self.qtp == 3:  # 判断题处理
                        # 判断题答案处理逻辑，使用更精确的匹配
                        positive_keywords = ["正确", "T", "True", "true", "是", "yes", "√"]
                        # 对于"对"字符，需要更精确的匹配，避免"不对"被误判
                        if "对" in ans and "不对" not in ans and "错对" not in ans:
                            judge_result = "true"
                        else:
                            judge_result = "true" if any(keyword in ans for keyword in positive_keywords) else "false"
                        Xlist.append(judge_result)
                        Alist.append(ans)
                        return Alist, Xlist
        if Alist:
            # 平台ID9006不输出详细返回日志
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                logger.debug(f"ID:{self.username},Alist不为空，直接返回: Alist={Alist}")
            return Alist, Xlist

        # 平台ID9006不输出qnum分支检查日志
        if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
            logger.debug(f"ID:{self.username},Alist为空，检查qnum分支: qnum={self.qnum}")

        if self.qnum == 1:
            # 使用清理后的题目文本查询备用题库
            clean_question = clean_question_text(self.question)
            answer, self.qnum = questionbank2(clean_question)
            return self.Answers(html, answer, bd=0.9)
        elif self.qnum == 2:
            # 直接跳转到AI答题（去除辅助题库查询）
            # 平台ID9006不输出跳转日志
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                logger.debug(f"ID:{self.username},qnum=2分支，准备跳转到AI答题")
            self.qnum = 3
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                logger.debug(f"ID:{self.username},设置qnum=3，递归调用Answers方法")
            return self.Answers(html, answer, bd=0.5)
        elif self.qnum == 3:
            # 尝试使用AI答题（仅对平台ID9006生效）
            # 平台ID9006不输出详细条件检查日志
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                has_cid = hasattr(self.session, 'cid')
                cid_value = getattr(self.session, 'cid', None)
                logger.debug(f"ID:{self.username},AI答题条件检查 - has_cid:{has_cid}, cid_value:{cid_value}, HOMEWORK_AI_AVAILABLE:{HOMEWORK_AI_AVAILABLE}")

            if hasattr(self.session, 'cid') and self.session.cid == 9006 and HOMEWORK_AI_AVAILABLE:
                    try:
                        logger.info(f"ID:{self.username},题库未找到答案，尝试使用AI答题")
                        # AI答题需要传递题型信息、原始题目文本和HTML元素
                        # 平台ID9006增加AI答题重试机制
                        ai_max_retries = 3 if hasattr(self.session, 'cid') and self.session.cid == 9006 else 1
                        ai_answer = None

                        for ai_retry in range(ai_max_retries):
                            try:
                                ai_answer = self._get_ai_answer_for_exam(self.question, self.qtp, html)
                                if ai_answer:
                                    break  # 成功获取答案，跳出重试循环
                                elif ai_retry < ai_max_retries - 1:
                                    if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                        logger.info(f"ID:{self.username},AI答题无响应，第{ai_retry + 1}次重试...")
                                    time.sleep(2)  # 等待2秒后重试
                            except Exception as e:
                                if ai_retry < ai_max_retries - 1:
                                    if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                        logger.warning(f"ID:{self.username},AI答题异常，第{ai_retry + 1}次重试: {str(e)}")
                                    time.sleep(3)  # 异常后等待更长时间
                                else:
                                    if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                        logger.error(f"ID:{self.username},AI答题重试失败: {str(e)}")

                        if ai_answer:
                            # 平台ID9006输出简化的题目和答案信息
                            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                logger.info(f"ID:{self.username},题目: {self.question[:50]}...")
                                logger.info(f"ID:{self.username},答案: {ai_answer}")
                            else:
                                logger.success(f"ID:{self.username},AI生成答案成功: {ai_answer}")
                            # 修复无限循环：直接处理AI答案，不要重新调用Answers方法
                            return self._process_ai_answer_directly(html, ai_answer)
                        else:
                            # 平台ID9006使用智能默认答案选择
                            if hasattr(self.session, 'cid') and self.session.cid == 9006:
                                default_answer = self._get_smart_default_answer(html)
                                logger.warning(f"ID:{self.username},AI答题失败，使用智能默认答案: {default_answer}")
                                return "AI答题失败", default_answer
                            else:
                                logger.warning(f"ID:{self.username},AI答题失败，使用默认答案")
                    except Exception as e:
                        logger.error(f"ID:{self.username},AI答题异常: {str(e)}")
            else:
                logger.warning(f"ID:{self.username},AI答题条件不满足 - has_cid:{has_cid}, cid_value:{cid_value}, HOMEWORK_AI_AVAILABLE:{HOMEWORK_AI_AVAILABLE}")
            return "答案匹配失败", ["A"]
        else:
            return "答案匹配失败", ["A"]

    def _get_ai_answer_for_exam(self, question_text, question_type=0, html=None):
        """使用AI接口为考试题目生成答案"""
        try:
            if not HOMEWORK_AI_AVAILABLE:
                return None

            # 清理题目文本用于AI查询（保留题型信息用于AI理解）
            import re
            clean_question = clean_question_text(question_text)

            if not clean_question:
                return None

            # 构建完整的问题（包含选项信息，类似平台ID9004的方式）
            full_question = self._build_full_question_with_options(clean_question, question_type, html)

            # 直接使用AI模型接口，不重试（根据用户要求）
            ai_answer = self._try_ai_model_interface(full_question, question_type)
            if ai_answer:
                # 平台ID9006简化日志输出
                if hasattr(self.session, 'cid') and self.session.cid == 9006:
                    logger.info(f"ID:{self.username},AI答题成功")
                else:
                    logger.success(f"ID:{self.username},AI模型接口成功: {ai_answer}")
                return ai_answer

            # AI接口失败，不重试，直接返回None
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                logger.warning(f"ID:{self.username},AI接口失败")
            return None

        except Exception as e:
            logger.error(f"ID:{self.username},AI答题接口调用失败: {str(e)}")
            return None

    def _build_full_question_with_options(self, clean_question, question_type, html):
        """构建包含选项的完整问题（类似平台ID9004的方式）"""
        if not html or question_type == 2:  # 填空题不需要选项
            return clean_question

        try:
            # 获取题型名称
            type_names = {
                0: "(单选题)",
                1: "(多选题)",
                2: "(填空题)",
                3: "(判断题)",
                4: "(简答题)"
            }
            question_type_name = type_names.get(question_type, "")

            # 构建完整问题
            full_question = f"{question_type_name} {clean_question}\n\n"

            # 提取选项信息
            options = {}
            try:
                stem_answer = html.find("div", {"class": "stem_answer"})
                if stem_answer:
                    option_divs = stem_answer.find_all("div", {"class": "clearfix answerBg"})
                    for option_div in option_divs:
                        span = option_div.find("span")
                        div = option_div.find("div")
                        if span and div:
                            option_label = span.get("data", "").strip()
                            option_text = div.text.strip()
                            if option_label and option_text:
                                options[option_label] = option_text

                # 添加选项到问题中
                for label, text in options.items():
                    full_question += f"{label}. {text}\n"

                # 平台ID9006不输出构建问题的详细日志
                if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                    logger.debug(f"ID:{self.username},构建完整问题: {full_question[:100]}...")
                return full_question

            except Exception as e:
                logger.warning(f"ID:{self.username},提取选项失败: {str(e)}")
                return clean_question

        except Exception as e:
            logger.error(f"ID:{self.username},构建完整问题失败: {str(e)}")
            return clean_question

    # 移除题库查询接口，根据用户要求不重试，直接使用AI接口

    def _try_ai_model_interface(self, question, question_type):
        """尝试AI模型接口（备选方案）"""
        try:
            import requests
            from urllib.parse import urlencode

            url = "http://tk.mixuelo.cc/api.php?act=aimodel"

            data_str = urlencode({
                'key': 'zXPX828s29Kk7Yj2',
                'model': 'deepseek-chat',
                'question': question
            })

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            response = requests.post(url, data=data_str, headers=headers, timeout=15)
            result = response.json()

            if result.get('code') == 1:
                ai_answer = result.get('answer', '') or result.get('data', '')
                ai_answer = ai_answer.strip() if ai_answer else ''

                if ai_answer:
                    # 清理AI答案
                    import re
                    answer_prefixes = ["答案:", "答案：", "Answer:", "答："]
                    for prefix in answer_prefixes:
                        if prefix in ai_answer:
                            parts = ai_answer.split(prefix, 1)
                            if len(parts) > 1:
                                ai_answer = parts[1].strip()

                    # 移除HTML标签，但保留###分隔符（重要！）
                    ai_answer = re.sub(r"<.*?>", "", ai_answer)
                    # 注意：不要移除###分隔符，这是多选题答案的重要格式
                    # ai_answer = ai_answer.replace("###", "")  # 已注释，保留###分隔符

                    return ai_answer

            return None

        except Exception as e:
            logger.debug(f"ID:{self.username},AI模型接口异常: {str(e)}")
            return None

    def _process_ai_answer_directly(self, html, ai_answer):
        """直接处理AI答案，避免无限循环"""
        try:
            logger.debug(f"ID:{self.username},开始直接处理AI答案: {ai_answer}")

            Xlist = list()
            Alist = list()

            # 获取所有选项
            options = []
            for i in html.find("div", {"class": "stem_answer"}).find_all(
                "div", {"class": "clearfix answerBg"}
            ):
                x = i.find("span").get("data")
                a = strip_options(i.find("div").text.strip())
                options.append((x, a))

            # 平台ID9006不输出详细调试信息
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                logger.debug(f"ID:{self.username},可用选项: {[(x, a) for x, a in options]}")

            # 处理AI答案
            ai_answers = ai_answer.split("###") if "###" in ai_answer else [ai_answer]
            if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                logger.debug(f"ID:{self.username},AI答案分割结果: {ai_answers}")

            # 匹配选项
            import difflib
            for ans in ai_answers:
                ans = ans.strip()
                if not ans:
                    continue

                # 平台ID9006的判断题特殊处理：语义匹配
                if hasattr(self.session, 'cid') and self.session.cid == 9006 and self.qtp == 3:
                    # 判断题语义匹配逻辑
                    positive_keywords = ["正确", "对", "是", "T", "True", "true", "yes", "√", "正确答案"]
                    negative_keywords = ["错误", "错", "否", "F", "False", "false", "no", "×", "错误答案"]

                    # 判断AI回复的语义
                    is_positive = False
                    is_negative = False

                    # 检查正面关键词
                    for keyword in positive_keywords:
                        if keyword in ans:
                            # 对于"对"字符，需要更精确的匹配，避免"不对"被误判
                            if keyword == "对" and ("不对" in ans or "错对" in ans):
                                continue
                            is_positive = True
                            break

                    # 检查负面关键词
                    if not is_positive:
                        for keyword in negative_keywords:
                            if keyword in ans:
                                is_negative = True
                                break

                    # 根据语义匹配选项
                    if is_positive or is_negative:
                        for x, a in options:
                            if x not in Xlist:  # 只考虑未使用的选项
                                option_text = a.lower().strip()
                                # 匹配正确选项
                                if is_positive and (option_text in ["对", "正确", "是", "t", "true", "yes"] or "对" in option_text):
                                    Xlist.append(x)
                                    Alist.append(a)
                                    if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                                        logger.success(f"ID:{self.username},判断题语义匹配: '{ans}' -> 选项{x}('{a}')")
                                    break
                                # 匹配错误选项
                                elif is_negative and (option_text in ["错", "错误", "否", "f", "false", "no"] or "错" in option_text):
                                    Xlist.append(x)
                                    Alist.append(a)
                                    if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                                        logger.success(f"ID:{self.username},判断题语义匹配: '{ans}' -> 选项{x}('{a}')")
                                    break
                        continue  # 语义匹配完成，跳过文本相似度匹配

                # 使用最佳匹配策略，找到未使用选项中相似度最高的
                best_match = None
                best_similarity = 0

                for x, a in options:
                    if x not in Xlist:  # 只考虑未使用的选项
                        similarity = difflib.SequenceMatcher(None, a, ans).ratio()
                        # 平台ID9006完全不输出详细匹配信息
                        if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                            logger.debug(f"ID:{self.username},选项匹配: '{a}' vs '{ans}' = {similarity:.3f}")

                        if similarity > 0.5 and similarity > best_similarity:
                            best_match = (x, a)
                            best_similarity = similarity

                if best_match:
                    x, a = best_match
                    Xlist.append(x)
                    Alist.append(a)
                    # 平台ID9006不输出详细匹配信息
                    if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.success(f"ID:{self.username},最佳匹配: '{ans}' -> 选项{x}('{a}'), 相似度={best_similarity:.3f}")
                else:
                    if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.warning(f"ID:{self.username},答案'{ans}'没有找到合适的匹配选项")

            if Xlist:
                # 平台ID9006只输出简化的成功信息
                if hasattr(self.session, 'cid') and self.session.cid == 9006:
                    logger.success(f"ID:{self.username},AI答题成功: {' '.join(Xlist)}")
                else:
                    logger.success(f"ID:{self.username},AI答案处理成功: {Alist} -> {Xlist}")
                return Alist, Xlist
            else:
                if not (hasattr(self.session, 'cid') and self.session.cid == 9006):
                    logger.warning(f"ID:{self.username},AI答案无法匹配任何选项，使用默认答案")
                return "答案匹配失败", ["A"]

        except Exception as e:
            logger.error(f"ID:{self.username},直接处理AI答案异常: {str(e)}")
            return "答案匹配失败", ["A"]

    def _get_smart_default_answer(self, html):
        """
        为平台ID9006提供智能默认答案选择
        根据题型和选项数量选择最优默认答案
        """
        try:
            # 获取所有选项
            options = []
            stem_answer = html.find("div", {"class": "stem_answer"})
            if stem_answer:
                option_divs = stem_answer.find_all("div", {"class": "clearfix answerBg"})
                for option_div in option_divs:
                    span = option_div.find("span")
                    if span and span.get("data"):
                        x = span.get("data")
                        options.append(x)

            if not options:
                return ["A"]  # 如果无法获取选项，返回默认A

            # 根据题型选择策略
            if self.qtp == 0:  # 单选题
                # 单选题选择中间选项（通常正确率较高）
                if len(options) >= 3:
                    middle_index = len(options) // 2
                    return [options[middle_index]]
                else:
                    return [options[0]]  # 选项少时选择第一个

            elif self.qtp == 1:  # 多选题
                # 多选题选择前两个选项（常见的保守策略）
                if len(options) >= 2:
                    return options[:2]
                else:
                    return options  # 选项少时全选

            elif self.qtp == 3:  # 判断题
                # 判断题倾向于选择"正确"（统计上正确率较高）
                for option in options:
                    if option.lower() in ["true", "a"]:  # 通常A表示正确
                        return [option]
                return [options[0]]  # 找不到明确的正确选项时选第一个

            else:
                return [options[0]]  # 其他题型选择第一个选项

        except Exception as e:
            logger.debug(f"ID:{self.username},智能默认答案选择异常: {str(e)}")
            return ["A"]  # 异常时返回最基本的默认答案

    def _extract_option_labels_from_answer(self, answer):
        """从答案中提取选项标识（A、B、C、D等）"""
        try:
            import re
            option_labels = []

            # 分割答案
            answer_parts = answer.split("###")

            for part in answer_parts:
                part = part.strip()
                # 匹配选项标识模式：A、B、C、D 或 A. B. C. D.
                match = re.match(r'^([A-Z])[\s、\.]', part)
                if match:
                    option_label = match.group(1)
                    option_labels.append(option_label)
                    # logger.debug(f"ID:{self.username},提取到选项标识: '{option_label}' from '{part}'")

            return option_labels if option_labels else None

        except Exception as e:
            logger.debug(f"ID:{self.username},提取选项标识异常: {str(e)}")
            return None

    def _format_fill_answer(self, answer):
        """格式化填空题答案，支持多种分隔符"""
        try:
            # 确保有username属性
            username = getattr(self, 'username', 'unknown')

            if not answer:
                return ""

            answer = str(answer).strip()

            # 处理多个填空的情况，支持###和|分隔符
            if "###" in answer:
                fill_answers = [ans.strip() for ans in answer.split("###") if ans.strip()]
                if len(fill_answers) > 1:
                    # 多个填空用换行符连接（适合APP端API）
                    formatted_answer = "\n".join(fill_answers)
                    # 简化日志输出，只在非9006平台显示详细信息
                    if not (hasattr(self, 'session') and hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.debug(f"ID:{username},多填空答案处理(换行格式): {fill_answers} -> {repr(formatted_answer)}")
                else:
                    formatted_answer = fill_answers[0] if fill_answers else ""
                    if not (hasattr(self, 'session') and hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.debug(f"ID:{username},单填空答案处理: {fill_answers} -> {formatted_answer}")
                return formatted_answer
            elif "|" in answer:
                fill_answers = [ans.strip() for ans in answer.split("|") if ans.strip()]
                if len(fill_answers) > 1:
                    # 多个填空用换行符连接（适合APP端API）
                    formatted_answer = "\n".join(fill_answers)
                    if not (hasattr(self, 'session') and hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.debug(f"ID:{username},竖线分隔答案处理(换行格式): {fill_answers} -> {repr(formatted_answer)}")
                else:
                    formatted_answer = fill_answers[0] if fill_answers else ""
                    if not (hasattr(self, 'session') and hasattr(self.session, 'cid') and self.session.cid == 9006):
                        logger.debug(f"ID:{username},竖线分隔单答案处理: {fill_answers} -> {formatted_answer}")
                return formatted_answer
            else:
                # 单个填空答案
                if not (hasattr(self, 'session') and hasattr(self.session, 'cid') and self.session.cid == 9006):
                    logger.debug(f"ID:{username},单个填空答案: {repr(answer)}")
                return answer

        except Exception as e:
            username = getattr(self, 'username', 'unknown')
            logger.error(f"ID:{username},格式化填空题答案异常: {str(e)}")
            # 返回原始答案或空字符串
            return str(answer) if answer else ""


if __name__ == "__main__":
    session = requests.session()
    session.headers = {
        "Cookie": 'k8sexam=1741624113.916.2715.818175; jrose=0A082F5A1DEC1E723657EBC761388302.mooc-exam-600071439-8ntc5; source=""; thirdRegist=0; schoolId=129837; k8s=1741594456.94.8796.920936; route=9dad54174dd60dc20bb47d99b18d6f40; videojs_id=5447621; writenote=yes; tl=1; uname=""; jrose=441CB4D068A878AF4EA7BD6745B92581.mooc-2523619855-2x70h; fid=4684; _uid=341463923; uf=d9387224d3a6095bf074fd62a2f69d94001acc1591681349f9775deb6b414b0b6c899f46b20fbc97fcd538756ccdc738913b662843f1f4ad6d92e371d7fdf644ef759f2e481f6eb70246270a56330e6c3ad59b143144275bc3a3c76c6a5dc08ad552431173659c60; _d=1741624133098; UID=341463923; vc2=B3AB84A9E961A5692BB49FD62CE82520; vc3=Y4%2Ba%2BGbWLsMa9ICqLPISzLTHYOHBuP%2BEeHBwMhWbU81kI%2FPHzmiUy6m9O5Ls5GfycZ7wxT6sA90SIa1dCU431i4LbyatFIVpoR4qoRmoH9rGMni9TXnEpjeoifAwCLuAg65j2PjcjdNz98ceYjTzGVW6hlGsnCfZ9P0fJobke8A%3Dd41b2ec7362a58427192dcbc21d2e356; cx_p_token=1f805c9da301b127b0c1a516763e6fcb; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIzNDE0NjM5MjMiLCJsb2dpblRpbWUiOjE3NDE2MjQxMzMwOTksImV4cCI6MTc0MjIyODkzM30.sm1cQCpxTPB7Lphf1jL63aP0NG7vAZeUmqzZbTAg39g; xxtenc=9a4196df62b27a4872e58dbc77d1c9f8; DSSTASH_LOG=C_38-UN_3791-US_341463923-T_1741624133099',
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.36",
        "X-Requested-With": "XMLHttpRequest",
    }
    list_info = [
        {
            "courseid": "250856463",
            "clazzid": "117280978",
            "cpi": "405328372",
            "kcname": "中国民间艺术的奇妙之旅",
        }
    ]
    ex = EXAM(session, "0000", list_info, open=1)
    ex.get_data()
