2025-07-24 00:37:54,559 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='董怡然' sex=AccountSex.男 phone=*********** school='新乡学院继续教育学院' stu_id=**********)
2025-07-24 00:37:54,560 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 00:37:54,560 [Main] INFO -> Ver. 0.4.5
2025-07-24 00:37:55,038 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/5aed39b8323ffb48a4baf267caa78645.jpg
2025-07-24 00:37:55,367 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 00:37:55,835 [RootAPI] INFO -> 课程列表拉取成功 共 26 个
2025-07-24 00:39:37,801 [Main] INFO -> 
-----*任务执行完毕, 程序退出*-----
2025-07-24 00:40:34,534 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='董怡然' sex=AccountSex.男 phone=*********** school='新乡学院继续教育学院' stu_id=**********)
2025-07-24 00:40:34,534 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 00:40:34,534 [Main] INFO -> Ver. 0.4.5
2025-07-24 00:40:34,987 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/5aed39b8323ffb48a4baf267caa78645.jpg
2025-07-24 00:40:35,029 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 00:40:35,512 [RootAPI] INFO -> 课程列表拉取成功 共 26 个
2025-07-24 00:41:21,969 [Main] INFO -> 
-----*任务执行完毕, 程序退出*-----
2025-07-24 00:41:28,348 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='董怡然' sex=AccountSex.男 phone=*********** school='新乡学院继续教育学院' stu_id=**********)
2025-07-24 00:41:28,348 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 00:41:28,348 [Main] INFO -> Ver. 0.4.5
2025-07-24 00:41:28,934 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/5aed39b8323ffb48a4baf267caa78645.jpg
2025-07-24 00:41:28,975 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 00:41:29,538 [RootAPI] INFO -> 课程列表拉取成功 共 26 个
2025-07-24 00:41:39,729 [Classes] DEBUG -> 章节 Resp: [{'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一单元', 'id': *********, 'label': '1', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二单元', 'id': *********, 'label': '2', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三单元', 'id': 302467097, 'label': '3', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 4, 'name': '第四单元', 'id': 302467098, 'label': '4', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 5, 'name': '第五单元', 'id': 302467099, 'label': '5', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 6, 'name': '第六单元', 'id': 302467100, 'label': '6', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 7, 'name': '第七单元', 'id': 302467101, 'label': '7', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 8, 'name': '第八单元', 'id': 302467102, 'label': '8', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 9, 'name': '第九单元', 'id': 302467103, 'label': '9', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 10, 'name': '第十单元', 'id': 302467104, 'label': '10', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 11, 'name': '第十一单元', 'id': 302467105, 'label': '11', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 12, 'name': '第十二单元', 'id': 302467106, 'label': '12', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 13, 'name': '第十三单元', 'id': 302467107, 'label': '13', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 0, 'attachment': {'data': []}, 'indexorder': 14, 'name': '第十四单元', 'id': 302467108, 'label': '14', 'layer': 1, 'parentnodeid': 0, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467109, 'label': '1.1', 'layer': 2, 'parentnodeid': *********, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467110, 'label': '2.1', 'layer': 2, 'parentnodeid': *********, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467111, 'label': '3.1', 'layer': 2, 'parentnodeid': 302467097, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467112, 'label': '4.1', 'layer': 2, 'parentnodeid': 302467098, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467113, 'label': '5.1', 'layer': 2, 'parentnodeid': 302467099, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467114, 'label': '6.1', 'layer': 2, 'parentnodeid': 302467100, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467115, 'label': '7.1', 'layer': 2, 'parentnodeid': 302467101, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467116, 'label': '8.1', 'layer': 2, 'parentnodeid': 302467102, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467117, 'label': '9.1', 'layer': 2, 'parentnodeid': 302467103, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467118, 'label': '10.1', 'layer': 2, 'parentnodeid': 302467104, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467119, 'label': '11.1', 'layer': 2, 'parentnodeid': 302467105, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467120, 'label': '12.1', 'layer': 2, 'parentnodeid': 302467106, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467121, 'label': '13.1', 'layer': 2, 'parentnodeid': 302467107, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 1, 'name': '第一课时', 'id': 302467122, 'label': '14.1', 'layer': 2, 'parentnodeid': 302467108, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467123, 'label': '1.2', 'layer': 2, 'parentnodeid': *********, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467124, 'label': '2.2', 'layer': 2, 'parentnodeid': *********, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467125, 'label': '3.2', 'layer': 2, 'parentnodeid': 302467097, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467126, 'label': '4.2', 'layer': 2, 'parentnodeid': 302467098, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467127, 'label': '5.2', 'layer': 2, 'parentnodeid': 302467099, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467128, 'label': '6.2', 'layer': 2, 'parentnodeid': 302467100, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467129, 'label': '7.2', 'layer': 2, 'parentnodeid': 302467101, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467130, 'label': '8.2', 'layer': 2, 'parentnodeid': 302467102, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467131, 'label': '9.2', 'layer': 2, 'parentnodeid': 302467103, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467132, 'label': '10.2', 'layer': 2, 'parentnodeid': 302467104, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467133, 'label': '11.2', 'layer': 2, 'parentnodeid': 302467105, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467134, 'label': '12.2', 'layer': 2, 'parentnodeid': 302467106, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467135, 'label': '13.2', 'layer': 2, 'parentnodeid': 302467107, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 2, 'name': '第二课时', 'id': 302467136, 'label': '14.2', 'layer': 2, 'parentnodeid': 302467108, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467137, 'label': '1.3', 'layer': 2, 'parentnodeid': *********, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467138, 'label': '2.3', 'layer': 2, 'parentnodeid': *********, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467139, 'label': '3.3', 'layer': 2, 'parentnodeid': 302467097, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467140, 'label': '4.3', 'layer': 2, 'parentnodeid': 302467098, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467141, 'label': '5.3', 'layer': 2, 'parentnodeid': 302467099, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467142, 'label': '6.3', 'layer': 2, 'parentnodeid': 302467100, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467143, 'label': '7.3', 'layer': 2, 'parentnodeid': 302467101, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467144, 'label': '8.3', 'layer': 2, 'parentnodeid': 302467102, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467145, 'label': '9.3', 'layer': 2, 'parentnodeid': 302467103, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467146, 'label': '10.3', 'layer': 2, 'parentnodeid': 302467104, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467147, 'label': '11.3', 'layer': 2, 'parentnodeid': 302467105, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467148, 'label': '12.3', 'layer': 2, 'parentnodeid': 302467106, 'status': 'open'}, {'jobcount': 1, 'attachment': {'data': []}, 'indexorder': 3, 'name': '第三课时', 'id': 302467149, 'label': '13.3', 'layer': 2, 'parentnodeid': 302467107, 'status': 'open'}]
2025-07-24 00:41:39,730 [Classes] INFO -> 获取课程章节成功 (共 55 个) [建筑施工技术(Cou.211690263/Cla.115811459)]
2025-07-24 00:41:39,796 [Chapters] INFO -> 任务点状态已更新
2025-07-24 00:41:39,860 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [1:第一单元(Id.*********)]
2025-07-24 00:41:39,860 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [1:第一单元(Id.*********)]
2025-07-24 00:41:39,860 [Main] INFO -> 忽略完成任务点 [1.1:第一课时(Id.302467109)]
2025-07-24 00:41:39,960 [Main] INFO -> 忽略完成任务点 [1.2:第二课时(Id.302467123)]
2025-07-24 00:41:40,061 [Main] INFO -> 忽略完成任务点 [1.3:第三课时(Id.302467137)]
2025-07-24 00:41:40,224 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [2:第二单元(Id.*********)]
2025-07-24 00:41:40,224 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [2:第二单元(Id.*********)]
2025-07-24 00:41:40,224 [Main] INFO -> 忽略完成任务点 [2.1:第一课时(Id.302467110)]
2025-07-24 00:41:40,325 [Main] INFO -> 忽略完成任务点 [2.2:第二课时(Id.302467124)]
2025-07-24 00:41:40,425 [Main] INFO -> 忽略完成任务点 [2.3:第三课时(Id.302467138)]
2025-07-24 00:41:40,588 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [3:第三单元(Id.302467097)]
2025-07-24 00:41:40,588 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [3:第三单元(Id.302467097)]
2025-07-24 00:41:40,588 [Main] INFO -> 忽略完成任务点 [3.1:第一课时(Id.302467111)]
2025-07-24 00:41:40,688 [Main] INFO -> 忽略完成任务点 [3.2:第二课时(Id.302467125)]
2025-07-24 00:41:40,789 [Main] INFO -> 忽略完成任务点 [3.3:第三课时(Id.302467139)]
2025-07-24 00:41:40,950 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [4:第四单元(Id.302467098)]
2025-07-24 00:41:40,950 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [4:第四单元(Id.302467098)]
2025-07-24 00:41:40,950 [Main] INFO -> 忽略完成任务点 [4.1:第一课时(Id.302467112)]
2025-07-24 00:41:41,051 [Main] INFO -> 忽略完成任务点 [4.2:第二课时(Id.302467126)]
2025-07-24 00:41:41,151 [Main] INFO -> 忽略完成任务点 [4.3:第三课时(Id.302467140)]
2025-07-24 00:41:41,313 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [5:第五单元(Id.302467099)]
2025-07-24 00:41:41,314 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [5:第五单元(Id.302467099)]
2025-07-24 00:41:41,314 [Main] INFO -> 忽略完成任务点 [5.1:第一课时(Id.302467113)]
2025-07-24 00:41:41,414 [Main] INFO -> 忽略完成任务点 [5.2:第二课时(Id.302467127)]
2025-07-24 00:41:41,515 [Main] INFO -> 忽略完成任务点 [5.3:第三课时(Id.302467141)]
2025-07-24 00:41:41,678 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [6:第六单元(Id.302467100)]
2025-07-24 00:41:41,678 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [6:第六单元(Id.302467100)]
2025-07-24 00:41:41,678 [Main] INFO -> 忽略完成任务点 [6.1:第一课时(Id.302467114)]
2025-07-24 00:41:41,779 [Main] INFO -> 忽略完成任务点 [6.2:第二课时(Id.302467128)]
2025-07-24 00:41:41,879 [Main] INFO -> 忽略完成任务点 [6.3:第三课时(Id.302467142)]
2025-07-24 00:41:42,041 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [7:第七单元(Id.302467101)]
2025-07-24 00:41:42,041 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [7:第七单元(Id.302467101)]
2025-07-24 00:41:42,041 [Main] INFO -> 忽略完成任务点 [7.1:第一课时(Id.302467115)]
2025-07-24 00:41:42,142 [Main] INFO -> 忽略完成任务点 [7.2:第二课时(Id.302467129)]
2025-07-24 00:41:42,242 [Main] INFO -> 忽略完成任务点 [7.3:第三课时(Id.302467143)]
2025-07-24 00:41:42,405 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [8:第八单元(Id.302467102)]
2025-07-24 00:41:42,406 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [8:第八单元(Id.302467102)]
2025-07-24 00:41:42,406 [Main] INFO -> 忽略完成任务点 [8.1:第一课时(Id.302467116)]
2025-07-24 00:41:42,515 [Main] INFO -> 忽略完成任务点 [8.2:第二课时(Id.302467130)]
2025-07-24 00:41:42,616 [Main] INFO -> 忽略完成任务点 [8.3:第三课时(Id.302467144)]
2025-07-24 00:41:42,779 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [9:第九单元(Id.302467103)]
2025-07-24 00:41:42,779 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [9:第九单元(Id.302467103)]
2025-07-24 00:41:42,779 [Main] INFO -> 忽略完成任务点 [9.1:第一课时(Id.302467117)]
2025-07-24 00:41:42,880 [Main] INFO -> 忽略完成任务点 [9.2:第二课时(Id.302467131)]
2025-07-24 00:41:42,981 [Main] INFO -> 忽略完成任务点 [9.3:第三课时(Id.302467145)]
2025-07-24 00:41:43,143 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [10:第十单元(Id.302467104)]
2025-07-24 00:41:43,143 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [10:第十单元(Id.302467104)]
2025-07-24 00:41:43,143 [Main] INFO -> 忽略完成任务点 [10.1:第一课时(Id.302467118)]
2025-07-24 00:41:43,244 [Main] INFO -> 忽略完成任务点 [10.2:第二课时(Id.302467132)]
2025-07-24 00:41:43,345 [Main] INFO -> 忽略完成任务点 [10.3:第三课时(Id.302467146)]
2025-07-24 00:41:43,506 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [11:第十一单元(Id.302467105)]
2025-07-24 00:41:43,506 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [11:第十一单元(Id.302467105)]
2025-07-24 00:41:43,506 [Main] INFO -> 忽略完成任务点 [11.1:第一课时(Id.302467119)]
2025-07-24 00:41:43,607 [Main] INFO -> 忽略完成任务点 [11.2:第二课时(Id.302467133)]
2025-07-24 00:41:43,708 [Main] INFO -> 忽略完成任务点 [11.3:第三课时(Id.302467147)]
2025-07-24 00:41:43,869 [Chapters] INFO -> 获取章节任务节点卡片成功 共 0 个 [12:第十二单元(Id.302467106)]
2025-07-24 00:41:43,869 [Chapters] INFO -> 章节 任务节点解析成功 共 0 个 [12:第十二单元(Id.302467106)]
2025-07-24 00:41:43,933 [Chapters] INFO -> 获取章节任务节点卡片成功 共 1 个 [12.1:第一课时(Id.302467120)]
2025-07-24 00:41:43,933 [Chapters] DEBUG -> (0) 解析卡片成功 共 1 个任务点
2025-07-24 00:41:43,934 [Chapters] DEBUG -> (0, 0) 视频任务点 schema: {'jobid': '****************', 'rt': '0.9', 'size': 284995618, 'hsize': '271.79 MB', 'name': 'HUNAN_34.mp4', 'mid': '3990588066481517798128008', 'type': '.mp4', 'doublespeed': 1, 'objectid': 'e877581fcb7d88797fb0a6e2b8b9b87b', '_jobid': '****************'}
2025-07-24 00:41:43,934 [Chapters] INFO -> 章节 任务节点解析成功 共 1 个 [12.1:第一课时(Id.302467120)]
2025-07-24 00:41:44,017 [PointVideo] DEBUG -> Attachment: {'hiddenConfig': False, 'isMirror': False, 'attachments': [{'headOffset': 2460000, 'otherInfo': 'nodeId_302467120-cpi_321528898-rt_0.9-ds_1-ff_d-be_0_0-vt_1-v_6-enc_facc017fce9c603e461e96e7d49937a4&courseId=211690263', 'isPassed': False, 'mid': '3990588066481517798128008', 'jumpTimePointList': [], 'type': 'video', 'begins': 0, 'jobid': '****************', 'customType': 0, 'attDurationEnc': '498048faeecd045cdf9c57ea1b216f14', 'videoFaceCaptureEnc': 'f8d6c9a3fb5b529ad840ef0fe904b469', 'ends': 0, 'randomCaptureTime': 204, 'property': {'jobid': '****************', 'rt': '0.9', 'size': 284995618, 'hsize': '271.79 MB', 'module': 'insertvideo', 'name': 'HUNAN_34.mp4', 'mid': '3990588066481517798128008', 'type': '.mp4', 'doublespeed': 1, 'objectid': 'e877581fcb7d88797fb0a6e2b8b9b87b', '_jobid': '****************'}, 'playTime': 2460000, 'attDuration': 2636, 'headOffsetVersion': 0, 'job': True, 'aid': 459477859, 'objectId': 'e877581fcb7d88797fb0a6e2b8b9b87b'}], 'coursename': '建筑施工技术', 'cutUseNewVeision': True, 'mooc2': 0, 'control': True, 'chapterVideoTranslate': 0, 'defaults': {'fid': '118496', 'ktoken': '7af4a9016e0fff9ab61a8adcf684fd95', 'mtEnc': '51ee49938665ae8b68957b4eeaa3bc1d', 'appInfo': '', 'playingCapture': 1, 'videoAutoPlay': 0, 'userid': '*********', 'reportTimeInterval': 60, 'showVideoWaterMark': 0, 'endCapture': 0, 'defenc': '6f6c47a080b20870e2dd29bc845e2733', 'cardid': 271584771, 'imageUrl': 'https://p.ananas.chaoxing.com/star3/origin/b74bd8f8139b0a74393dd499ab4c2a1a.png', 'state': 0, 'cpi': 321528898, 'captureInterval': 0, 'playAginCapture': 0, 'startCapture': 1, 'isFiled': 0, 'ignoreVideoCtrl': 0, 'reportUrl': 'https://mooc1-api.chaoxing.com/mooc-ans/multimedia/log/a/321528898', 'chapterCapture': 0, 'initdataUrl': 'https://mooc1-api.chaoxing.com/mooc-ans/richvideo/initdatawithviewer', 'cFid': '118496', 'knowledgeid': 302467120, 'videoTopicCloud': 0, 'qnenc': 'da21f33e86103baa7b23eb852ce07085', 'clazzId': 115811459, 'chapterCollectionType': 0, 'lastmodifytime': 1747214440000, 'aiVideoInterpret': 0, 'courseid': 211690263, 'subtitleUrl': 'https://mooc1-api.chaoxing.com/mooc-ans/richvideo/subtitle', 'playingLoopCapture': 1}, 'knowledgename': '第一课时', 'openShowHotMap': False, 'isErya': 0}
2025-07-24 00:41:44,017 [PointVideo] INFO -> Attachment拉取成功
2025-07-24 00:41:44,017 [PointVideo] INFO -> 解析Attachment成功
2025-07-24 00:41:44,080 [PointVideo] DEBUG -> 视频 schema: {'length': 284995618, 'thumbnailsEnc': 'f64cc61ba73553dcab480c8601b294d3', 'screenshot': 'https://p2.ananas.chaoxing.com/sv-w3/video/bf/92/e4/e877581fcb7d88797fb0a6e2b8b9b87b/snapshot.jpg', 'dtoken': '45d6668f47f3e3463bb956f1873b5989', 'duration': 2636, 'mp3': 'https://s2.ananas.chaoxing.com/sv-w3/video/bf/92/e4/e877581fcb7d88797fb0a6e2b8b9b87b/mp3/', 'download': 'http://d0.ananas.chaoxing.com/download/e877581fcb7d88797fb0a6e2b8b9b87b?at_=1753288903818&ak_=ad82ace52674c8fc67cfdb04b671efa0&ad_=4293ae9d8e62e2da2c8c3844ea9fc3fd', 'filename': 'HUNAN_34.mp4', 'crc': '494c769f1436650ae927013b0e94ec9a', 'public_cdn_prefix': ['s2', 's1'], 'http': 'https://s2.cldisk.com/sv-w3/video/bf/92/e4/e877581fcb7d88797fb0a6e2b8b9b87b/sd.mp4?at_=1753288903819&ak_=d3e085643eeccc465c7dd5cc0226502f&ad_=b58f56a3f527e13209f823f13033fdc1', 'thumbnails': 'https://p2.cldisk.com/sv-w3/video/bf/92/e4/e877581fcb7d88797fb0a6e2b8b9b87b/thumbnails/', 'objectid': 'e877581fcb7d88797fb0a6e2b8b9b87b', 'key': 'fdca840145a753eb8c70d59017df9351', 'status': 'success'}
2025-07-24 00:41:44,080 [PointVideo] INFO -> 拉取成功 PointVideo(title=HUNAN_34.mp4 duration=2636 objectid=e877581fcb7d88797fb0a6e2b8b9b87b dtoken=45d6668f47f3e3463bb956f1873b5989 jobid=****************)
2025-07-24 00:41:44,081 [MediaPlayResolver] INFO -> 开始播放 倍速=x1.0 汇报率=58s [PointVideo(title=HUNAN_34.mp4 duration=2636 objectid=e877581fcb7d88797fb0a6e2b8b9b87b dtoken=45d6668f47f3e3463bb956f1873b5989 jobid=****************)]
2025-07-24 00:41:44,140 [PointVideo] DEBUG -> 上报 resp: {'isPassed': False, 'videoTimeLimit': False, 'hasJobLimit': False}
2025-07-24 00:41:44,140 [PointVideo] INFO -> 播放上报成功 0/2636
2025-07-24 00:42:43,026 [RootAPI] INFO -> 账号登录成功 AccInfo(puid=********* name='董怡然' sex=AccountSex.男 phone=*********** school='新乡学院继续教育学院' stu_id=**********)
2025-07-24 00:42:43,026 [Main] INFO -> 
-----*任务开始执行*-----
2025-07-24 00:42:43,026 [Main] INFO -> Ver. 0.4.5
2025-07-24 00:42:43,409 [RootAPI] INFO -> 预上传人脸获取成功 U.http://p.ananas.chaoxing.com/star3/origin/5aed39b8323ffb48a4baf267caa78645.jpg
2025-07-24 00:42:43,457 [RootAPI] INFO -> 人脸保存成功 "faces\*********.jpg"
2025-07-24 00:42:43,982 [RootAPI] INFO -> 课程列表拉取成功 共 26 个
2025-07-24 00:42:59,893 [Main] ERROR -> 
-----*程序运行异常退出*-----
Traceback (most recent call last):
  File "Y:\yuanma\CxKitty-main\main.py", line 369, in <module>
    for task_obj in ClassSelector(command, classes):
  File "Y:\yuanma\CxKitty-main\cxapi\classes.py", line 224, in __next__
    exams_lst = self.__classes.get_exam_by_index(curr_index)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Y:\yuanma\CxKitty-main\cxapi\classes.py", line 151, in get_exam_by_index
    status=ExamStatus(exam.span.text),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "W:\Python\Python312\Lib\enum.py", line 757, in __call__
    return cls.__new__(cls, value)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "W:\Python\Python312\Lib\enum.py", line 1171, in __new__
    raise ve_exc
ValueError: '待做' is not a valid ExamStatus
