import pymysql
from dbutils.pooled_db import PooledDB
from loguru import logger
import time
import traceback




class OrderProcessorsql:
    def __init__(self):
        try:
            self.mysql_pool = PooledDB(
                creator=pymysql,
                maxconnections=15,  # 最大连接数
                host='127.0.0.1',
                user='root',         # 修改为你的MySQL用户名
                password='root',     # 修改为你的MySQL密码
                database='mzf',  # 修改为你的数据库名，根据SQL文件
                autocommit=True,
                cursorclass=pymysql.cursors.DictCursor)
            logger.success("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {str(e)}")
            traceback.print_exc()
            raise


    def get_order(self,sql):
        try:
            conn = self.mysql_pool.connection()
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                return result
        except Exception as e:
            logger.error(f"查询数据失败: {str(e)}, SQL: {sql}")
            time.sleep(5)  # 出错后等待5秒
            return []
            
    def get_order_dan(self,sql):
        try:
            conn = self.mysql_pool.connection()
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchone()
                return result
        except Exception as e:
            logger.error(f"查询单条数据失败: {str(e)}, SQL: {sql}")
            time.sleep(5)  # 出错后等待5秒
            return None
            
    def update_order(self,sql):
        try:
            conn = self.mysql_pool.connection()
            with conn.cursor() as cursor:
                cursor.execute(sql)
                return True
        except Exception as e:
            logger.error(f"更新数据失败: {str(e)}, SQL: {sql}")
            time.sleep(5)  # 出错后等待5秒
            return False
