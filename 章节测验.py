import requests

url = "https://mooc1.chaoxing.com/mooc-ans/knowledge/cards?clazzid=91297170&courseid=229349196&knowledgeid=670797757&num=2&ut=s&cpi=282371395&v=2025-0424-1038-4&mooc2=1&isMicroCourse=false&editorPreview=0&crossId=undefined"

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'sec-ch-ua-platform': "\"Windows\"",
  'Upgrade-Insecure-Requests': "1",
  'Sec-Fetch-Site': "cross-site",
  'Sec-Fetch-Mode': "navigate",
  'Sec-Fetch-User': "?1",
  'Sec-Fetch-Dest': "document",
  'Accept-Language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'Cookie': "k8s=1751347563.493.12706.587279; route=1ab934bb3bbdaaef56ce3b0da45c52ed; fid=2790; writenote=yes; doubleSpeedValue=2; videojs_id=8260357; jrose=E06B43EA4531A327AA736AB3737BC051.mooc-p4-2387308155-jzjwp; source=\"\"; _uid=221880762; _d=1751482845912; UID=221880762; vc3=a454agod0VF66B%2B8Oy75nUs8GS2N2ty8jUEWMi%2B7NpLERrpe7ydd2NCI4R1W5%2BwMSICNhL%2Fhvrepic6c7NaUZ0YCyybfNWrKh4Nbakg1H9kg%2FiVvJVk2TLz4dxyQxrfkmLemAvdJvvWhg7iKpeAJ2g5i67xt2YpKH8i3OHsyztE%3Da66dacb9740164f5f3d977ea0ff47b52; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f9980daa5ac6aa7d14f1db27cf8906ebabc81a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc7988116ae8984b028e33b0a5f2ba96c71a0d8eef657fc076d342747f5d0eac2fc7b9a467b233c21a4df7ff280fcb29d10d8a4c92b12beb4b44156174a8045981e87e69aadf3839ce560d457d8c24c6dfe7fafd565af53bf2; cx_p_token=cad34a0d2b89a3b7d5d904a98b0222c7; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTE0ODI4NDU5MTMsImV4cCI6MTc1MjA4NzY0NX0.6J9y34l94EvL3GE7M7CzyB2OSK3tVv-0wBlJ_j78IDY; xxtenc=c4885b4f5d02c8246c0260b0959ca202; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1751482845914; spaceFid=2790; spaceRoleId=3; tl=1"
}

response = requests.get(url, headers=headers)

print(response.text)