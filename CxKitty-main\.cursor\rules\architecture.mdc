# Architecture

## Layer Structure
- API Layer: Direct interaction with the learning platform (`cxapi` module)
- Business Logic Layer: Implementation of specific features (`resolver` module)
- Data Processing Layer: Data storage and analysis
- Main Control Layer: Coordination of modules (`main.py`)

## Module Responsibilities
1. **cxapi**: Core API interaction
   - `api.py`: Main API class
   - `session.py`: HTTP session management
   - `task_point/`: Task point handling
   - `classes.py`: Course management
   - `chapters.py`: Chapter management
   - `exam.py`: Exam handling

2. **resolver**: Business logic implementation
   - `question.py`: Question resolving
   - `media.py`: Media playback simulation
   - `document.py`: Document processing
   - `searcher/`: Answer search implementations

3. **utils.py**: Utility functions

4. **config.py**: Configuration management

## Dependency Flow
- Main script depends on resolvers
- Resolvers depend on API classes
- API classes depend on session management
- All modules may use utility functions

## Object Lifecycle
- Session is created first and maintained throughout execution
- API objects are initialized with session
- Task objects (chapters, exams) are created as needed
- Resolvers are instantiated to handle specific tasks

## Example
```
main.py
  ↓
  ├── ChaoXingAPI (cxapi/api.py)
  │     ↓
  │     └── SessionWraper (cxapi/session.py)
  │
  ├── ClassSelector (cxapi/classes.py)
  │     ↓
  │     ├── ChapterContainer (cxapi/chapters.py)
  │     └── ExamDto (cxapi/exam.py)
  │
  └── Resolvers (resolver/)
        ├── QuestionResolver (resolver/question.py)
        ├── MediaPlayResolver (resolver/media.py)
        └── DocumetResolver (resolver/document.py)
```
description:
globs:
alwaysApply: false
---
