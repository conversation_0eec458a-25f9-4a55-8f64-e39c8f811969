import requests

url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb?_classId=64009643&courseid=228836418&token=fdb8e5d170da272670346f3bf4cf7ad1&totalQuestionNum=6eaca1bf84b6b650bc4b09d8f8805f1e&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"

payload = {
  'courseId': "228836418",
  'classId': "64009643",
  'knowledgeid': "0",
  'cpi': "282371395",
  'workRelationId': "24875828",
  'workAnswerId': "52094560",
  'jobid': "",
  'standardEnc': "eba6203955dc26a6781808c842f604b7",
  'enc_work': "fdb8e5d170da272670346f3bf4cf7ad1",
  'totalQuestionNum': "6eaca1bf84b6b650bc4b09d8f8805f1e",
  'pyFlag': "1",
  'answerwqbid': "211691861,211691862,211691863,211691864,211691865,211691866,211691867,211691868,211691869,211691871,",
  'mooc2': "1",
  'randomOptions': "false",
  'workTimesEnc': "666666",
  'answertype211691861': "4",
  'answer211691861': "666666",
  'answertype211691862': "4",
  'answer211691862': "666666",
  'answertype211691863': "4",
  'answer211691863': "666666",
  'answertype211691864': "4",
  'answer211691864': "666666",
  'answertype211691865': "4",
  'answer211691865': "666666",
  'answertype211691866': "4",
  'answer211691866': "666666",
  'answertype211691867': "4",
  'answer211691867': "666666",
  'answertype211691868': "4",
  'answer211691868': "666666",
  'answertype211691869': "4",
  'answer211691869': "666666",
  'answertype211691871': "4",
  'answer211691871': "666666"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/javascript, */*; q=0.01",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'sec-ch-ua-platform': "\"Windows\"",
  'X-Requested-With': "XMLHttpRequest",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'Origin': "https://mooc1.chaoxing.com",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Referer': "https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork?courseId=228836418&classId=64009643&cpi=282371395&workId=24875828&answerId=52094560&standardEnc=eba6203955dc26a6781808c842f604b7&enc=efc4c54fbc52fb7e756541b1331f72d2",
  'Accept-Language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'Cookie': "k8s=1751347563.493.12706.587279; route=1ab934bb3bbdaaef56ce3b0da45c52ed; fid=2790; writenote=yes; doubleSpeedValue=2; videojs_id=8260357; jrose=E06B43EA4531A327AA736AB3737BC051.mooc-p4-2387308155-jzjwp; source=\"\"; _uid=221880762; _d=1751482845912; UID=221880762; vc3=a454agod0VF66B%2B8Oy75nUs8GS2N2ty8jUEWMi%2B7NpLERrpe7ydd2NCI4R1W5%2BwMSICNhL%2Fhvrepic6c7NaUZ0YCyybfNWrKh4Nbakg1H9kg%2FiVvJVk2TLz4dxyQxrfkmLemAvdJvvWhg7iKpeAJ2g5i67xt2YpKH8i3OHsyztE%3Da66dacb9740164f5f3d977ea0ff47b52; uf=da0883eb5260151ed5cd77328d409813eb3dd150f62496a8c8dcfef3f8d40f9980daa5ac6aa7d14f1db27cf8906ebabc81a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba0790730943c5c914713028f1ec42bf71b1188854805578cc7988116ae8984b028e33b0a5f2ba96c71a0d8eef657fc076d342747f5d0eac2fc7b9a467b233c21a4df7ff280fcb29d10d8a4c92b12beb4b44156174a8045981e87e69aadf3839ce560d457d8c24c6dfe7fafd565af53bf2; cx_p_token=cad34a0d2b89a3b7d5d904a98b0222c7; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyMjE4ODA3NjIiLCJsb2dpblRpbWUiOjE3NTE0ODI4NDU5MTMsImV4cCI6MTc1MjA4NzY0NX0.6J9y34l94EvL3GE7M7CzyB2OSK3tVv-0wBlJ_j78IDY; xxtenc=c4885b4f5d02c8246c0260b0959ca202; DSSTASH_LOG=C_38-UN_1612-US_221880762-T_1751482845914; spaceFid=2790; spaceRoleId=3; tl=1; _industry=6"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)