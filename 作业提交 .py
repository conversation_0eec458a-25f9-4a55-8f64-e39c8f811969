import requests

url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb?_classId=107051161&courseid=246628486&token=a6101ba183eb4fcb6d49a8e209070539&totalQuestionNum=b98d0197ccc4960bd512eacddf30e969&ua=pc&formType=post&saveStatus=1&version=1"

payload = {
  'courseId': "246628486",
  'classId': "107051161",
  'knowledgeid': "0",
  'cpi': "352747128",
  'workRelationId': "43209149",
  'workAnswerId': "54053387",
  'jobid': "",
  'standardEnc': "13e6b0680604774a548a0668579a05d9",
  'enc_work': "a6101ba183eb4fcb6d49a8e209070539",
  'totalQuestionNum': "b98d0197ccc4960bd512eacddf30e969",
  'pyFlag': "",
  'answerwqbid': "213686872,213686873,213686874,213686875,213686876,213686877,213686878,213686879,213686880,213686881,213686882,213686883,213686884,213686885,213686886,213686887,213686888,213686889,213686890,213686891,213686892,213686903,213686904,213686905,213686906,213686907,213686908,213686909,213686910,213686911,213686912,213686913,213686893,213686894,213686895,213686896,213686897,213686898,213686899,213686900,213686901,213686902,213686914,213686915,213686916,213686917,213686918,213686919,213686920,213686921,213686922,213686923,213686924,",
  'mooc2': "1",
  'randomOptions': "false",
  'workTimesEnc': "",
  'answertype213686872': "0",
  'answer213686872': "A",
  'answertype213686873': "0",
  'answer213686873': "B",
  'answertype213686874': "0",
  'answer213686874': "C",
  'answertype213686875': "0",
  'answer213686875': "D",
  'answertype213686876': "0",
  'answer213686876': "A",
  'answertype213686877': "0",
  'answer213686877': "C",
  'answertype213686878': "0",
  'answer213686878': "B",
  'answertype213686879': "0",
  'answer213686879': "B",
  'answertype213686880': "0",
  'answer213686880': "B",
  'answertype213686881': "0",
  'answer213686881': "C",
  'answertype213686882': "0",
  'answer213686882': "D",
  'answertype213686883': "0",
  'answer213686883': "A",
  'answertype213686884': "0",
  'answer213686884': "D",
  'answertype213686885': "0",
  'answer213686885': "A",
  'answertype213686886': "0",
  'answer213686886': "B",
  'answertype213686887': "0",
  'answer213686887': "C",
  'answertype213686888': "0",
  'answer213686888': "D",
  'answertype213686889': "0",
  'answer213686889': "B",
  'answertype213686890': "0",
  'answer213686890': "B",
  'answertype213686891': "0",
  'answer213686891': "B",
  'answertype213686892': "0",
  'answer213686892': "B",
  'answertype213686903': "0",
  'answer213686903': "D",
  'answertype213686904': "0",
  'answer213686904': "D",
  'answertype213686905': "0",
  'answer213686905': "A",
  'answertype213686906': "0",
  'answer213686906': "D",
  'answertype213686907': "0",
  'answer213686907': "C",
  'answertype213686908': "0",
  'answer213686908': "B",
  'answertype213686909': "0",
  'answer213686909': "A",
  'answertype213686910': "0",
  'answer213686910': "A",
  'answertype213686911': "0",
  'answer213686911': "B",
  'answertype213686912': "0",
  'answer213686912': "D",
  'answertype213686913': "0",
  'answer213686913': "C",
  'answertype213686893': "1",
  'answer213686893': "A",
  'answertype213686894': "1",
  'answer213686894': "A",
  'answertype213686895': "1",
  'answer213686895': "A",
  'answertype213686896': "1",
  'answer213686896': "A",
  'answertype213686897': "1",
  'answer213686897': "A",
  'answertype213686898': "1",
  'answer213686898': "B",
  'answertype213686899': "1",
  'answer213686899': "A",
  'answertype213686900': "1",
  'answer213686900': "A",
  'answertype213686901': "1",
  'answer213686901': "A",
  'answertype213686902': "1",
  'answer213686902': "A",
  'answertype213686914': "1",
  'answer213686914': "B",
  'answertype213686915': "1",
  'answer213686915': "A",
  'answertype213686916': "1",
  'answer213686916': "A",
  'answertype213686917': "1",
  'answer213686917': "A",
  'answertype213686918': "1",
  'answer213686918': "B",
  'answertype213686919': "1",
  'answer213686919': "A",
  'answertype213686920': "1",
  'answer213686920': "C",
  'answertype213686921': "1",
  'answer213686921': "A",
  'answertype213686922': "1",
  'answer213686922': "A",
  'answertype213686923': "1",
  'answer213686923': "A",
  'answertype213686924': "1",
  'answer213686924': "A"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/javascript, */*; q=0.01",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'sec-ch-ua-platform': "\"Windows\"",
  'X-Requested-With': "XMLHttpRequest",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'Origin': "https://mooc1.chaoxing.com",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Referer': "https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork?courseId=246628486&classId=107051161&cpi=352747128&workId=43209149&answerId=54053387&standardEnc=13e6b0680604774a548a0668579a05d9&enc=392f27840bc0e74ca75e6052af5b6663",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "k8s=1752227079.986.16817.254016; jrose=B58CDBB2DEF0188F1A672A3860AD9A22.mooc-1340034249-3kgn8; route=384a56f0aa1d1c34a64006dc82a9a2b0; fid=1964; _uid=302997781; _d=1752227091159; UID=302997781; vc3=fuWc9kgAMSnKBkrTqJ%2FZMbmJSBCDHu5X3WPVteCOD%2FBK3SWZHJQHWhvzAE0KUw%2FZlbKn6DQzvu41GeOa1nSysev4JBdyi8JdqHHo5HZNaJIgoAXfrvV%2FmiFMLKA%2FBVT0Laku5IXzO9lZqWQUjgtm5KEDrQ2j5ywkK4UGBLBB73M%3Db2ffc51d2e7c9d3378adde9e9a5f2579; uf=b2d2c93beefa90dc581a9334c2e96eb176491bd6d9206ab59002b251610203f4cd2f93f22311efc40b83f93837622a6781a6c9ddee30899fd807a544f7930b6aed1e6c11a143bb563b0339d97cdac4ba3a28bffd8379c226e5851b744f8aa02c9fb3947ed09a594cad2ebcfb3d26a3a7a36fd6195cbdcb77fc9b9f804e8e2709622a75f3949237bf2e70bf81838ea52170b5a05e402d2a6370184964ffe8c27c7508d4699207f393c81b8292d6cad699b1f899d50c1c3fa3aa2ebad65cd196bb; cx_p_token=c8fde10a0ee55fd4f4b65e68c3b98b80; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIzMDI5OTc3ODEiLCJsb2dpblRpbWUiOjE3NTIyMjcwOTExNjEsImV4cCI6MTc1MjgzMTg5MX0.SueSbKshkZTkp7uUY5FZXPDCam1C7qsHcCgEu7Zzb6Q; xxtenc=165153d898319081d672d6c69e190e96; DSSTASH_LOG=C_38-UN_311-US_302997781-T_1752227091161"
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)