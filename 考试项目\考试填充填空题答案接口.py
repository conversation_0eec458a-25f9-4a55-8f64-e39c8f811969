import requests

url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionSubmitTestNew?classId=*********&courseId=*********&cpi=*********&testPaperId=7705392&testUserRelationId=*********&tempSave=true&pos=ed07ec23a2d4fedc0d04d6ea38&rd=0.3811935279530314&value=(732%7C756)&qid=885039976&_edt=1754560531671249&version=1&view=json&_csign=0&_signcode=undefined&_signc=undefined&_signe=undefined&_signk=undefined&_cxcid=undefined&_cxtime=undefined&_signt=undefined"

payload = {
  'courseId': "*********",
  'paperId': "391485590",
  'testPaperId': "7705392",
  'examCreateUserId': "434778097",
  'feedbackEnc': "",
  'testUserRelationId': "*********",
  'tId': "7705392",
  'subCount': "",
  'remainTime': "467",
  'encRemainTime': "478",
  'encLastUpdateTime': "*************",
  'tempSave': "true",
  'timeOver': "false",
  'type': "0",
  'classId': "*********",
  'enc': "ee3ef10913b863a3c7c2414fd7dbd4ef",
  'examsystem': "0",
  'start': "45",
  'userId': "*********",
  'randomOptions': "false",
  'cpi': "*********",
  'openc': "c2b71668132c2c1f83060ce2a2d20584",
  'enterPageTime': "1754560522018",
  'questionId': "885039976",
  'questionScore': "3.0",
  'type885039976': "2",
  'score885039976': "3.0",
  'exitdtime': "0",
  'monitorforcesubmit': "0",
  'answeredView': "0",
  'paperGroupId': "0",
  'typeName885039976': "填空题",
  'answerEditor8850399761': "<p>这里输入填充的答案</p>",
  'blankNum885039976': "1,"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/javascript, */*; q=0.01",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'sec-ch-ua-platform': "\"Windows\"",
  'X-Requested-With': "XMLHttpRequest",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'Origin': "https://mooc1.chaoxing.com",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Referer': "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?keyboardDisplayRequiresUserAction=1&getTheNextQuestion=1&courseId=*********&classId=*********&tId=7705392&id=*********&p=1&start=45&remainTimeParam=478&relationAnswerLastUpdateTime=*************&enc=ee3ef10913b863a3c7c2414fd7dbd4ef&monitorStatus=0&monitorOp=-1&examsystem=0&qbanksystem=0&qbankbackurl=&cpi=*********&openc=c2b71668132c2c1f83060ce2a2d20584&newMooc=true&webSnapshotMonitor=0",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "k8sexam=**********.995.2157.770768; jrose=3178B0FB2BE8CCA1A45FF424FB6C0581.mooc-exam-**********-nglqs; fid=186199; k8s=**********.041.3350.205515; jrose=DF8505FBAD4E3DE00BFB7EAAD175FCBF.mooc-**********-b1dc9; route=2fe558bdb0a1aea656e6ca70ad0cad20; _uid=*********; _d=*************; UID=*********; vc3=SZ9tQKD2%2BLAbjtMZz2ER%2BvmqPihD4WSPLVCbo3VQaJcXbv3vTk5GRkgZz4ihgS7gzX23pSMv54D4%2FO80eVZYqi1lIMfJWcyLsNSr13F5ioebA8zIKInSVSiQcLOZ%2F1OPBjUm9i3PfR8sz5naZosSFHAQ%2BYveqStyIbdxeqXqPP8%3Dd50af9a95a8499951928e3d318fb61ad; uf=b2d2c93beefa90dcc2cc18ee0bc212bb032b60b19ecb2cc7277929211cdc9f74b5bfe39a28794b8d9d480c8213f89991982d154fbbcea007ea4a1670a3a8352fe9295d8c89b08ad0f44425e20f927c6b585575080c0461d5e5851b744f8aa02c9fb3947ed09a594c8a2b614e66074036c199071ae5f72ef87496bb565e5488f8ea10b9170fd5654a03ab7c69f7cfdd0a70b5a05e402d2a6370184964ffe8c27c8385f50b603e62dcddc0ec7d30069426b1f899d50c1c3fa3aa2ebad65cd196bb; cx_p_token=0c3e992801ab0b21028906b8ccf1082d; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.0SP5IsBFfnNOvDwrJdYGwpmAtYXdRM-95WPwjsCG37Y; xxtenc=901686f415ab5434c8b90d51ed8458c1; DSSTASH_LOG=C_38-UN_0-US_*********-T_1754555574143; source=\"\"; spaceFid=186199; spaceRoleId=\"\""
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)