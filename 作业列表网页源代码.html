
<!DOCTYPE html>
<html lang="en">

<script>
    _HOST_ = "//mooc1.chaoxing.com";
    _CP_ = "/mooc-ans";
    _HOST_CP1_ = "//mooc1.chaoxing.com/mooc-ans";
    // _HOST_CP2_ = _HOST_ + _CP_;
    _HOST_CP2_ = _CP_;
</script><head>
<meta charset="UTF-8">
<title>作业列表</title>
<link href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/work-list.css?v=2024-0913-1500" type="text/css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/mooc-ans/mooc2/css/taskStudent_pop.css" />
<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/common/jquery.min.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/mooc-ans/js/common/jquery-migrate.min.js"></script><script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/js/jquery.nicescroll.min.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/poplayout.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/mooc-ans/mooc2/js/xlPaging.js"></script>
<script src="//mooc1.chaoxing.com/mooc-ans/js/ServerHost.js"></script>
<style>
.check_repeat {display:inline-block;padding: 0 12px;background: #F2F4F7;border-radius:35px;line-height: 30px;color: #474C59;font-size: 14px;cursor: pointer;}
.check_repeat i{display:inline-block;width:94px;height:18px;float:left;margin:6px 10px 0 0;}
.check_repeat i img{width:100%;height:100%;display: block;}
/*无障碍元素隐藏*/
.element-invisible-hidden {
	position: absolute !important;
	clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
	clip: rect(1px, 1px, 1px, 1px);
}
</style>
</head>
<body style="background: transparent;">
<div id="workFocus" tabindex="0" aria-label=" 作业已刷新，请按tab键" role="option"></div>
<div class="box">
    <div class="content">
        <div class="main task-list">
            <div class="top-back" tabindex="0">
                <h2>建筑22102</h2>
            </div>
            
            <input type="hidden" id="courseId" value="228836418" />
			<input type="hidden" id="classId" value="64009643" />
			<input type="hidden" id="cpi" value="282371395" />
			<input type="hidden" id="enc" value="550114ba5f5bf0c61d3f650fe04350da" />
			<input type="hidden" id="status" value="0" />
			<input type="hidden" id="topicId" value="0" />

            <div class="has-content" style="display: block;" tabindex="-1">
                <div class="filter">
					<div class="check_repeat fr"  onclick="gotoDaya();"><i><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/daya.png"></i>提交的作业将经过大雅相似度分析系统，请勿抄袭</div>
										<span class="title">筛选</span>
					<span class="ipt-radio circl-choosed">
						<input name="group-radio" type="radio" onclick="changeStatus(this);" checked  data="0" tabindex="-1"/><i class="icon-radio"></i>
					</span>
					<div class="operate-list-group fs12 color181E33 lineheight20 inlineBlock">全部</div>

					<span class="ipt-radio circl-choosed">
						<input name="group-radio" type="radio" onclick="changeStatus(this);"  data="2" tabindex="-1"/><i class="icon-radio"></i>
					</span>
					<div class="operate-list-group fs12 color181E33 lineheight20 inlineBlock">已完成</div>

					<span class="ipt-radio circl-choosed">
						<input name="group-radio" type="radio" onclick="changeStatus(this);"  data="1" tabindex="-1"/><i class="icon-radio"></i>
					</span>
					<div class="operate-list-group fs12 color181E33 lineheight20 inlineBlock">未完成</div>
					                </div>
                
                                <div class="bottomList" tabindex="0" aria-label="作业列表">
                    <ul>
                    															
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=24875828&answerId=52094560&enc=efc4c54fbc52fb7e756541b1331f72d2" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="立面图的识读 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">立面图的识读</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=24834403&answerId=52094562&enc=d9d4115d51357981b00c88e08a0c1300" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="平面图识读1 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">平面图识读1</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        																													
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=24741191&answerId=52032271&enc=36f42613a294a69e7426553adc846827" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="施工图首页及总平面图识图 ; 已完成" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">施工图首页及总平面图识图</p>
                                                                <div class="clear"></div>
                                <p class="status fl">已完成</p>

								
																<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=228836418&cpi=282371395&clazzid=64009643&ut=s&relationId=24741191&type=2" class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        																													
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=24684457&answerId=52016577&enc=0ad84949b5b3f813e5b388dbb4dafc94" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="制图标准练习题 ; 已完成" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">制图标准练习题</p>
                                                                <div class="clear"></div>
                                <p class="status fl">已完成</p>

								
																<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=228836418&cpi=282371395&clazzid=64009643&ut=s&relationId=24684457&type=2" class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        																													
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=24350270&answerId=51934976&enc=d37bc81b22373531fa556ad3bf0bd261" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="作业20221124曲面体投影 ; 待批阅" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">作业20221124曲面体投影</p>
                                                                <div class="clear"></div>
                                <p class="status fl">待批阅</p>

								
																<a href="https://stat2-ans.chaoxing.com/study-knowledge/ans?courseid=228836418&cpi=282371395&clazzid=64009643&ut=s&relationId=24350270&type=2" class="listSubmit fl insightBtn" target="_blank" tabindex="-1"><i><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/insight.png"></i>智能分析</a>
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=24285879&answerId=52094563&enc=bcf5c624c9a568883cd5f2c16e1ae9f3" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="作业20221122作业提交 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">作业20221122作业提交</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=23867235&answerId=51859699&enc=2a8497c963cc05da1ebb8ef61687d783" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="作业20221108课堂作业 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">作业20221108课堂作业</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=23505511&answerId=0&enc=fe31603cabb52e0d0cb5054e7e065ae6" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="20221027课堂作业 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">20221027课堂作业</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=23417675&answerId=54248272&enc=50a9b7e9927c6abc876717c49d97f735" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="作业20221025课堂练习 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">作业20221025课堂练习</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=23252505&answerId=52094557&enc=40bd9d330dcf38eb5cbceb8f9bcbd686" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="作业20221020课堂练习 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">作业20221020课堂练习</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=23182165&answerId=0&enc=9422cb63896a68c9dac6aadc3e4d28fb" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="直线的投影2 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">直线的投影2</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                        														
							
																		
																															
                    	
                    	<li onclick="goTask(this);" data="https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId=228836418&classId=64009643&cpi=282371395&workId=23179460&answerId=0&enc=aa896abfe3dee8fa1a562f08724fe023" onkeydown="if(event.keyCode == 13){goTask(this);}" tabindex="0" aria-label="直线的投影1 ; 未交" role="link" >
                            <div class="tag icon-zy"></div>
                            <div class="right-content">
                                <p class="overHidden2 fl">直线的投影1</p>
                                                                <div class="clear"></div>
                                <p class="status fl">未交</p>

								
								                            </div>
                                                        <div class="clearfix"></div>
                        </li>
                                            </ul>
                </div>
                            </div>
            <div style="width:100%;height:110px;"></div>
            <div class="pagePosition">
				<div class="pageDiv" id="page"></div>
			</div>
			        </div>
    </div>
</div>

<div class="maskDiv popMoveShowHide" style="display:none;" id="recordList">
	<div class="popDiv wid640 popMove">
		<div class="popHead">
			<a href="javascript:;" onclick="closeAnswers();" class="popClose popMoveDele fr" style="margin-top: 5px;"><img src="//mooc1.chaoxing.com/mooc-ans/mooc2/images/popClose.png" /></a>
			<p class="fl" style="color: #181E33;font-size: 18px;">查看作答记录</p>
		</div>
		<div class="het62"></div>
				<div id="content"></div>
	</div>
</div>
<script>

var courseId = $("#courseId").val();
var classId = $("#classId").val();
var cpi = $("#cpi").val();
var enc = $("#enc").val();
var status = $("#status").val();
var topicId = $("#topicId").val();

	$("#page").paging({
		nowPage : 1,
		pageNum : 2,
		buttonNum : 9, // 要展示的页码数量
		callback : function(num) {
			location.href = "/mooc-ans/mooc2/work/list?courseId=" + courseId + "&classId=" + classId + "&cpi=" + cpi 
				+ "&enc=" + enc + "&status=" + status + "&pageNum=" + num + "&topicId=" + topicId;
		}
	});

function goTask(obj) {
	var url = $(obj).attr("data");
	window.open(url, '_blank');
}

function changeStatus(obj) {
	var data = $(obj).attr("data");
	location.href = "/mooc-ans/mooc2/work/list?courseId=" + courseId + "&classId=" + classId + "&cpi=" + cpi + "&enc=" + enc + "&status=" + data;
}

$(function() {
	$(".insightBtn").click(function(event) {
		event.stopPropagation();
	});

	$(".bottomList ul li").each(function() {
		var PLHeight = $(this).find(".right-content p.status").height();
		var PHeight = $(this).find(".right-content p:first-child").height();
		if (PLHeight == null) {
			var marginTop = (40 - PHeight) / 2
			$(this).find(".right-content p:first-child").css("margin-top", marginTop + "px");
		}
	});

	if("0" == "1"){
		tabIntoAccessibleCustom();
	}
});

function tabIntoAccessibleCustom() {
	if("0" == "1"){
		try {
			if (window.top && window.top.accessiblePlugs) {
				// window.top.accessiblePlugs.taggeriframe();
				window.top.accessiblePlugs.update();
			}
		} catch (e) {
			console.log(e)
		}
	}
}

function gotoDaya() {
	var url = ServerHost.dayaDomain;
	window.open(url, "_blank");
}

var recordLock = 0
function answerList(workId, answerId, enc) {
	if(recordLock == 1) {
		return;
	}
	recordLock = 1;

	$.ajax({
		type : "get",
		url : _HOST_CP2_ + "/mooc2/work/answer-list",
		dataType : "html",
		data : {
			courseId : courseId,
			classId : classId,
			cpi : cpi,
			workId : workId,
			answerId : answerId,
			enc : enc
		},
		success : function(data) {
			$("#content").html(data);
			$(".popSetupShowHide").fullFadeIn();
			$("#recordList").show();
			MoveFixed();
		}
	});
}

function closeAnswers() {
	$("#recordList").hide();
	$('.popSetupShowHide').fullFadeOut();
	recordLock = 0;
}

function MoveFixed() {
	$('.popMove').css({
		top : function() {
			return ($(window).height() - $(this).height()) / 2;
		},
		left : function() {
			return ($(window).width() - $(this).width()) / 2;
		}
	});

	$("#boxscroll").niceScroll({
		cursorborder : "",
		cursorcolor : "#D9DDE1",
		boxzoom : false
	});
}

$(function(){
	if("0" == "1"){
		setTimeout(function() {
			$('#workFocus').focus();
		}, 500)
	}
})
</script>
</body>
</html>
