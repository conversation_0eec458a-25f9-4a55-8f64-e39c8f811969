import traceback

from API import Re
from collections import OrderedDict

from loguru import logger
from bs4 import BeautifulSoup
from API.Questionbank import *
import difflib
import json
import re
import time
from API.Xuan import match_answer, sort_answers

def tiankong_quchong(text):
    items = text.split('###')
    unique_items = list(OrderedDict.fromkeys(item.strip() for item in items))
    result = '###'.join(unique_items)

    return result
class StaratWorkTaks:
    def __init__(self,session,couserid,classid,cpi,listid,jobid,kcname,username):
        self.session = session
        self.couserid = couserid
        self.kcname = kcname
        self.classid = classid
        self.cpi = cpi
        self.listid = listid
        self.jobid = jobid
        self.username = username
        self.userid = self.session.Uid()

    def Html_Wkrk(self,html):
        self.html = BeautifulSoup(html,'html.parser')
        if '已批阅' in self.html.find("title").text.strip() or '待批阅' in self.html.find("title").text.strip():
            logger.success(f"ID:{self.username},","本章节已提交")
        else:
            try:
                # 尝试获取常规作业页面的参数
                try:
                    self.workAnswerId = self.html.find("input", {"id": "workAnswerId"}).get("value")
                    self.totalQuestionNum = self.html.find("input", {"id": "totalQuestionNum"}).get("value")
                    self.old = self.html.find("input", {"id": "oldWorkId"}).get("value")
                    self.workRelationId = self.html.find("input", {"id": "workRelationId"}).get("value")
                    self.enc_work = self.html.find("input", {"id": "enc_work"}).get("value")
                except AttributeError:
                    # 尝试获取作业页面的参数 (平台ID 9004)
                    logger.info(f"ID:{self.username},尝试解析作业页面参数")
                    # 查找表单中的隐藏字段
                    self.workAnswerId = self.html.find("input", {"name": "workAnswerId"}).get("value") if self.html.find("input", {"name": "workAnswerId"}) else ""
                    self.totalQuestionNum = self.html.find("input", {"name": "totalQuestionNum"}).get("value") if self.html.find("input", {"name": "totalQuestionNum"}) else ""
                    self.old = self.html.find("input", {"id": "old"}).get("value") if self.html.find("input", {"id": "old"}) else ""
                    self.workRelationId = self.html.find("input", {"name": "workRelationId"}).get("value") if self.html.find("input", {"name": "workRelationId"}) else ""
                    self.enc_work = self.html.find("input", {"id": "enc_work"}).get("value") if self.html.find("input", {"id": "enc_work"}) else ""
                    
                    # 如果仍然无法获取关键参数，尝试从其他位置提取
                    if not self.workAnswerId:
                        self.workAnswerId = self.html.find("input", {"id": "answerId"}).get("value") if self.html.find("input", {"id": "answerId"}) else ""
                    if not self.enc_work:
                        self.enc_work = self.html.find("input", {"id": "enc"}).get("value") if self.html.find("input", {"id": "enc"}) else ""
                
                self.params = dict()
                answerwqbid = list()
                
                # 查找题目元素，支持多种可能的类名
                question_divs = self.html.findAll("div", {"class": "Py-mian1 singleQuesId"})
                if not question_divs:
                    question_divs = self.html.findAll("div", {"class": "questionLi"})
                if not question_divs:
                    question_divs = self.html.findAll("div", {"class": "TiMu"})
                if not question_divs:
                    question_divs = self.html.findAll("div", {"class": "padBom50 questionLi fontLabel singleQuesId"})
                
                logger.info(f"ID:{self.username},找到{len(question_divs)}个题目")
                
                for quest in question_divs:
                    # 尝试多种方式获取题目ID
                    try:
                        self.qid = quest.get("data")
                        if not self.qid:
                            self.qid = quest.get("id", "").replace("question", "")
                        if not self.qid:
                            self.qid = quest.find("input", {"name": re.compile(r"^answertype\d+$")}).get("name").replace("answertype", "")
                    except (AttributeError, TypeError):
                        # 如果无法获取题目ID，生成一个随机ID
                        self.qid = f"q{len(answerwqbid) + 1}"
                        logger.warning(f"ID:{self.username},无法获取题目ID，使用临时ID:{self.qid}")
                    
                    # 尝试多种方式获取题目内容
                    try:
                        question_div = quest.find("h3", {"class": "mark_name colorDeep fontLabel workTextWrap"})
                        if not question_div:
                            question_div = quest.find("div", {"class": "Py-m1-title fs16 fontLabel"})
                        if not question_div:
                            question_div = quest.find("div", {"class": "Zy_TItle clearfix"})
                        if not question_div:
                            question_div = quest.find("div", {"class": "mark_name colorDeep"})
                        if not question_div:
                            question_div = quest.find("h3", {"class": "mark_name colorDeep"})
                        
                        # 获取题目文本，去除序号和题型标签
                        if question_div:
                            question_text = question_div.text.strip()
                            # 移除题目序号
                            question_text = re.sub(r'^\d+\.', "", question_text)
                            # 移除题型标签
                            question_text = re.sub(r'\([^)]*\)', "", question_text)
                            question_text = re.sub(r'（[^）]*）', "", question_text)
                            question = question_text.strip()
                        else:
                            question = ""
                    except AttributeError:
                        question = ""
                        logger.warning(f"ID:{self.username},无法获取题目内容")
                    
                    # 尝试多种方式获取题目类型
                    try:
                        qtp_input = quest.find("input", {"name": f"answertype{self.qid}"})
                        if qtp_input:
                            self.qtp = int(qtp_input.get("value").strip())
                        else:
                            # 尝试从题目元素的class或其他属性推断题型
                            if quest.find("ul", {"class": "Zy_ulTop"}):  # 选择题特征
                                self.qtp = 0 if "单选题" in question else 1
                            elif quest.find("textarea"):  # 简答题特征
                                self.qtp = 4
                            elif quest.find("div", {"class": "Py_answer"}):  # 判断题特征
                                self.qtp = 3
                            else:
                                self.qtp = 0  # 默认为单选题
                    except (AttributeError, ValueError):
                        self.qtp = 0  # 默认为单选题
                        logger.warning(f"ID:{self.username},无法获取题目类型，默认为单选题")
                    
                    # 处理题目中的图片
                    if question_div and question_div.find_all("img", src=True):
                        question += "".join([img['src'] for img in question_div.find_all("img", src=True) if img.get('src')])
                    
                    self.question = Re.strip_title(question)
                    logger.debug(f"{self.qtp}-{self.question}")
                    answer, self.qnum = questionbank(self.kcname, self.question)
                    
                    # 根据题型处理答案
                    if self.qtp == 0 or self.qtp == 1:
                        if answer is not None:
                            try:
                                answer, htmlanswer = self.Xuan(quest, answer)
                                answers = "".join(list(OrderedDict.fromkeys(self.__paixun(answer))))
                            except Exception as e:
                                logger.error(f"ID:{self.username},选择题处理错误: {str(e)}")
                                answers = 'A'
                                htmlanswer = '答案处理错误'
                        else:
                            answers = 'A'
                            htmlanswer = '无答案'
                        logger.success(f"answer:{answers}-{htmlanswer}")
                    elif self.qtp == 2:
                        tk = False
                        if answer is not None:
                            # answer = tiankong_quchong(answer)
                            lenanswer = len(quest.findAll("div", {"input": "blankInp2 answerInput escapeInput"}))
                            if len(answer.split("###")) == lenanswer:
                                tk = True
                        else:
                            answer = list()
                        logger.info(f"answer:{answer}")
                        for a, i in enumerate(quest.findAll("input", {"class": "blankInp2 answerInput escapeInput"})):
                            tkid = i.get('id').replace("answer", "")
                            if tk is True:
                                tk_answers = answer.split("###")[a]
                            else:
                                try:
                                    tk_answers = answer.split("###")[a]
                                except:
                                    tk_answers = ''
                            self.params[f'answerEditor{tkid}'] = f'<p>{tk_answers}</p>'
                        tiankongsize = quest.find("input", {"name": f"tiankongsize{self.qid}"}).get('value')
                        self.params[f'tiankongsize{self.qid}'] = tiankongsize
                    elif self.qtp == 3:
                        if answer is not None:
                            if 'true' in answer or '对' in answer or '正确' in answer:
                                answers = 'true'
                            else:
                                answers = 'false'
                        else:
                            answers = 'true'
                        logger.info(f"answer:{answers}")
                    elif self.qtp == 4 or self.qtp == 5 or self.qtp == 6 or self.qtp == 7 or self.qtp == 8 or self.qtp == 10 or self.qtp == 14:
                        if answer is not None:
                            answers = f'<p>{answer}</p>'
                        else:
                            answers = '<p> </p>'
                    elif self.qtp == 19:
                        token = [i.get("value") for i in quest.findAll("input", {"name": "readCompreHension-childId"})]
                        token_type = [i.get("value") for i in
                                      quest.findAll("input", {"name": "readCompreHension-childType"})]
                        tl_list = {}
                        for a, id in enumerate(token):
                            if answer:
                                try:
                                    tl_answer = answer.split("###")[a]
                                except:
                                    tl_answer = ''
                            else:
                                tl_answer = ''
                            logger.info(f"ID:{self.username},听力{id} - ans:{tl_answer}")
                            self.params[f'answer{id}'] = tl_answer
                            tl_list[id] = {"type": token_type[a], "answer": tl_answer}
                        self.params['readCompreHension-childId'] = '&'.join(token)
                        self.params['readCompreHension-childType'] = '&'.join(token_type)
                        data = json.dumps([tl_list])
                        self.params[f'answer{self.qid}'] = data
                        self.params[f'answertype{self.qid}'] = 19
                    else:
                        answers = 'A'
                        # print(quest)
                    if self.qtp != 2 and self.qtp != 19:
                        self.params[f'answer{self.qid}'] = answers
                    self.params[f'answertype{self.qid}'] = self.qtp
                    answerwqbid.append(self.qid)
                    time.sleep(0.5)
                
                # 如果没有找到任何题目，记录警告
                if not answerwqbid:
                    logger.warning(f"ID:{self.username},未找到任何题目")
                    return
                
                self.params['answerwqbid'] = ','.join(answerwqbid) + ','
                self.PostDo()
                time.sleep(0.5)
            except Exception as e:
                traceback.print_exc()
                logger.info(f"ID:{self.username},章节老师未设置内容或解析失败: {str(e)}")
                
    def Xuan(self,quest,answers,bidui=0.95):
        # 使用增强版的匹配函数
        result = match_answer(quest, answers, self.qtp, self.username, bidui)
        
        # 如果没有匹配到答案，尝试使用其他题库
        if result[0] == ['A'] and result[1] == '答案匹配失败' and answers is not None:
            if self.qnum == 1:
                answers, self.qnum = questionbank2(self.question)
                return self.Xuan(quest, answers, bidui=0.9)
            elif self.qnum == 2:
                answers, self.qnum = fujia(self.question)
                return self.Xuan(quest, answers, bidui=0.9)
            elif self.qnum == 3:
                self.qnum = 4
                return self.Xuan(quest, answers, bidui=0.7)
        
        return result

    def __paixun(self, daan):
        # 使用增强版的排序函数
        return sort_answers(daan)

    def PostDo(self):
        data = {
            "pyFlag": "",
            "courseId": self.couserid,
            "classId": self.classid,
            "api": "1",
            "mooc": "0",
            "workAnswerId": self.workAnswerId,
            "totalQuestionNum": self.totalQuestionNum,
            "fullScore": "100.0",
            "knowledgeid": self.listid,
            "oldSchoolId": "",
            "old": self.old,
            "jobid": self.jobid,
            "workRelationId": self.workRelationId,
            "enc_work": self.enc_work,
            "isphone": "true",
            "userId": self.userid,
            "workTimesEnc": "",
            ** self.params
        }
        
        # 尝试多个可能的提交接口
        try:
            r = self.session.post("https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew", data=data)
            if '''提交失败，参数异常''' in r.text:
                logger.warning(f"ID:{self.username},标准接口提交失败，尝试备用接口")
                # 尝试备用接口
                r = self.session.post("https://mooc1.chaoxing.com/work/addStudentWorkNew", data=data)
            
            if '''提交失败，参数异常''' in r.text:
                logger.error(f"ID:{self.username},所有接口提交失败: {data}")
            else:
                logger.success(f"ID:{self.username},{r.text}")
        except Exception as e:
            logger.error(f"ID:{self.username},提交答案异常: {str(e)}")
            traceback.print_exc()
