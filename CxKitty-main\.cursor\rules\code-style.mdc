# Code Style

## Indentation
- Use 4 spaces for indentation
- Do not use tabs
- Maintain consistent indentation throughout the file

## Line Length
- Maximum line length is flexible but should generally not exceed 100 characters
- Long expressions can be broken into multiple lines for readability

## Naming Conventions
- Class names: CamelCase (e.g., `ChaoXingAPI`, `SessionWraper`)
- Function/method names: snake_case (e.g., `fetch_classes`, `login_passwd`)
- Variable names: snake_case (e.g., `task_point`, `json_content`)
- Constants: UPPER_CASE (e.g., `API_LOGIN_WEB`, `SESSIONS_PATH`)
- Private attributes/methods: prefix with double underscore (e.g., `__captcha_max_retry`)

## Docstrings
- Use triple double quotes for docstrings
- Include Args and Returns sections in function docstrings
- Type hints should be included in function signatures

## Example
```python
class SessionWraper(Session):
    """requests.Session 的封装
    用于处理风控、序列化 ck 和自动重试
    """

    def login_passwd(self, phone: str, passwd: str) -> tuple[bool, dict]:
        """以 web 方式使用手机号+密码账号
        Args:
            phone: 手机号
            passwd: 登录密码
        Returns:
            tuple[bool, dict]: 登录状态和响应内容
        """
        # Implementation
        return True, response_json
```
description:
globs:
alwaysApply: false
---
