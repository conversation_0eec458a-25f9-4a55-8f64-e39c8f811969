import hashlib
import re


# 移除各种空白字符
def remove_blank(text):
    return re.sub(r"[\t\n\r 　 ]+", "", text)


# 移除各种空白字符，但保留括号内的空格
def remove_blank_preserve_spaces_in_brackets(text):
    # 使用正则表达式查找所有括号及其内容
    pattern = r"(\（[^）]*\）|\([^)]*\))"
    brackets = re.findall(pattern, text)

    # 用占位符替换所有括号内容
    placeholder = "BRACKET_PLACEHOLDER_"
    replaced_text = text
    for i, bracket in enumerate(brackets):
        replaced_text = replaced_text.replace(bracket, f"{placeholder}{i}", 1)

    # 移除非括号部分的空白字符
    cleaned_text = re.sub(r"[\t\n\r 　 ]+", "", replaced_text)

    # 恢复括号内容
    for i, bracket in enumerate(brackets):
        cleaned_text = cleaned_text.replace(f"{placeholder}{i}", bracket)

    return cleaned_text


# 修剪课程
def strip_course(course):
    course = remove_blank(course)
    course = re.sub(
        r"\([^()]*\)|（[^（）]*）|【[^【】]*】|\[[^\[\]]*]|\{[^{}]*}", "", course
    )
    course = re.sub(r"[^\u4e00-\u9fa5]", "", course)
    course = re.sub(
        r"副本|重修|春|夏|秋|冬|季|第|学|期|年|级|版|班级|一|二|三|四|五|六|七|八|九|十|上|下",
        "",
        course,
    )
    if not course:
        course = "其他"
    return course


# 中文字符转英文字符
def transform_char(text):
    text = text.replace("，", ",")
    text = text.replace("：", ":")
    text = text.replace("！", "!")
    text = text.replace("？", "?")
    text = text.replace("（", "(")
    text = text.replace("）", ")")
    text = text.replace("【", "[")
    text = text.replace("】", "]")
    text = text.replace("“", '"')
    text = text.replace("”", '"')
    text = text.replace("‘", "'")
    text = text.replace("’", "'")
    return text


# 修剪题目
def strip_title(text):
    text = re.sub(r"<.*?>", "", text)  # 去除HTML标签

    # 检查是否包含填空符号
    has_blank_brackets = False
    if re.search(r"（\s+）|（\s*）|\(\s+\)|\(\s*\)", text):
        has_blank_brackets = True

    if has_blank_brackets:
        # 如果包含填空符号，使用保留括号内空格的函数
        text = remove_blank_preserve_spaces_in_brackets(text)
    else:
        # 否则使用原始函数
        text = remove_blank(text)

    text = transform_char(text)  # 中文转英文
    text = text.replace("'", "")  # 去除英文单引号
    text = re.sub(r"\(\d+\.\d+分\)", "", text)
    return text


# 修剪题目
def strip_title2(text):
    text = re.sub(r"\(\d+\.\d+分\)", "", text)
    text = re.sub(r"<.*?>", "", text)  # 去除HTML标签

    # 检查是否包含填空符号
    has_blank_brackets = False
    if re.search(r"（\s+）|（\s*）|\(\s+\)|\(\s*\)", text):
        has_blank_brackets = True

    if has_blank_brackets:
        # 如果包含填空符号，使用保留括号内空格的函数
        text = remove_blank_preserve_spaces_in_brackets(text)
    else:
        # 否则使用原始函数
        text = remove_blank(text)

    text = transform_char(text)  # 中文转英文
    text = text.replace("'", "")  # 去除英文单引号

    return text


# 修剪选项
def strip_options(text):
    text = transform_char(text)  # 中文转英文
    text = remove_blank(text)  # 移除空白字符
    text = text.replace("'", "")  # 去除英文单引号
    return text
