# Exception Handling

## General Rules
- All network requests must be wrapped in try-except blocks
- File operations should include proper error handling
- Use specific exception types when possible
- Provide meaningful error messages

## Exception Hierarchy
- Use custom exception classes defined in `cxapi.exception` module
- Extend from base exceptions when creating new exception types
- Maintain consistent naming convention for exceptions (e.g., `HandleCaptchaError`)

## Logging
- Use the Logger class for logging exceptions
- Include appropriate log levels (info, warning, error)
- Include exception information in log messages

## Example
```python
try:
    response = self.session.get(url, params=params, headers=headers, timeout=10)
    if response.status_code == 200:
        # 处理成功响应
        return self._process_response(response)
    else:
        logger.error(f"请求失败，状态码: {response.status_code}")
        return None
except Exception as e:
    logger.error(f"请求异常: {str(e)}")
    return None
```

## Retry Mechanism
- Implement retry logic for network operations
- Use exponential backoff when appropriate
- Set maximum retry limits to prevent infinite loops
description:
globs:
alwaysApply: false
---
