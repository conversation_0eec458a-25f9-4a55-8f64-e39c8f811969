<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<script>
	_HOST_ = "//mooc1.chaoxing.com";
	_CP_ = "/exam-ans";
	_HOST_CP1_ = "//mooc1.chaoxing.com/exam-ans";
	// _HOST_CP2_ = _HOST_ + _CP_;
	_HOST_CP2_ = _CP_;
</script>

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title></title>
	<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/common.css?v=2025-0424-1038" />
	<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/style.css?v=2025-0424-1038" />
	<link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/pop.css?v=2021-0311-1412" />
	<link rel="stylesheet" type="text/css"
		href="//mooc1.chaoxing.com/exam-ans/mooc2/css/exam-look.css?v=2024-0723-1447" />
	<script src="//mooc1.chaoxing.com/exam-ans/space/new/js/jquery.min.js"></script>
	<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poptoast.js?v=2021-0917-1623"></script>
	<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poplayout.js"></script>
	<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/js/exam/watermark-part-min.js"></script>
</head>



<body class="resultBody">

	<input type="hidden" id="answerId" name="answerId" value="*********" />
	<input type="hidden" id="examRelationId" name="examRelationId" value="7705392" />
	<input type="hidden" id="courseId" name="courseId" value="*********" />
	<input type="hidden" id="classId" name="classId" value="*********" />
	<input type="hidden" id="cpi" name="cpi" value="*********" />
	<input type="hidden" id="qbanksystem" name="qbanksystem" value="0" />
	<input type="hidden" id="qbankbackurl" name="qbankbackurl" value="" />

	<div class="subNav" tabindex="0" role="option" aria-label="考试 页面">
		考试</div>
	<div class="het40"></div>
	<!--导航结束-->
	<div class="bodyImg"><img tabindex="-1" aria-hidden="true"
			src="//mooc1.chaoxing.com/exam-ans/mooc2/images/result_img.png" /></div>


	<div class="result_Main" style="position:relative;overflow:hidden">
		<div id="watermark-wrapper" class="watermark-wrapper"></div>
		<div style="position:relative;z-index:10">
			<h2 class="fs32 color0 textCenter marBom30" tabindex="0" role="option">交卷成功</h2>

			<h2 class="fs16 color3 textCenter result_number" tabindex="0" role="option">本次成绩<b>100.0</b>分 </h2>
			<div class="textCenter" tabindex="0" role="option">
				<div class="last_number"><i></i>最终成绩<b>100.0</b>分 </div>
			</div>


			<div class="result_gray_div marBom30 fs14 color3" tabindex="0" role="option">
				<p>姓名：张颖姿</p>
				<p>学号：**************</p>
				<p>领取时间：2025-07-23 06:51</p>
				<p>提交时间：2025-07-23 07:02</p>
				<p>考试用时：10 分钟</p>
				<p>提交方式：学生交卷</p>
			</div>

			<div class="textCenter">
				<div class="Retake" tabindex="0" role="option" aria-label='本次考试允许重考2次，已重考0次'><i></i>本次考试允许重考2次，已重考0次<a
						href="javascript:" onclick="jumpRetest()">重考</a></div>
			</div>


			<p class="marBom30 textCenter">
				<a href="/exam-ans/mycourse/transfer?moocId=*********&clazzid=*********&ut=s&refer=%2Fexam-ans%2Fexam%2Ftest%2FreVersionPaperMarkContentNew%3FcourseId%3D*********%26classId%3D*********%26p%3D1%26id%3D*********%26ut%3Ds%26newMooc%3Dtrue%26qbanksystem%3D1%26qbankbackurl%3D%252Fexam-ans%252Fexam%252Ftest%252Flook%253FcourseId%253D*********%2526classId%253D*********%2526examId%253D7705392%2526examAnswerId%253D*********%2526cpi%253D*********"
					class=" btnBlue  fs14 btn_124">查看试卷详情</a>
			</p>

		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:2001;" id="timeOverSubmitConfirmPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">作答时间耗尽，试卷已提交</p>
			<p class="popWord fs16 colorIn" style="margin: 2px 2px;">试卷领取时间：</p>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">考试用时：<span class="consumeMinutes"></span>分钟</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">知道了</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv " style="display:none;z-index:2001;" id="showLoginInfo">
		<div class="popDiv liveEwmPop">
			<div class="popHead">
				<a href="#" class="popClose fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"
						onclick="$('#showLoginInfo').fullFadeOut();" /></a>
				<p class="fl fs18 colorDeep">直播二维码</p>
			</div>
			<div class="het62"></div>
			<div class="livePop_con">
				<div class="fs16 color1 liveweminfo">
					<p>账号：<span class="color0" id="loginName"></span></p>
					<p>密码：<span class="color0" id="password"></span></p>
				</div>
				<dl class="pop_ewm">
					<dd>二维码仅考试期间有效</dd>
					<dt><img id="ewmUrl" src="" onclick="rereshSecondDeviceQRCode()" /></dt>
				</dl>
			</div>

		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:2000;" id="submitConfirmPop" tabindex="0" role="alertdialog"
		aria-label="交卷">
		<div class="popDiv wid440 Marking" style="left:39%;top:30%;width:450px;min-height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep" tabindex="0" role="option">提示</p>
				<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
					<img tabindex="-1" aria-hidden="true" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"
						onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">
				</a>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 30px 2px;" tabindex="0" role="option">确认交卷？</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">确定</a>
				<a href="javascript:" class="btnBlue btn_92_cancel fr fs14"
					onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">取消</a>
			</div>
			<div class="het72" tabindex="0" role="option" aria-label="弹窗结尾"></div>
		</div>
	</div>


	<div class="maskDiv" style="display:none;z-index:1000;" id="audioLimitTimesWin">
		<div class="popSetDiv wid440">
			<div class="popHead RadisTop">
				<a href="javascript:;" class="popClose fr" onclick="$('#audioLimitTimesWin').fullFadeOut();"><img
						src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 color1">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 color2 audioLimitTimesTip">此附件仅支持打开 <span></span> 次，你已打开 <span></span> 次，不能再次打开</p>
			<div class="popBottom RadisBom">
				<a href="javascript:;" class="jb_btn jb_btn_92 fr fs14"
					onclick="$('#audioLimitTimesWin').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv popMoveShowHide" id="confirmEnterWin" style="display:none;z-index:1000;" tabindex="0"
		role="alertdialog" aria-label="进入考试">
		<div class="popDiv wid440 popMove">
			<div class="popHead">
				<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
					<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"
						onclick="$('#confirmEnterWin').fullFadeOut();" />
				</a>
				<p class="fl fs18 colorDeep" style="font-size:18px;" tabindex="-1">提示</p>
			</div>
			<div class="het62"></div>
			<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option">
				<div class=" tip" style="line-height:26px;font-size:16px;min-height: 140px;width:100%;"></div>
			</div>
			<div class="popBottom">
				<a tabindex="0" role="button" id="tabIntoexam2" href="javascript:;"
					class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">进入考试</a>
				<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14"
					onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">取消</a>
			</div>
			<div class="het72" tabindex="0" role="option" id="confirmEnterWinEnd" aria-label="弹窗结尾"></div>
		</div>
	</div>


	<div class="maskDiv" style="display:none;z-index:1000;" id="multiTerminalWin">
		<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:300px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:18px 2px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">继续考试</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel"
					onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">退出考试</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>



	<div class="maskDiv" style="display:none;z-index:1000;" id="examTipsPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#examTipsPop').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="singleQuesLimitTimePop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
				本题作答时间已用完，将进入下一题 </div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="singleQuesLimitTimeConfirm();"> 确定 </a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimePop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
				当前考试需按分卷顺序作答，确认进入下一个分卷？ </div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
				<a href="javascript:" class="btnBlue btn_92_cancel fr fs14"
					onclick="$('#groupPaperLimitTimePop').fullFadeOut();$('#groupStart').val(0)">取消</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimeOverPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
				当前分卷作答时间已用完，将进入下一分卷 </div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="groupPaperLimitTimeSubmitPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
				当前分卷需限时耗尽才允许提交</div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="switchScreenPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
			</div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#switchScreenPop').fullFadeOut();">确定</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>


	<div class="maskDiv popMoveShowHide" id="confirmRetestWin" style="display:none;z-index:1000;" tabindex="0"
		role="alertdialog" aria-label="重考提示">
		<div class="popDiv wid440 popMove">
			<div class="popHead">
				<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭">
					<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png"
						onclick="$('#confirmRetestWin').fullFadeOut();" /></a>
				<p class="fl fs18 colorDeep" style="font-size:18px;">提示</p>
			</div>
			<div class="het62"></div>
			<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option">
				<div class=" tip" style="line-height:26px;font-size:16px;min-height: 80px;width:100%;">
					开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最高成绩为最终成绩，请确认是否重考？</div>
			</div>
			<div class="popBottom">
				<a tabindex="0" role="button" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm"
					onclick="">确定重考</a>
				<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14"
					onclick="$('#confirmRetestWin').fullFadeOut();" style="width:88px;">取消</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>


	<div class="maskDiv" style="display:none;z-index:1000;" id="exitTimesSubmitPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"
				data="系统检测到你已离开考试{1}次，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#exitTimesSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="exitDurationSubmitPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"
				data="系统检测到你的切屏时长已超过{1}秒，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#exitDurationSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:2001;" id="teacherNoticePop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">教师提醒</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 10px 2px; height:200px;overflow:auto;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#teacherNoticePop').fullFadeOut()">知道了</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>



	<div class="maskDiv taskStatusShowHide" style="display:none;z-index:1000;" id="stuSelfTestAutoPaper">
		<div class="popDiv wid440 centered">
			<div class="popHead">
				<a href="javascript:" class="popClose popMoveDele fr"><img
						src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="barDiv">
				<div class="barCon" style="width:10%"></div>
			</div>
			<p class="barInfo" style="margin-bottom:20px">自测试卷生成中，已完成<span id="taskrate">0%</span></p>
			<div class="popBottom">
			</div>
			<div class="het72"></div>
		</div>
	</div>



	<div class="maskDiv" style="display:none;z-index:1000;" id="faceRecognitionComparePop">
		<div class="popDiv iden_resultPop">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#faceRecognitionComparePop').fullFadeOut();"><img
						src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 colorDeep">识别结果</p>
			</div>
			<div class="het62"></div>
			<div class="face_Box face_compared">
				<p class="contrastTit textCenter face_result"><i class="icon"></i><span class="tip"></span></p>
				<div class="contrastImgBox textCenter">
					<dl>
						<dt><img src="" class="currentFaceId"></dt>
						<dd>本次采集</dd>
					</dl>
					<dl class="greenDL">
						<dt><img src="" class="collectedFaceId"></dt>
						<dd class="archivePhoto">档案照片</dd>
					</dl>
				</div>
				<div class="face_video_btn textCenter marTop60 face_fail_actionbtn">
					<a href="javascript:" class="disble_time_btn jb_btn_158 fs14 reFaceRecognition">重新识别</a>
				</div>
			</div>
			<div class="face_Box face_comparing">
				<p class="contrast_loading"><i><img
							src="//mooc1.chaoxing.com/exam-ans/mooc2/images/load.png"></i>人脸比对中...</p>
			</div>
		</div>
	</div>


	<div class="maskDiv" style="display:none;z-index:1000;" id="forceSubmitTip">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#forceSubmitTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
		</div>
	</div>



	<div class="maskDiv" style="display:none;z-index:1000;" id="examAddTimeRemindTip">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;max-height:360px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">延时提醒</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;">该考试教师进行延时操作</p>
			<p class="popWord fs16 colorIn minutes" style="margin:2px 2px;" data="延时时长：[1]分钟"></p>
			<p class="popWord fs16 colorIn remind"
				style="margin:6px 2px;overflow: auto;max-height:160px;word-break: break-all;" data="延时原因：[1]"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#examAddTimeRemindTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
		</div>
	</div>


	<div class="maskDiv" style="display:none;" id="confirmPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#confirmPop').fullFadeOut();"><img
						src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">删除后将无法恢复，确认删除？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmDelete" onclick="">确定</a>
				<a href="javascript:" onclick="$('#confirmPop').fullFadeOut();"
					class="btnBlue btn_92_cancel fr fs14">取消</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;" id="examWrongQuesPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#examWrongQuesPop').fullFadeOut();"><img
						src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">您有正在进行的错题练习，是否继续上次作答？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 continueAnswer" onclick="">继续作答</a>
				<a href="javascript:" onclick="" class="btnBlue btn_92_cancel fr fs14 reAnswer">重刷</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>


	<div class="maskDiv" style="display:none;z-index:1000;" id="lockExamWin">
		<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">申诉</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick=""
					style="width:88px;">退出考试</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="exitexamForcesubmitWin">
		<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;">退出考试将强制收卷，确认退出?</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">退出</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel"
					onclick="$('#exitexamForcesubmitWin').fullFadeOut();" style="width:88px;">取消</a>
			</div>
			<div class="het72"></div>
		</div>
	</div>

	<div class="maskDiv" style="display:none;z-index:1000;" id="exitexamForcesubmitPop">
		<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn"
				style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;">系统检测到曾退出考试，将强制收卷</div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose"
					onclick="$('#exitexamForcesubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
		</div>
	</div>


	<script type="text/javascript">
		$(function () {
			function MoveFixed() {
				$('.popMove').css({
					top: function () {
						return ($(window).height() - 440) / 2;
					}, left: function () {
						return ($(window).width() - $(this).width()) / 2;
					}
				});
			}
			setTimeout(function () {
				MoveFixed();
			}, 500);

			if ("0" == "1") {
				var firstFocus = $(".subNav");
				if (firstFocus) {
					setTimeout(function () {
						firstFocus.eq(0).focus();
					}, 300)
				}
			}
			watermark({ "watermarl_element": "watermark-wrapper", "watermark_txt": "fca7d22e", "watermark_width": 80, "watermark_height": 50, "watermark_x": 40, "watermark_y": 20, "watermark_angle": -15, "watermark_alpha": 0.08, "watermark_fontsize": "16px" });
		});
	</script>

	<script type="text/javascript">

		function examNotesUrl() {
			var courseId = $('#courseId').val();
			var examRelationId = $('#examRelationId').val();
			var clazzId = $('#classId').val();
			var cpi = $("#cpi").val();
			var url = _HOST_CP2_ + '/exam/test/examcode/examnotes?examId=' + examRelationId + '&courseId=' + courseId + '&classId=' + clazzId + '&cpi=' + cpi;
			var qbanksystem = $('#qbanksystem').val();
			var qbankbackurl = $('#qbankbackurl').val();
			if (qbanksystem == 1 && qbankbackurl != '') {
				url = url + '&qbanksystem=' + qbanksystem + '&qbankbackurl=' + qbankbackurl;
			}
			return url;
		}


		function jumpRetest() {
			var url = examNotesUrl();
			url = url.replace('?', '?reset=true&');
			location.href = url;
		}

		/*
		function retestClick(){
			var tip = '请确认是否重考？';
			var confirm = '确定';
			$('#confirmRetestWin .tip').html(tip);
			$('#confirmRetestWin .confirm').html(confirm);
			$('#confirmRetestWin .confirm').attr('onclick', 'retest()');
			$("#confirmRetestWin").fullFadeIn();
		}
		
		function retest(){
		   var callBack = function(){
			 location.href = examNotesUrl();
		   };
		   reTestAction(callBack);
		}
		
		function reTestAction(callBack) {
			var url = _HOST_CP2_ + "/exam/test/reVersionReTest?courseId=*********&classId=*********&tId=7705392&id=*********";
			$.ajax({
				type: "get",
				url: url,
				dataType : 'json',
				success: function (data) {
					var status = data.status;
					if (status) {
						callBack && callBack();
					} else {
						$.toast({type : 'failure', content : '重考失败!'});
						location.reload();
					}
				},
				error: function (data) {
					$.toast({type : 'failure', content : '重考失败!'});
					location.reload();
				}
			});
		}
		*/

		function getExamRank() {
			var classId = $("#classId").val();
			var courseId = $("#courseId").val();
			var answerId = $("#answerId").val();
			var cpi = $("#cpi").val();
			$.ajax({
				type: "get",
				dataType: "json",
				url: _HOST_CP2_ + "/exam/test/myExamRank",
				data: {
					"classId": classId,
					"courseId": courseId,
					"answerId": answerId,
					"cpi": cpi
				},
				success: function (data) {
					if (data.status) {
						var content = "排名：" + data.examRank;
						$(".rank").html(content);
					} else {
						$.toast({ type: 'failure', content: data.msg });
					}
				}
			});
		}
	</script>
</body>

</html>