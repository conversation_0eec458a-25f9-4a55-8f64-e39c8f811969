<?php
include('confing/common.php');
$ckxz = $DB->get_row("select settings,api_ck,api_xd,api_proportion from qingka_wangke_config");
$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;
@header('Content-Type: application/json; charset=UTF-8');
if ($conf['settings'] != 1) {
	exit('{"code":-1,"msg":"API功能已关闭，请联系管理员！"}');
} else {
	switch ($act) {
		case 'verify': //用户登录验证
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			
			if ($uid == '' || $key == '') {
				exit(json_encode(['status' => 'error', 'message' => '用户ID或密钥不能为空']));
			}
			
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if (!$row) {
				// 用户不存在
				wlog(0, "登录验证", "用户{$uid}不存在", 0);
				exit(json_encode(['status' => 'error', 'message' => '用户ID不存在']));
			} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "登录验证", "密钥错误", 0);
				exit(json_encode(['status' => 'error', 'message' => '密钥错误']));
			} else {
				// 验证成功，记录日志
				wlog($uid, "登录验证", "登录成功，欢迎{$row['name']}", 0);
				exit(json_encode([
					'status' => 'success',
					'message' => '登录成功',
					'user' => [
						'uid' => $uid,
						'name' => $row['name']
					]
				]));
			}
			break;
		case 'getmoney'://查询当前余额
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			if ($uid == '' || $key == '') {
				exit('{"code":0,"msg":"所有项目不能为空"}');
			}
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				$result = array(
					'code' => 1,
					'msg' => '查询成功',
					'money' => $row['money']
				);
				exit(json_encode($result));
			}
			break;
		// 版本更新后台
		case 'getupdateinfo':
			$uid = trim(strip_tags(addslashes($_POST['uid'])));
			$key = trim(strip_tags(addslashes($_POST['key'])));

			if ($uid == '' || $key == '') {
				exit('{"code":0,"msg":"小本生意，请根据要求来可以么"}');
			}

			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");

			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				$new = isset($conf['banbennr']) ? $conf['banbennr'] : '默认内容';
				$version = isset($conf['banben']) ? $conf['banben'] : '当前版本';
				$url = isset($conf['banbenurl']) ? $conf['banbenurl'] : '安装包地址';


				$latestVersion = [
					"code" => "1",
					"version" => $version,
					"url" => "$url",
					"new" => $new
				];

				$result = array(
					'code' => 1,
					'msg' => '查询成功',
					'data' => $latestVersion
				);
				exit(json_encode($result));
			}
			break;
		// 版本更新
		case 'updateversion':
			$uid = trim(strip_tags(addslashes($_POST['uid'])));
			$key = trim(strip_tags(addslashes($_POST['key'])));
			$newVersion = trim(strip_tags(addslashes($_POST['newVersion'])));

			if ($uid == '' || $key == '' || $newVersion == '') {
				exit('{"code":0,"msg":"参数错误，请根据要求来可以么"}');
			}

			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");

			if ($row['key'] == '0') {
				$result = array("code" => -1, "msg" => "你还没有开通接口哦");
				exit(json_encode($result));
			} elseif ($row['key'] != $key) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			} else {
				// 查询当前的k值
				$configRow = $DB->get_row("SELECT k FROM qingka_wangke_config WHERE v='banben'");

				if (!$configRow) {
					exit('{"code":-3,"msg":"配置信息未找到"}');
				}

				// 更新数据库中的版本信息
				$updateResult = $DB->query("UPDATE `qingka_wangke_config` SET k='{$newVersion}' WHERE v='banben'");

				if ($updateResult) {
					$result = array("code" => 1, "msg" => "版本更新成功");
				} else {
					$result = array("code" => -3, "msg" => "版本更新失败");
				}

				exit(json_encode($result));
			}
			break;
		case 'verify_key': // 验证key是否有效
			$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			
			if (empty($key)) {
				exit(json_encode(array("code" => 0, "msg" => "请提供要验证的Key")));
			}
			
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if ($row) {
				// 记录验证成功日志
				wlog($row['uid'], "Key验证", "Key验证成功，欢迎{$row['name']}", 0);
				exit(json_encode(array("code" => 1, "msg" => "Key验证成功")));
			} else {
				// 记录验证失败日志
				wlog(0, "Key验证", "Key验证失败：{$key}", 0);
				exit(json_encode(array("code" => 0, "msg" => "Key不存在，请前往蜜雪激活")));
			}
			break;
		case 'query': // 题库查询
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			$question = trim(strip_tags(daddslashes($_POST['question'])));
			
			// 调试日志
			error_log("Debug - 接收到的参数:");
			error_log("key: " . $key);
			error_log("question: " . $question);
			
			// 验证参数
			if ($key == '' || $question == '') {
				error_log("Debug - 参数验证失败");
				exit('{"code":0,"msg":"所有项目不能为空"}');
			}

			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				$result = array("code" => -2, "msg" => "密匙错误");
				exit(json_encode($result));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 保存原始问题用于后续处理
			$original_question_raw = $question;
			
			// 预处理：去除分数标记，如"(5.0)"、"（5.0）"等
			$no_score_question = preg_replace('/\s*[\(（][\s]*\d+(\.\d+)?[\s]*[\)）]\s*$/', '', $original_question_raw);
			error_log("Debug - 去除分数标记后的问题: " . $no_score_question);
			
			// 清理问题文本，移除特殊字符以提高匹配率
			$cleaned_question = $question;
			$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			$cleaned_question = trim($cleaned_question); // 移除首尾空格
			
			// 移除题型前缀，如"(单选题)"、"（多选题）"等
			$cleaned_question = preg_replace('/^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：]*[\)）\]\】]?\s*/u', '', $cleaned_question);
			
			// 移除各种括号和标点符号，包括填空符号"（ ）"
			$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！', '（ ）', '( )'), '', $cleaned_question);
			
			// 提取核心问题（去掉可能的选项部分）
			$core_question = $cleaned_question;
			if (mb_strlen($core_question, 'UTF-8') > 15) {
				// 如果问题较长，尝试提取前半部分作为核心问题
				$core_question = mb_substr($core_question, 0, min(mb_strlen($core_question, 'UTF-8'), 30), 'UTF-8');
			}
			
			// 创建一个版本的问题，其中逗号替换为填空符号，用于处理特殊情况
			$comma_to_blank = preg_replace('/，将，/u', '，将（ ），', $original_question_raw);
			$comma_to_blank = preg_replace('/,将,/u', ',将（ ）,', $comma_to_blank);
			
			// 创建去除分数标记后的清理版本
			$cleaned_no_score = '';
			if ($no_score_question != $original_question_raw) {
				$cleaned_no_score = preg_replace('/\s+/', ' ', $no_score_question); // 合并多个空格为一个
				$cleaned_no_score = trim($cleaned_no_score); // 移除首尾空格
				$cleaned_no_score = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！', '（ ）', '( )'), '', $cleaned_no_score);
				error_log("Debug - 去除分数标记后的清理问题: " . $cleaned_no_score);
			}
			
			error_log("Debug - 清理后的问题: " . $cleaned_question);
			error_log("Debug - 核心问题: " . $core_question);
			error_log("Debug - 逗号转填空符号: " . $comma_to_blank);
			
			// 查询题库 - 使用多种查询方式
			$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			$core_question = $DB->escape($core_question); // 核心问题（SQL注入防护）
			$comma_to_blank = $DB->escape($comma_to_blank); // 逗号转填空符号版本（SQL注入防护）
			$no_score_question = $DB->escape($no_score_question); // 去除分数标记版本（SQL注入防护）
			$cleaned_no_score = !empty($cleaned_no_score) ? $DB->escape($cleaned_no_score) : ''; // 去除分数标记后的清理版本（SQL注入防护）
			
			// 1. 先尝试完全匹配原始问题
			$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			error_log("Debug - SQL精确查询(原始): " . $sql);
			$q = $DB->get_row($sql);
			
			// 2. 如果未匹配，尝试完全匹配去除分数标记的问题
			if (!$q && $no_score_question != $original_question) {
				$sql = "SELECT * FROM question WHERE question = '{$no_score_question}' LIMIT 1";
				error_log("Debug - SQL精确查询(去除分数标记): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 3. 如果未匹配，尝试完全匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - SQL精确查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 4. 如果未匹配，尝试完全匹配去除分数标记后的清理问题
			if (!$q && !empty($cleaned_no_score)) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_no_score}' LIMIT 1";
				error_log("Debug - SQL精确查询(去除分数标记后清理): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 5. 如果未匹配，尝试逗号转填空符号版本
			if (!$q && $comma_to_blank != $original_question) {
				$sql = "SELECT * FROM question WHERE question = '{$comma_to_blank}' LIMIT 1";
				error_log("Debug - SQL精确查询(逗号转填空符号): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 6. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			if (!$q) {
				$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
				'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ',', ''), '，', ''), '?', ''), '？', ''), '.', ''), '。', '') = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - SQL特殊字符对比查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 7. 如果仍未匹配，尝试模糊匹配原始问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(原始): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 8. 如果未匹配，尝试模糊匹配去除分数标记的问题
			if (!$q && $no_score_question != $original_question) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$no_score_question}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(去除分数标记): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 9. 如果未匹配，尝试模糊匹配逗号转填空符号版本
			if (!$q && $comma_to_blank != $original_question) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$comma_to_blank}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(逗号转填空符号): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 10. 如果仍未匹配，尝试模糊匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 11. 如果未匹配，尝试模糊匹配去除分数标记后的清理问题
			if (!$q && !empty($cleaned_no_score)) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_no_score}%' LIMIT 1";
				error_log("Debug - SQL模糊查询(去除分数标记后清理): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 12. 如果仍未匹配，尝试核心问题的模糊匹配
			if (!$q && mb_strlen($core_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$core_question}%' LIMIT 1";
				error_log("Debug - SQL核心问题模糊查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 13. 如果仍未匹配，尝试将清理后问题作为题库的子串或反向查询
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE 
						question LIKE '%{$cleaned_question}%' OR 
						'{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
						'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ' ', ''), '　', ''), '%') 
						LIMIT 1";
				error_log("Debug - SQL双向子串查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 14. 尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				// 将问题拆分为关键词
				$keywords = preg_split('/\s+/', $cleaned_question);
				$long_keywords = array();
				
				// 筛选长度大于3的关键词
				foreach ($keywords as $keyword) {
					if (mb_strlen($keyword, 'UTF-8') > 3) {
						$long_keywords[] = $DB->escape($keyword);
					}
				}
				
				// 如果没有大于3的关键词，就尝试使用所有的关键词
				if (empty($long_keywords) && !empty($keywords)) {
					foreach ($keywords as $keyword) {
						if (mb_strlen($keyword, 'UTF-8') > 1) {
							$long_keywords[] = $DB->escape($keyword);
						}
					}
				}
				
				// 如果有足够的关键词，使用它们进行查询
				if (!empty($long_keywords)) {
					$keyword_conditions = array();
					foreach ($long_keywords as $keyword) {
						$keyword_conditions[] = "question LIKE '%{$keyword}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
					error_log("Debug - SQL关键词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 15. 如果仍未匹配，尝试分词匹配（将问题分成几部分，只要匹配其中一部分）
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 10) {
				$parts = array();
				$len = mb_strlen($cleaned_question, 'UTF-8');
				
				// 将问题分成更多部分进行匹配，提高匹配成功率
				$part1 = mb_substr($cleaned_question, 0, ceil($len/4), 'UTF-8');
				$part2 = mb_substr($cleaned_question, ceil($len/4), ceil($len/4), 'UTF-8');
				$part3 = mb_substr($cleaned_question, 2*ceil($len/4), ceil($len/4), 'UTF-8');
				$part4 = mb_substr($cleaned_question, 3*ceil($len/4), $len-3*ceil($len/4), 'UTF-8');
				
				// 降低最小匹配长度要求为5个字符
				$min_match_length = 5;
				
				if (mb_strlen($part1, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part1);
				if (mb_strlen($part2, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part2);
				if (mb_strlen($part3, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part3);
				if (mb_strlen($part4, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part4);
				
				// 如果分词后没有足够长的部分，尝试提取问题中的关键短语
				if (empty($parts)) {
					// 提取连续的5个或更多字符作为匹配关键词
					for ($i = 0; $i <= mb_strlen($cleaned_question, 'UTF-8') - $min_match_length; $i++) {
						$substr = mb_substr($cleaned_question, $i, $min_match_length, 'UTF-8');
						if (!in_array($substr, $parts)) {
							$parts[] = $DB->escape($substr);
						}
						
						// 限制关键词数量，避免查询过于复杂
						if (count($parts) >= 5) break;
					}
				}
				
				if (!empty($parts)) {
					$part_conditions = array();
					foreach ($parts as $part) {
						$part_conditions[] = "question LIKE '%{$part}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $part_conditions) . " LIMIT 1";
					error_log("Debug - SQL分词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 16. 使用连续5个字符的滑动窗口进行匹配 - 优化算法，先从原始问题中提取，再从去除分数标记的问题中提取
			if (!$q) {
				$window_size = 5; // 滑动窗口大小，即最小匹配字符数
				$windows = array();
				
				// 从原始问题中提取滑动窗口
				$cleaned_for_window = preg_replace('/\s+/', '', $original_question_raw); // 移除所有空格
				if (mb_strlen($cleaned_for_window, 'UTF-8') >= $window_size) {
					for ($i = 0; $i <= mb_strlen($cleaned_for_window, 'UTF-8') - $window_size; $i++) {
						$window = mb_substr($cleaned_for_window, $i, $window_size, 'UTF-8');
						$windows[] = $DB->escape($window);
						
						// 限制窗口数量，避免查询过于复杂
						if (count($windows) >= 10) break;
					}
				}
				
				// 如果原始问题没有足够的窗口，从去除分数标记的问题中提取
				if (count($windows) < 5 && $no_score_question != $original_question_raw) {
					$cleaned_no_score_for_window = preg_replace('/\s+/', '', $no_score_question); // 移除所有空格
					if (mb_strlen($cleaned_no_score_for_window, 'UTF-8') >= $window_size) {
						for ($i = 0; $i <= mb_strlen($cleaned_no_score_for_window, 'UTF-8') - $window_size; $i++) {
							$window = mb_substr($cleaned_no_score_for_window, $i, $window_size, 'UTF-8');
							if (!in_array($window, $windows)) {
								$windows[] = $DB->escape($window);
							}
							
							// 限制窗口数量，避免查询过于复杂
							if (count($windows) >= 15) break;
						}
					}
				}
				
				if (!empty($windows)) {
					$window_conditions = array();
					foreach ($windows as $window) {
						$window_conditions[] = "question LIKE '%{$window}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $window_conditions) . " LIMIT 1";
					error_log("Debug - MCX SQL滑动窗口查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 17. 尝试去除填空符号后进行匹配
			if (!$q) {
				// 替换常见的填空符号模式
				$no_blank_question = preg_replace('/[（\(][\s_]{0,10}[）\)]/', '', $original_question_raw);
				$no_blank_question = $DB->escape($no_blank_question);
				
				if ($no_blank_question != $original_question) {
					$sql = "SELECT * FROM question WHERE question LIKE '%{$no_blank_question}%' LIMIT 1";
					error_log("Debug - SQL去除填空符号查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 18. 处理特殊情况 - 检测"将，"模式并尝试替换为"将（ ）,"
			if (!$q) {
				// 检查是否存在"将,"或"将，"模式
				if (strpos($original_question_raw, '将，') !== false || strpos($original_question_raw, '将,') !== false) {
					// 创建一个新版本，将"将，"替换为"将（ ）,"
					$special_case = str_replace(['将，', '将,'], ['将（ ），', '将（ ）,'], $original_question_raw);
					$special_case = $DB->escape($special_case);
					
					error_log("Debug - 特殊情况处理: " . $special_case);
					
					// 尝试精确匹配
					$sql = "SELECT * FROM question WHERE question = '{$special_case}' LIMIT 1";
					error_log("Debug - SQL特殊情况精确查询: " . $sql);
					$q = $DB->get_row($sql);
					
					// 如果精确匹配失败，尝试模糊匹配
					if (!$q) {
						$sql = "SELECT * FROM question WHERE question LIKE '%{$special_case}%' LIMIT 1";
						error_log("Debug - SQL特殊情况模糊查询: " . $sql);
						$q = $DB->get_row($sql);
					}
				}
			}
			
			// 19. 直接在题库中搜索包含原始问题关键部分的题目
			if (!$q) {
				// 提取问题的前半部分和后半部分
				$front_part = mb_substr($original_question_raw, 0, floor(mb_strlen($original_question_raw, 'UTF-8') / 3), 'UTF-8');
				$back_part = mb_substr($original_question_raw, -floor(mb_strlen($original_question_raw, 'UTF-8') / 3), NULL, 'UTF-8');
				
				$front_part = $DB->escape($front_part);
				$back_part = $DB->escape($back_part);
				
				error_log("Debug - 前半部分: " . $front_part);
				error_log("Debug - 后半部分: " . $back_part);
				
				// 使用问题的前半部分和后半部分进行查询
				$sql = "SELECT * FROM question WHERE question LIKE '%{$front_part}%' AND question LIKE '%{$back_part}%' LIMIT 1";
				error_log("Debug - SQL前后部分查询: " . $sql);
				$q = $DB->get_row($sql);
				
				// 如果未匹配，尝试使用去除分数标记问题的前后部分
				if (!$q && $no_score_question != $original_question_raw) {
					$front_part_no_score = mb_substr($no_score_question, 0, floor(mb_strlen($no_score_question, 'UTF-8') / 3), 'UTF-8');
					$back_part_no_score = mb_substr($no_score_question, -floor(mb_strlen($no_score_question, 'UTF-8') / 3), NULL, 'UTF-8');
					
					$front_part_no_score = $DB->escape($front_part_no_score);
					$back_part_no_score = $DB->escape($back_part_no_score);
					
					error_log("Debug - 去除分数标记前半部分: " . $front_part_no_score);
					error_log("Debug - 去除分数标记后半部分: " . $back_part_no_score);
					
					$sql = "SELECT * FROM question WHERE question LIKE '%{$front_part_no_score}%' AND question LIKE '%{$back_part_no_score}%' LIMIT 1";
					error_log("Debug - SQL去除分数标记前后部分查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			error_log("Debug - 查询结果: " . print_r($q, true));
			
			// 计算相似度并记录搜题历史
			$time = date('Y-m-d H:i:s');
			if ($q) {
				// 计算相似度
				$accuracy = 0;
				similar_text($question, $q['question'], $accuracy);
				$accuracy = round($accuracy, 2);
				
				// 查找最长公共子串，用于记录匹配质量
				$max_common_length = 0;
				$common_substring = '';
				
				// 查找最长公共子串
				for ($i = 0; $i < mb_strlen($question, 'UTF-8'); $i++) {
					for ($j = 0; $j < mb_strlen($q['question'], 'UTF-8'); $j++) {
						$k = 0;
						while (
							($i + $k) < mb_strlen($question, 'UTF-8') && 
							($j + $k) < mb_strlen($q['question'], 'UTF-8') && 
							mb_substr($question, $i + $k, 1, 'UTF-8') === mb_substr($q['question'], $j + $k, 1, 'UTF-8')
						) {
							$k++;
						}
						
						if ($k > $max_common_length) {
							$max_common_length = $k;
							$common_substring = mb_substr($question, $i, $k, 'UTF-8');
						}
					}
				}
				
				error_log("Debug - 相似度: " . $accuracy . "%, 最长公共子串长度: " . $max_common_length . ", 内容: " . $common_substring);
				
				// 检查答案是否为占位符（如"____"或空值）
				if ($q['answer'] == "____" || trim($q['answer']) == "" || $q['answer'] == "0") {
					// 记录搜题历史（找到题目但答案无效）
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '答案无效', '0', '{$time}')");
					
					// 返回未找到答案的结果
					$result = array(
						"code" => 0,
						"data" => array(
							"question" => "未找到有效答案！",
							"answer" => "很抱歉, 题目找到但答案无效。",
							"times" => 999
						),
						"message" => "验证成功，但是没有找到有效答案"
					);
					// 记录未找到有效答案的日志
					wlog($uid, "蜜雪程序查询", "查询题目：{$question} - 找到题目但答案无效", 0);
				} else {
				// 使用已经计算好的相似度
				
				// 记录搜题历史（找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
				
				// 检查用户积分是否足够
				if($row['money'] < 1) {
					exit('{"code":-3,"msg":"积分不足，无法进行搜索，请充值"}');
				}
				
				// 扣除用户积分（只在查询成功时扣除）
				$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
				// 记录积分扣除日志
				wlog($uid, "积分消费", "API题库搜索消费1积分", -1);
				
				$result = array(
					"code" => 1,
					"data" => array(
						"question" => $q['question'],
						"answer" => $q['answer'],
						"times" => 999
					),
					"message" => "请求成功"
				);
				// 记录操作日志
					wlog($uid, "蜜雪程序查询", "查询题目：{$question} -答案：{$q['answer']} 查询成功", 0);
				}
					} else {
				// 记录搜题历史（未找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
				
				// 未找到答案
				$result = array(
					"code" => 0,
					"data" => array(
						"question" => "未找到答案！",
						"answer" => "很抱歉, 题目搜索不到。",
						"times" => 999
					),
					"message" => "验证成功，但是没有找到答案"
				);
				// 记录未找到答案的日志
				wlog($uid, "蜜雪程序查询", "查询题目：{$question} - 未找到答案", 0);
			}
			
						exit(json_encode($result));
			break;
		case 'mcx': // 柠檬题库兼容接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取POST请求体中的JSON数据
			$json_data = file_get_contents('php://input');
			$post_data = json_decode($json_data, true);
			
			// 从POST JSON或GET参数中获取问题
			$question = '';
			$options = '';
			$type = '6'; // 默认类型
			$uid_param = ''; // 用户传入的uid参数（仅记录用）
			
			if ($post_data) {
				// 从JSON POST中获取数据
				$question = isset($post_data['question']) ? trim(strip_tags(daddslashes($post_data['question']))) : '';
				$options = isset($post_data['options']) ? trim(strip_tags(daddslashes($post_data['options']))) : '';
				$type = isset($post_data['type']) ? trim(strip_tags(daddslashes($post_data['type']))) : '6';
				$uid_param = isset($post_data['uid']) ? trim(strip_tags(daddslashes($post_data['uid']))) : '';
					} else {
				// 从常规POST或GET中获取数据
				$question = isset($_POST['question']) ? trim(strip_tags(daddslashes($_POST['question']))) : (isset($_GET['question']) ? trim(strip_tags(daddslashes($_GET['question']))) : '');
				$options = isset($_POST['options']) ? trim(strip_tags(daddslashes($_POST['options']))) : (isset($_GET['options']) ? trim(strip_tags(daddslashes($_GET['options']))) : '');
				$type = isset($_POST['type']) ? trim(strip_tags(daddslashes($_POST['type']))) : (isset($_GET['type']) ? trim(strip_tags(daddslashes($_GET['type']))) : '6');
				$uid_param = isset($_POST['uid']) ? trim(strip_tags(daddslashes($_POST['uid']))) : (isset($_GET['uid']) ? trim(strip_tags(daddslashes($_GET['uid']))) : '');
			}
			
			// 调试日志
			error_log("Debug - MCX接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("question: " . $question);
			error_log("options: " . $options);
			error_log("type: " . $type);
			error_log("uid_param: " . $uid_param);
			
			// 验证参数
			if ($key == '' || $question == '') {
				error_log("Debug - MCX参数验证失败");
				exit(json_encode(array("code" => 1001, "msg" => "参数不完整", "data" => array("answer" => ""))));
			}
			
			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				exit(json_encode(array("code" => 1001, "msg" => "密钥错误", "data" => array("answer" => ""))));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 保存原始问题用于后续处理
			$original_question_raw = $question;
			
			// 预处理：去除分数标记，如"(5.0)"、"（5.0）"等
			$no_score_question = preg_replace('/\s*[\(（][\s]*\d+(\.\d+)?[\s]*[\)）]\s*$/', '', $original_question_raw);
			error_log("Debug - MCX 去除分数标记后的问题: " . $no_score_question);
			
			// 清理问题文本，移除特殊字符以提高匹配率
			$cleaned_question = $question;
			$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			$cleaned_question = trim($cleaned_question); // 移除首尾空格
			
			// 移除题型前缀，如"(单选题)"、"（多选题）"等
			$cleaned_question = preg_replace('/^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：]*[\)）\]\】]?\s*/u', '', $cleaned_question);
			
			// 移除各种括号和标点符号，包括填空符号"（ ）"
			$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！', '（ ）', '( )'), '', $cleaned_question);
			
			// 提取核心问题（去掉可能的选项部分）
			$core_question = $cleaned_question;
			if (mb_strlen($core_question, 'UTF-8') > 15) {
				// 如果问题较长，尝试提取前半部分作为核心问题
				$core_question = mb_substr($core_question, 0, min(mb_strlen($core_question, 'UTF-8'), 30), 'UTF-8');
			}
			
			// 创建一个版本的问题，其中逗号替换为填空符号，用于处理特殊情况
			$comma_to_blank = preg_replace('/，将，/u', '，将（ ），', $original_question_raw);
			$comma_to_blank = preg_replace('/,将,/u', ',将（ ）,', $comma_to_blank);
			
			// 创建去除分数标记后的清理版本
			$cleaned_no_score = '';
			if ($no_score_question != $original_question_raw) {
				$cleaned_no_score = preg_replace('/\s+/', ' ', $no_score_question); // 合并多个空格为一个
				$cleaned_no_score = trim($cleaned_no_score); // 移除首尾空格
				$cleaned_no_score = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！', '（ ）', '( )'), '', $cleaned_no_score);
				error_log("Debug - MCX 去除分数标记后的清理问题: " . $cleaned_no_score);
			}
			
			error_log("Debug - MCX 清理后的问题: " . $cleaned_question);
			error_log("Debug - MCX 核心问题: " . $core_question);
			error_log("Debug - MCX 逗号转填空符号: " . $comma_to_blank);
			
			// 查询题库 - 使用多种查询方式
			$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			$core_question = $DB->escape($core_question); // 核心问题（SQL注入防护）
			$comma_to_blank = $DB->escape($comma_to_blank); // 逗号转填空符号版本（SQL注入防护）
			$no_score_question = $DB->escape($no_score_question); // 去除分数标记版本（SQL注入防护）
			$cleaned_no_score = !empty($cleaned_no_score) ? $DB->escape($cleaned_no_score) : ''; // 去除分数标记后的清理版本（SQL注入防护）
			
			// 1. 先尝试完全匹配原始问题
			$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			error_log("Debug - MCX SQL精确查询(原始): " . $sql);
			$q = $DB->get_row($sql);
			
			// 2. 如果未匹配，尝试完全匹配去除分数标记的问题
			if (!$q && $no_score_question != $original_question) {
				$sql = "SELECT * FROM question WHERE question = '{$no_score_question}' LIMIT 1";
				error_log("Debug - MCX SQL精确查询(去除分数标记): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 3. 如果未匹配，尝试完全匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - MCX SQL精确查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 4. 如果未匹配，尝试完全匹配去除分数标记后的清理问题
			if (!$q && !empty($cleaned_no_score)) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_no_score}' LIMIT 1";
				error_log("Debug - MCX SQL精确查询(去除分数标记后清理): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 5. 如果未匹配，尝试逗号转填空符号版本
			if (!$q && $comma_to_blank != $original_question) {
				$sql = "SELECT * FROM question WHERE question = '{$comma_to_blank}' LIMIT 1";
				error_log("Debug - MCX SQL精确查询(逗号转填空符号): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 6. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			if (!$q) {
				$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
				'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ',', ''), '，', ''), '?', ''), '？', ''), '.', ''), '。', '') = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - MCX SQL特殊字符对比查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 7. 如果仍未匹配，尝试模糊匹配原始问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(原始): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 8. 如果未匹配，尝试模糊匹配去除分数标记的问题
			if (!$q && $no_score_question != $original_question) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$no_score_question}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(去除分数标记): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 9. 如果未匹配，尝试模糊匹配逗号转填空符号版本
			if (!$q && $comma_to_blank != $original_question) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$comma_to_blank}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(逗号转填空符号): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 10. 如果仍未匹配，尝试模糊匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 11. 如果未匹配，尝试模糊匹配去除分数标记后的清理问题
			if (!$q && !empty($cleaned_no_score)) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_no_score}%' LIMIT 1";
				error_log("Debug - MCX SQL模糊查询(去除分数标记后清理): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 12. 如果仍未匹配，尝试核心问题的模糊匹配
			if (!$q && mb_strlen($core_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$core_question}%' LIMIT 1";
				error_log("Debug - MCX SQL核心问题模糊查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 13. 如果仍未匹配，尝试将清理后问题作为题库的子串或反向查询
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE 
				        question LIKE '%{$cleaned_question}%' OR 
				        '{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
				        '(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ' ', ''), '　', ''), '%') 
				        LIMIT 1";
				error_log("Debug - MCX SQL双向子串查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 14. 尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				// 将问题拆分为关键词
				$keywords = preg_split('/\s+/', $cleaned_question);
				$long_keywords = array();
				
				// 筛选长度大于3的关键词
				foreach ($keywords as $keyword) {
					if (mb_strlen($keyword, 'UTF-8') > 3) {
						$long_keywords[] = $DB->escape($keyword);
					}
				}
				
				// 如果没有大于3的关键词，就尝试使用所有的关键词
				if (empty($long_keywords) && !empty($keywords)) {
					foreach ($keywords as $keyword) {
						if (mb_strlen($keyword, 'UTF-8') > 1) {
							$long_keywords[] = $DB->escape($keyword);
						}
					}
				}
				
				// 如果有足够的关键词，使用它们进行查询
				if (!empty($long_keywords)) {
					$keyword_conditions = array();
					foreach ($long_keywords as $keyword) {
						$keyword_conditions[] = "question LIKE '%{$keyword}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
					error_log("Debug - MCX SQL关键词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 15. 如果仍未匹配，尝试分词匹配（将问题分成几部分，只要匹配其中一部分）
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 10) {
				$parts = array();
				$len = mb_strlen($cleaned_question, 'UTF-8');
				
				// 将问题分成更多部分进行匹配，提高匹配成功率
				$part1 = mb_substr($cleaned_question, 0, ceil($len/4), 'UTF-8');
				$part2 = mb_substr($cleaned_question, ceil($len/4), ceil($len/4), 'UTF-8');
				$part3 = mb_substr($cleaned_question, 2*ceil($len/4), ceil($len/4), 'UTF-8');
				$part4 = mb_substr($cleaned_question, 3*ceil($len/4), $len-3*ceil($len/4), 'UTF-8');
				
				// 降低最小匹配长度要求为5个字符
				$min_match_length = 5;
				
				if (mb_strlen($part1, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part1);
				if (mb_strlen($part2, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part2);
				if (mb_strlen($part3, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part3);
				if (mb_strlen($part4, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part4);
				
				// 如果分词后没有足够长的部分，尝试提取问题中的关键短语
				if (empty($parts)) {
					// 提取连续的5个或更多字符作为匹配关键词
					for ($i = 0; $i <= mb_strlen($cleaned_question, 'UTF-8') - $min_match_length; $i++) {
						$substr = mb_substr($cleaned_question, $i, $min_match_length, 'UTF-8');
						if (!in_array($substr, $parts)) {
							$parts[] = $DB->escape($substr);
						}
						
						// 限制关键词数量，避免查询过于复杂
						if (count($parts) >= 5) break;
					}
				}
				
				if (!empty($parts)) {
					$part_conditions = array();
					foreach ($parts as $part) {
						$part_conditions[] = "question LIKE '%{$part}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $part_conditions) . " LIMIT 1";
					error_log("Debug - MCX SQL分词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 16. 使用连续5个字符的滑动窗口进行匹配 - 优化算法，先从原始问题中提取，再从去除分数标记的问题中提取
			if (!$q) {
				$window_size = 5; // 滑动窗口大小，即最小匹配字符数
				$windows = array();
				
				// 从原始问题中提取滑动窗口
				$cleaned_for_window = preg_replace('/\s+/', '', $original_question_raw); // 移除所有空格
				if (mb_strlen($cleaned_for_window, 'UTF-8') >= $window_size) {
					for ($i = 0; $i <= mb_strlen($cleaned_for_window, 'UTF-8') - $window_size; $i++) {
						$window = mb_substr($cleaned_for_window, $i, $window_size, 'UTF-8');
						$windows[] = $DB->escape($window);
						
						// 限制窗口数量，避免查询过于复杂
						if (count($windows) >= 10) break;
					}
				}
				
				// 如果原始问题没有足够的窗口，从去除分数标记的问题中提取
				if (count($windows) < 5 && $no_score_question != $original_question_raw) {
					$cleaned_no_score_for_window = preg_replace('/\s+/', '', $no_score_question); // 移除所有空格
					if (mb_strlen($cleaned_no_score_for_window, 'UTF-8') >= $window_size) {
						for ($i = 0; $i <= mb_strlen($cleaned_no_score_for_window, 'UTF-8') - $window_size; $i++) {
							$window = mb_substr($cleaned_no_score_for_window, $i, $window_size, 'UTF-8');
							if (!in_array($window, $windows)) {
								$windows[] = $DB->escape($window);
							}
							
							// 限制窗口数量，避免查询过于复杂
							if (count($windows) >= 15) break;
						}
					}
				}
				
				if (!empty($windows)) {
					$window_conditions = array();
					foreach ($windows as $window) {
						$window_conditions[] = "question LIKE '%{$window}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $window_conditions) . " LIMIT 1";
					error_log("Debug - MCX SQL滑动窗口查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 17. 尝试去除填空符号后进行匹配
			if (!$q) {
				// 替换常见的填空符号模式
				$no_blank_question = preg_replace('/[（\(][\s_]{0,10}[）\)]/', '', $original_question_raw);
				$no_blank_question = $DB->escape($no_blank_question);
				
				if ($no_blank_question != $original_question) {
					$sql = "SELECT * FROM question WHERE question LIKE '%{$no_blank_question}%' LIMIT 1";
					error_log("Debug - MCX SQL去除填空符号查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 18. 处理特殊情况 - 检测"将，"模式并尝试替换为"将（ ）,"
			if (!$q) {
				// 检查是否存在"将,"或"将，"模式
				if (strpos($original_question_raw, '将，') !== false || strpos($original_question_raw, '将,') !== false) {
					// 创建一个新版本，将"将，"替换为"将（ ）,"
					$special_case = str_replace(['将，', '将,'], ['将（ ），', '将（ ）,'], $original_question_raw);
					$special_case = $DB->escape($special_case);
					
					error_log("Debug - MCX 特殊情况处理: " . $special_case);
					
					// 尝试精确匹配
					$sql = "SELECT * FROM question WHERE question = '{$special_case}' LIMIT 1";
					error_log("Debug - MCX SQL特殊情况精确查询: " . $sql);
					$q = $DB->get_row($sql);
					
					// 如果精确匹配失败，尝试模糊匹配
					if (!$q) {
						$sql = "SELECT * FROM question WHERE question LIKE '%{$special_case}%' LIMIT 1";
						error_log("Debug - MCX SQL特殊情况模糊查询: " . $sql);
						$q = $DB->get_row($sql);
					}
				}
			}
			
			// 19. 直接在题库中搜索包含原始问题关键部分的题目
			if (!$q) {
				// 提取问题的前半部分和后半部分
				$front_part = mb_substr($original_question_raw, 0, floor(mb_strlen($original_question_raw, 'UTF-8') / 3), 'UTF-8');
				$back_part = mb_substr($original_question_raw, -floor(mb_strlen($original_question_raw, 'UTF-8') / 3), NULL, 'UTF-8');
				
				$front_part = $DB->escape($front_part);
				$back_part = $DB->escape($back_part);
				
				error_log("Debug - MCX 前半部分: " . $front_part);
				error_log("Debug - MCX 后半部分: " . $back_part);
				
				// 使用问题的前半部分和后半部分进行查询
				$sql = "SELECT * FROM question WHERE question LIKE '%{$front_part}%' AND question LIKE '%{$back_part}%' LIMIT 1";
				error_log("Debug - MCX SQL前后部分查询: " . $sql);
				$q = $DB->get_row($sql);
				
				// 如果未匹配，尝试使用去除分数标记问题的前后部分
				if (!$q && $no_score_question != $original_question_raw) {
					$front_part_no_score = mb_substr($no_score_question, 0, floor(mb_strlen($no_score_question, 'UTF-8') / 3), 'UTF-8');
					$back_part_no_score = mb_substr($no_score_question, -floor(mb_strlen($no_score_question, 'UTF-8') / 3), NULL, 'UTF-8');
					
					$front_part_no_score = $DB->escape($front_part_no_score);
					$back_part_no_score = $DB->escape($back_part_no_score);
					
					error_log("Debug - MCX 去除分数标记前半部分: " . $front_part_no_score);
					error_log("Debug - MCX 去除分数标记后半部分: " . $back_part_no_score);
					
					$sql = "SELECT * FROM question WHERE question LIKE '%{$front_part_no_score}%' AND question LIKE '%{$back_part_no_score}%' LIMIT 1";
					error_log("Debug - MCX SQL去除分数标记前后部分查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			error_log("Debug - MCX查询结果: " . print_r($q, true));
			
			// 计算相似度并记录搜题历史
			$time = date('Y-m-d H:i:s');
			if ($q) {
				// 计算相似度
				$accuracy = 0;
				similar_text($question, $q['question'], $accuracy);
				$accuracy = round($accuracy, 2);
				
				// 查找最长公共子串，用于记录匹配质量
				$max_common_length = 0;
				$common_substring = '';
				
				// 查找最长公共子串
				for ($i = 0; $i < mb_strlen($question, 'UTF-8'); $i++) {
					for ($j = 0; $j < mb_strlen($q['question'], 'UTF-8'); $j++) {
						$k = 0;
						while (
							($i + $k) < mb_strlen($question, 'UTF-8') && 
							($j + $k) < mb_strlen($q['question'], 'UTF-8') && 
							mb_substr($question, $i + $k, 1, 'UTF-8') === mb_substr($q['question'], $j + $k, 1, 'UTF-8')
						) {
							$k++;
						}
						
						if ($k > $max_common_length) {
							$max_common_length = $k;
							$common_substring = mb_substr($question, $i, $k, 'UTF-8');
						}
					}
				}
				
				error_log("Debug - MCX 相似度: " . $accuracy . "%, 最长公共子串长度: " . $max_common_length . ", 内容: " . $common_substring);
				
				// 检查答案是否为占位符（如"____"或空值）
				if ($q['answer'] == "____" || trim($q['answer']) == "" || $q['answer'] == "0") {
					// 记录搜题历史（找到题目但答案无效）
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '答案无效', '0', '{$time}')");
					
					// 返回未找到答案的结果
					$result = array(
						"code" => 1001,
						"msg" => "题目找到但答案无效",
						"data" => array(
							"answer" => ""
						)
					);
					// 记录未找到有效答案的日志
					wlog($uid, "OCS查询", "查询题目：{$question} - 找到题目但答案无效", 0);
				} else {
				// 记录搜题历史（找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
				
				// 检查用户积分是否足够
				if($row['money'] < 1) {
					exit(json_encode(array("code" => 1001, "msg" => "积分不足，无法进行搜索，请充值", "data" => array("answer" => ""))));
				}
				
				// 扣除用户积分（只在查询成功时扣除）
				$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
				// 记录积分扣除日志
				wlog($uid, "积分消费", "OCS搜索消费1积分", -1);
				
				// 按照柠檬题库的格式返回结果
				$result = array(
					"code" => 1000,
					"msg" => "success",
					"data" => array(
						"answer" => $q['answer']
					)
				);
				// 记录操作日志
				wlog($uid, "OCS查询", "查询题目：{$question} - 查询成功", 0);
				}
				} else {
				// 记录搜题历史（未找到答案）
				$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
						   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
				
				// 未找到答案，按照柠檬题库的格式返回结果
				$result = array(
					"code" => 1001,
					"msg" => "未找到答案",
					"data" => array(
						"answer" => ""
					)
				);
				// 记录未找到答案的日志
				wlog($uid, "OCS查询", "查询题目：{$question} - 未找到答案", 0);
			}
			
			exit(json_encode($result));
			break;
		case 'xxt': // 油猴脚本专用接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取问题和题目类型
			$question = isset($_POST['question']) ? trim(strip_tags(daddslashes($_POST['question']))) : (isset($_GET['question']) ? trim(strip_tags(daddslashes($_GET['question']))) : '');
			$type = isset($_POST['type']) ? trim(strip_tags(daddslashes($_POST['type']))) : (isset($_GET['type']) ? trim(strip_tags(daddslashes($_GET['type']))) : '0');
			$options = isset($_POST['options']) ? trim(strip_tags(daddslashes($_POST['options']))) : (isset($_GET['options']) ? trim(strip_tags(daddslashes($_GET['options']))) : '');
			
			// 调试日志
			error_log("Debug - XXT接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("question: " . $question);
			error_log("type: " . $type);
			error_log("options: " . $options);
			
			// 验证参数
			if ($question == '') {
				error_log("Debug - XXT参数验证失败");
				exit(json_encode(array("code" => 0, "msg" => "题目不能为空", "data" => array("answer" => ""))));
			}
			
			// 用户ID，如果有key则验证，没有则使用默认值
			$uid = 0;
			$need_pay = false;
			
			// 如果提供了key，则验证key
			if (!empty($key)) {
				$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
				if ($row) {
					$uid = $row['uid'];
					// 检查用户积分是否足够
					if($row['money'] < 1) {
						exit(json_encode(array("code" => 0, "msg" => "积分不足，无法进行搜索，请充值", "data" => array("answer" => ""))));
					}
					$need_pay = true;
				} else {
					// key错误，返回错误信息并停止查询
					error_log("Debug - XXT key验证失败");
					exit(json_encode(array("code" => 0, "msg" => "Key验证失败，请检查您的Key是否正确", "data" => array("answer" => ""))));
				}
			} else {
				// 未提供key，返回错误信息
				error_log("Debug - XXT 未提供key");
				exit(json_encode(array("code" => 0, "msg" => "请提供有效的Key", "data" => array("answer" => ""))));
			}
			
			// 保存原始问题用于后续处理
			$original_question_raw = $question;
			
			// 预处理：去除分数标记，如"(5.0)"、"（5.0）"等
			$no_score_question = preg_replace('/\s*[\(（][\s]*\d+(\.\d+)?[\s]*[\)）]\s*$/', '', $original_question_raw);
			error_log("Debug - XXT 去除分数标记后的问题: " . $no_score_question);
			
			// 清理问题文本，移除特殊字符以提高匹配率
			$cleaned_question = $question;
			
			// 记录原始问题用于调试
			error_log("Debug - XXT 原始问题: " . $cleaned_question);
			
			// 移除题型前缀，如"(单选题)"、"（多选题）"等
			$cleaned_question = preg_replace('/^\s*[\(（\[]?\s*(?:单选题|多选题|判断题|填空题|简答题)[\s\:\：]?[\)）\]]?\s*/u', '', $cleaned_question);
			error_log("Debug - XXT 移除题型前缀后: " . $cleaned_question);
			
			$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			$cleaned_question = trim($cleaned_question); // 移除首尾空格
			$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！', '（ ）', '( )'), '', $cleaned_question); // 移除各种括号和标点符号，包括填空符号
			
			// 如果问题过长，可能是包含了选项，尝试提取主要问题部分
			if (mb_strlen($cleaned_question, 'UTF-8') > 30) {
				// 尝试找到问题的结束标志（如问号）
				$pos = mb_strpos($cleaned_question, '?', 0, 'UTF-8');
				if ($pos !== false && $pos > 10) {
					$short_question = mb_substr($cleaned_question, 0, $pos + 1, 'UTF-8');
					error_log("Debug - XXT 提取主要问题: " . $short_question);
					// 如果提取的问题足够长，使用提取的问题
					if (mb_strlen($short_question, 'UTF-8') > 10) {
						$cleaned_question = $short_question;
					}
				}
				
				// 也尝试查找中文问号
				$pos = mb_strpos($cleaned_question, '？', 0, 'UTF-8');
				if ($pos !== false && $pos > 10) {
					$short_question = mb_substr($cleaned_question, 0, $pos + 1, 'UTF-8');
					error_log("Debug - XXT 提取主要问题(中文问号): " . $short_question);
					// 如果提取的问题足够长，使用提取的问题
					if (mb_strlen($short_question, 'UTF-8') > 10) {
						$cleaned_question = $short_question;
					}
				}
			}
			
			// 创建一个版本的问题，其中逗号替换为填空符号，用于处理特殊情况
			$comma_to_blank = preg_replace('/，将，/u', '，将（ ），', $original_question_raw);
			$comma_to_blank = preg_replace('/,将,/u', ',将（ ）,', $comma_to_blank);
			
			// 创建去除分数标记后的清理版本
			$cleaned_no_score = '';
			if ($no_score_question != $original_question_raw) {
				$cleaned_no_score = preg_replace('/\s+/', ' ', $no_score_question); // 合并多个空格为一个
				$cleaned_no_score = trim($cleaned_no_score); // 移除首尾空格
				$cleaned_no_score = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！', '（ ）', '( )'), '', $cleaned_no_score);
				error_log("Debug - XXT 去除分数标记后的清理问题: " . $cleaned_no_score);
			}
			
			error_log("Debug - XXT 清理后的问题: " . $cleaned_question);
			error_log("Debug - XXT 逗号转填空符号: " . $comma_to_blank);
			
			// 提取核心问题（如果问题较长，提取一部分作为核心问题）
			$core_question = $cleaned_question;
			if (mb_strlen($core_question, 'UTF-8') > 15) {
				$core_question = mb_substr($core_question, 0, min(mb_strlen($core_question, 'UTF-8'), 30), 'UTF-8');
				$core_question = $DB->escape($core_question);
				error_log("Debug - XXT 核心问题: " . $core_question);
			}
			
			// 查询题库 - 使用多种查询方式
			$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			$core_question = $DB->escape($core_question); // 核心问题（SQL注入防护）
			$comma_to_blank = $DB->escape($comma_to_blank); // 逗号转填空符号版本（SQL注入防护）
			$no_score_question = $DB->escape($no_score_question); // 去除分数标记版本（SQL注入防护）
			$cleaned_no_score = !empty($cleaned_no_score) ? $DB->escape($cleaned_no_score) : ''; // 去除分数标记后的清理版本（SQL注入防护）
			
			// 1. 先尝试完全匹配原始问题
			$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			error_log("Debug - XXT SQL精确查询(原始): " . $sql);
			$q = $DB->get_row($sql);
			
			// 2. 如果未匹配，尝试完全匹配去除分数标记的问题
			if (!$q && $no_score_question != $original_question) {
				$sql = "SELECT * FROM question WHERE question = '{$no_score_question}' LIMIT 1";
				error_log("Debug - XXT SQL精确查询(去除分数标记): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 3. 如果未匹配，尝试完全匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - XXT SQL精确查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 4. 如果未匹配，尝试完全匹配去除分数标记后的清理问题
			if (!$q && !empty($cleaned_no_score)) {
				$sql = "SELECT * FROM question WHERE question = '{$cleaned_no_score}' LIMIT 1";
				error_log("Debug - XXT SQL精确查询(去除分数标记后清理): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 5. 如果未匹配，尝试逗号转填空符号版本
			if (!$q && $comma_to_blank != $original_question) {
				$sql = "SELECT * FROM question WHERE question = '{$comma_to_blank}' LIMIT 1";
				error_log("Debug - XXT SQL精确查询(逗号转填空符号): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 6. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			if (!$q) {
				$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
				'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ',', ''), '，', ''), '?', ''), '？', ''), '.', ''), '。', '') = '{$cleaned_question}' LIMIT 1";
				error_log("Debug - XXT SQL特殊字符对比查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 7. 如果仍未匹配，尝试模糊匹配原始问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(原始): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 8. 如果未匹配，尝试模糊匹配去除分数标记的问题
			if (!$q && $no_score_question != $original_question) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$no_score_question}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(去除分数标记): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 9. 如果未匹配，尝试模糊匹配逗号转填空符号版本
			if (!$q && $comma_to_blank != $original_question) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$comma_to_blank}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(逗号转填空符号): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 10. 如果仍未匹配，尝试模糊匹配清理后问题
			if (!$q) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(清理后): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 11. 如果未匹配，尝试模糊匹配去除分数标记后的清理问题
			if (!$q && !empty($cleaned_no_score)) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_no_score}%' LIMIT 1";
				error_log("Debug - XXT SQL模糊查询(去除分数标记后清理): " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 12. 如果仍未匹配，尝试核心问题的模糊匹配
			if (!$q && mb_strlen($core_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE question LIKE '%{$core_question}%' LIMIT 1";
				error_log("Debug - XXT SQL核心问题模糊查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 13. 如果仍未匹配，尝试双向子串查询
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				$sql = "SELECT * FROM question WHERE 
						question LIKE '%{$cleaned_question}%' OR 
						'{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
						'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ' ', ''), '　', ''), '%') 
						LIMIT 1";
				error_log("Debug - XXT SQL双向子串查询: " . $sql);
				$q = $DB->get_row($sql);
			}
			
			// 14. 尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
				// 将问题拆分为关键词
				$keywords = preg_split('/\s+/', $cleaned_question);
				$long_keywords = array();
				
				// 筛选长度大于3的关键词
				foreach ($keywords as $keyword) {
					if (mb_strlen($keyword, 'UTF-8') > 3) {
						$long_keywords[] = $DB->escape($keyword);
					}
				}
				
				// 如果没有大于3的关键词，就尝试使用所有的关键词
				if (empty($long_keywords) && !empty($keywords)) {
					foreach ($keywords as $keyword) {
						if (mb_strlen($keyword, 'UTF-8') > 1) {
							$long_keywords[] = $DB->escape($keyword);
						}
					}
				}
				
				// 如果有足够的关键词，使用它们进行查询
				if (!empty($long_keywords)) {
					$keyword_conditions = array();
					foreach ($long_keywords as $keyword) {
						$keyword_conditions[] = "question LIKE '%{$keyword}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
					error_log("Debug - XXT SQL关键词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 15. 如果仍未匹配，尝试分词匹配（将问题分成几部分，只要匹配其中一部分）
			if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 10) {
				$parts = array();
				$len = mb_strlen($cleaned_question, 'UTF-8');
				
				// 将问题分成更多部分进行匹配，提高匹配成功率
				$part1 = mb_substr($cleaned_question, 0, ceil($len/4), 'UTF-8');
				$part2 = mb_substr($cleaned_question, ceil($len/4), ceil($len/4), 'UTF-8');
				$part3 = mb_substr($cleaned_question, 2*ceil($len/4), ceil($len/4), 'UTF-8');
				$part4 = mb_substr($cleaned_question, 3*ceil($len/4), $len-3*ceil($len/4), 'UTF-8');
				
				// 降低最小匹配长度要求为5个字符
				$min_match_length = 5;
				
				if (mb_strlen($part1, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part1);
				if (mb_strlen($part2, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part2);
				if (mb_strlen($part3, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part3);
				if (mb_strlen($part4, 'UTF-8') >= $min_match_length) $parts[] = $DB->escape($part4);
				
				if (!empty($parts)) {
					$part_conditions = array();
					foreach ($parts as $part) {
						$part_conditions[] = "question LIKE '%{$part}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $part_conditions) . " LIMIT 1";
					error_log("Debug - XXT SQL分词查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 16. 使用连续5个字符的滑动窗口进行匹配 - 优化算法，先从原始问题中提取，再从去除分数标记的问题中提取
			if (!$q) {
				$window_size = 5; // 滑动窗口大小，即最小匹配字符数
				$windows = array();
				
				// 从原始问题中提取滑动窗口
				$cleaned_for_window = preg_replace('/\s+/', '', $original_question_raw); // 移除所有空格
				if (mb_strlen($cleaned_for_window, 'UTF-8') >= $window_size) {
					for ($i = 0; $i <= mb_strlen($cleaned_for_window, 'UTF-8') - $window_size; $i++) {
						$window = mb_substr($cleaned_for_window, $i, $window_size, 'UTF-8');
						$windows[] = $DB->escape($window);
						
						// 限制窗口数量，避免查询过于复杂
						if (count($windows) >= 10) break;
					}
				}
				
				// 如果原始问题没有足够的窗口，从去除分数标记的问题中提取
				if (count($windows) < 5 && $no_score_question != $original_question_raw) {
					$cleaned_no_score_for_window = preg_replace('/\s+/', '', $no_score_question); // 移除所有空格
					if (mb_strlen($cleaned_no_score_for_window, 'UTF-8') >= $window_size) {
						for ($i = 0; $i <= mb_strlen($cleaned_no_score_for_window, 'UTF-8') - $window_size; $i++) {
							$window = mb_substr($cleaned_no_score_for_window, $i, $window_size, 'UTF-8');
							if (!in_array($window, $windows)) {
								$windows[] = $DB->escape($window);
							}
							
							// 限制窗口数量，避免查询过于复杂
							if (count($windows) >= 15) break;
						}
					}
				}
				
				if (!empty($windows)) {
					$window_conditions = array();
					foreach ($windows as $window) {
						$window_conditions[] = "question LIKE '%{$window}%'";
					}
					
					$sql = "SELECT * FROM question WHERE " . implode(" OR ", $window_conditions) . " LIMIT 1";
					error_log("Debug - XXT SQL滑动窗口查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 17. 尝试去除填空符号后进行匹配
			if (!$q) {
				// 替换常见的填空符号模式
				$no_blank_question = preg_replace('/[（\(][\s_]{0,10}[）\)]/', '', $original_question_raw);
				$no_blank_question = $DB->escape($no_blank_question);
				
				if ($no_blank_question != $original_question) {
					$sql = "SELECT * FROM question WHERE question LIKE '%{$no_blank_question}%' LIMIT 1";
					error_log("Debug - XXT SQL去除填空符号查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			// 18. 处理特殊情况 - 检测"将，"模式并尝试替换为"将（ ）,"
			if (!$q) {
				// 检查是否存在"将,"或"将，"模式
				if (strpos($original_question_raw, '将，') !== false || strpos($original_question_raw, '将,') !== false) {
					// 创建一个新版本，将"将，"替换为"将（ ）,"
					$special_case = str_replace(['将，', '将,'], ['将（ ），', '将（ ）,'], $original_question_raw);
					$special_case = $DB->escape($special_case);
					
					error_log("Debug - XXT 特殊情况处理: " . $special_case);
					
					// 尝试精确匹配
					$sql = "SELECT * FROM question WHERE question = '{$special_case}' LIMIT 1";
					error_log("Debug - XXT SQL特殊情况精确查询: " . $sql);
					$q = $DB->get_row($sql);
					
					// 如果精确匹配失败，尝试模糊匹配
					if (!$q) {
						$sql = "SELECT * FROM question WHERE question LIKE '%{$special_case}%' LIMIT 1";
										error_log("Debug - XXT SQL特殊情况模糊查询: " . $sql);
				$q = $DB->get_row($sql);
			}
		}
	}
	
	// 19. 直接在题库中搜索包含原始问题关键部分的题目
	if (!$q) {
				// 提取问题的前半部分和后半部分
				$front_part = mb_substr($original_question_raw, 0, floor(mb_strlen($original_question_raw, 'UTF-8') / 3), 'UTF-8');
				$back_part = mb_substr($original_question_raw, -floor(mb_strlen($original_question_raw, 'UTF-8') / 3), NULL, 'UTF-8');
				
				$front_part = $DB->escape($front_part);
				$back_part = $DB->escape($back_part);
				
				error_log("Debug - XXT 前半部分: " . $front_part);
				error_log("Debug - XXT 后半部分: " . $back_part);
				
				// 使用问题的前半部分和后半部分进行查询
				$sql = "SELECT * FROM question WHERE question LIKE '%{$front_part}%' AND question LIKE '%{$back_part}%' LIMIT 1";
				error_log("Debug - XXT SQL前后部分查询: " . $sql);
				$q = $DB->get_row($sql);
				
				// 如果未匹配，尝试使用去除分数标记问题的前后部分
				if (!$q && $no_score_question != $original_question_raw) {
					$front_part_no_score = mb_substr($no_score_question, 0, floor(mb_strlen($no_score_question, 'UTF-8') / 3), 'UTF-8');
					$back_part_no_score = mb_substr($no_score_question, -floor(mb_strlen($no_score_question, 'UTF-8') / 3), NULL, 'UTF-8');
					
					$front_part_no_score = $DB->escape($front_part_no_score);
					$back_part_no_score = $DB->escape($back_part_no_score);
					
					error_log("Debug - XXT 去除分数标记前半部分: " . $front_part_no_score);
					error_log("Debug - XXT 去除分数标记后半部分: " . $back_part_no_score);
					
					$sql = "SELECT * FROM question WHERE question LIKE '%{$front_part_no_score}%' AND question LIKE '%{$back_part_no_score}%' LIMIT 1";
					error_log("Debug - XXT SQL去除分数标记前后部分查询: " . $sql);
					$q = $DB->get_row($sql);
				}
			}
			
			error_log("Debug - XXT查询结果: " . print_r($q, true));
			
			// 计算相似度并记录搜题历史
			$time = date('Y-m-d H:i:s');
			if ($q) {
				// 检查答案是否为占位符（如"____"或空值）
				if ($q['answer'] == "____" || trim($q['answer']) == "" || $q['answer'] == "0") {
					// 记录搜题历史（找到题目但答案无效）
					if ($uid > 0) {
						$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
								   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '答案无效', '0', '{$time}')");
						
						// 记录未找到有效答案的日志
						wlog($uid, "蜜雪脚本查询", "查询题目：{$question} - 找到题目但答案无效", 0);
					}
					
					// 返回未找到答案的结果
					$result = array(
						"code" => 0,
						"msg" => "未找到有效答案，题目找到但答案无效",
						"data" => array(
							"question" => "未找到有效答案！",
							"answer" => "很抱歉, 题目找到但答案无效。",
							"times" => 999
						)
					);
				} else {
				// 计算相似度
				$accuracy = 0;
				similar_text($question, $q['question'], $accuracy);
				$accuracy = round($accuracy, 2);
				
				// 放宽相似度要求，降低最小匹配长度
					$min_match_length = 5; // 根据用户要求，设置为5个字符相同就可以匹配
				$max_common_length = 0;
					$common_substring = "";
				
				// 查找最长公共子串（仅用于记录，不再作为拒绝的依据）
				for ($i = 0; $i < mb_strlen($question, 'UTF-8'); $i++) {
					for ($j = 0; $j < mb_strlen($q['question'], 'UTF-8'); $j++) {
						$k = 0;
						while (
							($i + $k) < mb_strlen($question, 'UTF-8') && 
							($j + $k) < mb_strlen($q['question'], 'UTF-8') && 
							mb_substr($question, $i + $k, 1, 'UTF-8') === mb_substr($q['question'], $j + $k, 1, 'UTF-8')
						) {
							$k++;
						}
						
						if ($k > $max_common_length) {
							$max_common_length = $k;
							$common_substring = mb_substr($question, $i, $k, 'UTF-8');
						}
					}
				}
				
					error_log("Debug - XXT 最长公共子串: " . $common_substring . " (长度: " . $max_common_length . ")");
					
					// 如果最长公共子串长度小于最小匹配长度，记录日志但仍然返回结果（不再拒绝）
					if ($max_common_length < $min_match_length) {
						error_log("Debug - XXT 警告：最长公共子串长度(" . $max_common_length . ")小于最小匹配长度(" . $min_match_length . ")，但仍然返回结果");
					}
				
				// 如果有用户ID，记录搜题历史
				if ($uid > 0) {
					// 记录搜题历史（找到答案）
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
					
					// 如果需要扣费，扣除积分并记录日志
					if ($need_pay) {
						$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
						wlog($uid, "蜜雪脚本查询", "查询题目：{$question} - 查询成功，消费1积分", -1);
					}
				}
				
				// 返回结果，兼容油猴脚本格式
				$result = array(
					"code" => 200,
					"msg" => "success",
					"data" => array(
						"question" => $q['question'],
						"answer" => $q['answer'],
						"times" => 999
					)
				);
				}
				} else {
				// 如果有用户ID，记录搜题历史
				if ($uid > 0) {
					// 记录搜题历史（未找到答案）
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
					
					// 记录未找到答案的日志（不扣除积分）
					wlog($uid, "蜜雪脚本查询", "查询题目：{$question} - 未找到答案", 0);
				}
				
				// 未找到答案，返回错误信息
				$result = array(
					"code" => 0,
					"msg" => "未找到答案，请重新描述问题或联系客服添加题库",
					"data" => array(
						"question" => "未找到答案",
						"answer" => "很抱歉，题库中没有找到相关问题的答案",
						"times" => 0
					)
				);
			}
			
				exit(json_encode($result));
			break;
		case 'ai': // AI 接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取POST请求体中的JSON数据
			$json_data = file_get_contents('php://input');
			$post_data = json_decode($json_data, true);
			
			// 从POST JSON或GET/POST参数中获取问题和模型
			$question = '';
			$model = 'net-gpt-3.5-turbo-16k'; // 默认模型
			
			if ($post_data) {
				// 从JSON获取模型
				if (isset($post_data['model'])) {
					$model = trim(strip_tags(daddslashes($post_data['model'])));
				}
				
				// 从JSON获取问题
				if (isset($post_data['messages']) && is_array($post_data['messages'])) {
					foreach ($post_data['messages'] as $message) {
						if (isset($message['role']) && $message['role'] === 'user') {
							$question = trim(strip_tags(daddslashes($message['content'])));
							break;
						}
					}
				}
			}
			
			// 调试日志
			error_log("Debug - AI接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("model: " . $model);
			error_log("question: " . $question);
			
			// 验证参数
			if ($question == '') {
				error_log("Debug - AI参数验证失败");
				exit(json_encode(array("code" => 1001, "msg" => "问题不能为空", "data" => array("answer" => ""))));
			}
			
			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				exit(json_encode(array("code" => 1001, "msg" => "密钥错误", "data" => array("answer" => ""))));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 设置不同模型的积分消费
			$model_cost = array(
				'gpt-3.5-turbo-16k' => 1,
				'deepseek-chat' => 2,
				'gpt-4o-mini' => 2,
				'gpt-4' => 10,
				'glm-4-flash' => 2
			);
			
			// 根据模型确定需要的积分
			$required_points = isset($model_cost[$model]) ? $model_cost[$model] : 1;
			
			// 检查用户积分是否足够
			if($row['money'] < $required_points) {
				exit(json_encode(array("code" => 0, "msg" => "积分不足，无法进行搜索，请充值。当前模型 {$model} 需要 {$required_points} 积分", "data" => array("answer" => "", "question" => $question, "times" => 0))));
			}
			
			// 根据客户端传递的模型标识符映射到实际API使用的模型名称
			$model_mapping = array(
				'gpt-3.5-turbo-16k' => 'gpt-3.5-turbo-16k',
				'deepseek-chat' => 'deepseek-chat',
				'gpt-4o-mini' => 'gpt-4o-mini',
				'gpt-4' => 'gpt-4',
				'glm-4-flash' => 'glm-4-flash'
			);
			
			// 获取实际使用的模型名称，如果映射中没有则使用默认模型
			$api_model = isset($model_mapping[$model]) ? $model_mapping[$model] : 'gpt-3.5-turbo-16k';
			
			// 记录映射情况
			error_log("模型映射: 客户端模型 {$model} 映射到 API模型 {$api_model}");
			
			// 调用外部AI API
			$ai_data = array(
				"model" => $api_model,
				"messages" => array(
					array(
						"role" => "system",
						"search" => "true",
						"content" => "你是一个专业的答题助手。请根据题目类型给出准确的答案。回答要求：1. 选择题：只返回答案加：选项字母，如 'A' 或 'ABC' 答案：A,并介绍为什么选这个选项 2.判断题：只返回 '正确' 或 '错误' 答案：正确，并介绍为什么选这个答案 3. 填空题：直接返回答案：答案，并介绍为什么选这个答案 4. 其他题型：返回简洁的答案加上为什么选这个答案"
					),
					array(
						"role" => "user",
						"content" => $question
					)
				)
			);
			
			// 记录请求日志
			wlog($uid, "AI助手", "准备请求模型: {$api_model}, 问题: {$question}", 0);
			
			// 记录实际发送的请求体，用于调试
			error_log("AI API请求数据: " . json_encode($ai_data, JSON_UNESCAPED_UNICODE));
			
			$ch = curl_init('https://api.ephone.ai/v1/chat/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ai_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Accept: application/json',
				'Authorization: sk-YQSpuaSrMqVxGviMB07zYwOAkm90ZK48mffK852a2r8iEPgi',
				'search: true',
				'Content-Type: application/json'
			));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 禁用 SSL 证书验证
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 禁用主机名验证
			
			// 执行请求前记录
			error_log("准备执行 cURL 请求到 https://api.ephone.ai/v1/chat/completions");
			
			$response = curl_exec($ch);
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$curl_error = curl_error($ch);
			$curl_errno = curl_errno($ch);
			curl_close($ch);
			
			// 记录API请求结果
			error_log("AI API响应: HTTP码 " . $http_code . ", cURL错误码: " . $curl_errno . ", cURL错误: " . $curl_error);
			if ($response) {
				error_log("API响应内容: " . substr($response, 0, 1000));
				} else {
				error_log("API没有返回响应内容");
			}
			
			if ($http_code == 200) {
				$ai_result = json_decode($response, true);
				if (isset($ai_result['choices'][0]['message']['content'])) {
					// 扣除用户积分
					$DB->query("UPDATE qingka_wangke_user SET money=money-{$required_points} WHERE uid='$uid'");
					// 记录积分扣除日志
					wlog($uid, "AI助手", "AI问答消费{$required_points}积分，使用模型：{$model}", -$required_points);
					
					// 记录成功的AI请求
					$time = date('Y-m-d H:i:s');
					$answer = $ai_result['choices'][0]['message']['content'];
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '0', 'AI问答', '{$answer}', '100', '{$time}')");
					
					// 返回结果，格式与 xxt 接口一致
					exit(json_encode(array(
						"code" => 200,
						"msg" => "success",
						"data" => array(
							"answer" => $answer,
							"question" => $question,
							"times" => 999
						)
					)));
				}
			}
			
			// 记录失败的请求
			wlog($uid, "AI助手", "AI问答请求失败: {$question}, HTTP码: {$http_code}, cURL错误: {$curl_error}", 0);
			exit(json_encode(array(
				"code" => 1001,
				"msg" => "AI服务请求失败，请稍后重试",
				"data" => array(
					"answer" => "",
					"question" => $question,
					"times" => 0
				)
			)));
			break;

		case 'autoai': // 自动AI答题接口
			// 获取认证信息
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取POST请求体中的JSON数据
			$json_data = file_get_contents('php://input');
			$post_data = json_decode($json_data, true);
			
			// 从POST JSON或GET/POST参数中获取问题和模型
			$question = '';
			$model = 'gpt-3.5-turbo-16k'; // 默认模型
			
			if ($post_data) {
				// 从JSON获取模型
				if (isset($post_data['model'])) {
					$model = trim(strip_tags(daddslashes($post_data['model'])));
				}
				
				// 从JSON获取问题
				if (isset($post_data['messages']) && is_array($post_data['messages'])) {
					foreach ($post_data['messages'] as $message) {
						if (isset($message['role']) && $message['role'] === 'user') {
							$question = trim(strip_tags(daddslashes($message['content'])));
			break;
						}
					}
				}
			}
			
			// 调试日志
			error_log("Debug - AutoAI接收到的参数:");
			error_log("Authorization: " . $auth_header);
			error_log("key: " . $key);
			error_log("model: " . $model);
			error_log("question: " . $question);
			
			// 验证参数
			if ($question == '') {
				error_log("Debug - AutoAI参数验证失败");
				exit(json_encode(array("code" => 0, "msg" => "问题不能为空", "data" => array("answer" => "", "question" => "", "times" => 0))));
			}
			
			// 验证key
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				exit(json_encode(array("code" => 0, "msg" => "密钥错误", "data" => array("answer" => "", "question" => $question, "times" => 0))));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 设置不同模型的积分消费
			$model_cost = array(
				'gpt-3.5-turbo-16k' => 1,
				'deepseek-chat' => 2,
				'gpt-4o-mini' => 2,
				'gpt-4' => 10,
				'glm-4-flash' => 2
			);
			
			// 根据模型确定需要的积分
			$required_points = isset($model_cost[$model]) ? $model_cost[$model] : 1;
			
			// 检查用户积分是否足够
			if($row['money'] < $required_points) {
				exit(json_encode(array("code" => 1001, "msg" => "积分不足，无法进行搜索，请充值。当前模型 {$model} 需要 {$required_points} 积分", "data" => array("answer" => ""))));
			}
			
			// 根据客户端传递的模型标识符映射到实际API使用的模型名称
			$model_mapping = array(
				'gpt-3.5-turbo-16k' => 'gpt-3.5-turbo-16k',
				'deepseek-chat' => 'deepseek-chat',
				'gpt-4o-mini' => 'gpt-4o-mini',
				'gpt-4' => 'gpt-4',
				'glm-4-flash' => 'glm-4-flash'
			);
			
			// 获取实际使用的模型名称，如果映射中没有则使用默认模型
			$api_model = isset($model_mapping[$model]) ? $model_mapping[$model] : 'gpt-3.5-turbo-16k';
			
			// 记录映射情况
			error_log("AutoAI模型映射: 客户端模型 {$model} 映射到 API模型 {$api_model}");
			
			// 获取系统提示词，如果发送请求中包含则使用请求中的
			$system_prompt = "你是一个专业的答题助手，请严格按照以下规则回答问题：

1. **单选题规则**:
   - 分析题目和选项
   - 找出正确答案
   - 只返回正确答案的完整选项内容（不要返回选项字母如A/B/C/D）
   - 例如：题目'索氏抽提法测定粗脂肪含量的时，用（ ）作提取剂。A. 乙醇 B. 甲苯 C. 丙酮 D. 乙醚或石油醚'
   - 正确回答：'乙醚或石油醚'（而不是返回'D'）

2. **多选题规则**:
   - 找出所有正确选项
   - 返回所有正确选项的完整内容，用###分隔
   - 例如：'素氏抽取器接收瓶###乙醚必须无水###控制回流速度'
   - 不要返回选项字母，不要添加序号或标记

3. **判断题规则**:
   - 只返回'正确'或'错误'
   - 不要添加任何解释

4. **填空题规则**:
   - 直接返回填空内容
   - 多个空用###分隔

5. **简答题规则**:
   - 直接回答问题要点，简洁明了
   - 不要使用序号、标记符号（如*、•、1.、2.等）
   - 不要添加\"答案：\"等前缀
   - 多个要点用句号分隔，保持在一段内
   - 例如：室内楼梯栏杆通过固定支撑柱连接到梯段。踏步防滑可采用防滑条或防滑材料。楼梯平面形式有直梯、L型梯、U型梯等。


重要：永远不要在答案中包含选项字母（如A、B、C、D），只返回选项的实际内容文本。不要添加任何解释、分析或其他内容，只返回答案本身。";
			
			if ($post_data && isset($post_data['messages']) && is_array($post_data['messages'])) {
				foreach ($post_data['messages'] as $message) {
					if (isset($message['role']) && $message['role'] === 'system') {
						$system_prompt = trim(strip_tags(daddslashes($message['content'])));
						break;
					}
				}
			}
			
			// 调用外部AI API
			$ai_data = array(
				"model" => $api_model,
				"messages" => array(
					array(
						"role" => "system",
						"search" => "true",
						"content" => $system_prompt
					),
					array(
						"role" => "user",
						"content" => $question
					)
				),
				"temperature" => 0.1, // 降低温度值，使答案更加确定
				"max_tokens" => 200 // 限制返回长度，避免过长的解释
			);
			
			// 记录请求日志
			wlog($uid, "自动AI答题", "准备请求模型: {$api_model}, 问题: {$question}", 0);
			
			// 记录实际发送的请求体，用于调试
			error_log("AutoAI API请求数据: " . json_encode($ai_data, JSON_UNESCAPED_UNICODE));
			
			$ch = curl_init('https://api.ephone.ai/v1/chat/completions');
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ai_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Accept: application/json',
				'Authorization: sk-YQSpuaSrMqVxGviMB07zYwOAkm90ZK48mffK852a2r8iEPgi',
				'search: true',
				'Content-Type: application/json'
			));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 20); // 设置超时时间为20秒，自动化场景需要快速响应
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 禁用 SSL 证书验证
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 禁用主机名验证
			
			// 执行请求前记录
			error_log("准备执行 AutoAI cURL 请求到 https://api.ephone.ai/v1/chat/completions");
			
			$response = curl_exec($ch);
			$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			$curl_error = curl_error($ch);
			$curl_errno = curl_errno($ch);
			curl_close($ch);
			
			// 记录API请求结果
			error_log("AutoAI API响应: HTTP码 " . $http_code . ", cURL错误码: " . $curl_errno . ", cURL错误: " . $curl_error);
			if ($response) {
				error_log("AutoAI API响应内容: " . substr($response, 0, 1000));
			} else {
				error_log("AutoAI API没有返回响应内容");
			}
			
			if ($http_code == 200) {
				$ai_result = json_decode($response, true);
				if (isset($ai_result['choices'][0]['message']['content'])) {
					// 扣除用户积分
					$DB->query("UPDATE qingka_wangke_user SET money=money-{$required_points} WHERE uid='$uid'");
					// 记录积分扣除日志
					wlog($uid, "自动AI答题", "自动AI答题消费{$required_points}积分，使用模型：{$model}", -$required_points);
					
					// 记录成功的AI请求
					$time = date('Y-m-d H:i:s');
					$answer = $ai_result['choices'][0]['message']['content'];
					
					// 处理答案，移除多余的解释
					$answer_cleaned = $answer;
					
					// 清理答案，移除多余的解释和格式
					// 1. 如果包含"答案："，提取后面的内容
					if (strpos($answer, '答案：') !== false) {
						$answer_parts = explode('答案：', $answer);
						if (count($answer_parts) > 1) {
							$answer_cleaned = trim($answer_parts[1]);
							
							// 如果后面还有解释，只保留第一段
							$explanation_parts = preg_split('/[\r\n]+/', $answer_cleaned, 2);
							if (count($explanation_parts) > 0) {
								$answer_cleaned = trim($explanation_parts[0]);
							}
						}
					}
					
					// 2. 移除可能的选项标识符和其他格式
					$answer_cleaned = preg_replace('/^[A-D][\.\、\:：]?\s*/', '', $answer_cleaned); // 移除开头的选项标识如"A."
					$answer_cleaned = preg_replace('/[\.\。\,\，\;\；]$/', '', $answer_cleaned); // 移除结尾的标点符号
					$answer_cleaned = trim($answer_cleaned); // 移除首尾空格
					
					// 记录处理后的答案
					error_log("AutoAI 原始答案: " . $answer);
					error_log("AutoAI 处理后答案: " . $answer_cleaned);
					
					// 记录搜题历史
					$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
							   VALUES ('{$uid}', '{$question}', '0', '自动AI答题', '{$answer}', '100', '{$time}')");
					
					// 返回结果，格式与 xxt 接口一致
					exit(json_encode(array(
						"code" => 200,
						"msg" => "success",
						"data" => array(
							"answer" => $answer_cleaned, // 返回处理后的答案，不包括解释
							"question" => $question,
							"times" => 999
						)
					)));
				}
			}
			
			// 记录失败的请求
			wlog($uid, "自动AI答题", "自动AI答题请求失败: {$question}, HTTP码: {$http_code}, cURL错误: {$curl_error}", 0);
			exit(json_encode(array(
				"code" => 0, // 使用与xxt接口一致的错误码
				"msg" => "AI服务请求失败，请稍后重试",
				"data" => array(
					"answer" => "AI服务暂时不可用，请稍后重试", // 提供一个明确的错误信息
					"question" => $question,
					"times" => 0
				)
			)));
			break;

		case 'get_orders': // 根据账号查询订单
			$uid = trim(strip_tags(daddslashes($_POST['uid'])));
			$key = trim(strip_tags(daddslashes($_POST['key'])));
			$username = trim(strip_tags(daddslashes($_POST['username'])));
				
				// 验证参数
			if ($uid == '' || $key == '') {
				exit(json_encode(['status' => 'error', 'message' => '用户ID或密钥不能为空']));
				}
				
			// 验证用户
				$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			if (!$row) {
				// 用户不存在
				wlog(0, "订单查询", "用户{$uid}不存在", 0);
				exit(json_encode(['status' => 'error', 'message' => '用户ID不存在']));
				} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "订单查询", "密钥错误", 0);
				exit(json_encode(['status' => 'error', 'message' => '密钥错误']));
			}
			
			// 构建查询条件
			$where = "dockstatus='99'  AND status='待处理' AND cid='99999'";
			
			// 如果提供了username，则按username查询
			if (!empty($username)) {
				$username = $DB->escape($username); // 防SQL注入
				$where .= " AND user='$username'";
			}
			
			// 查询订单数据
			$orders = $DB->query("SELECT * FROM qingka_wangke_order WHERE $where ORDER BY addtime ASC LIMIT 1");
			$result = array();
			
			// 处理查询结果
			while ($order = $DB->fetch($orders)) {
				$result[] = array(
					'oid' => $order['oid'],
					'user' => $order['user'],
					'pass' => $order['pass'],
					'school' => $order['school'],
					'kcname' => $order['kcname'],
					'kcid' => $order['kcid'],  // 添加课程ID字段
					'status' => $order['status'],
					'addtime' => $order['addtime'],
					'dockstatus' => $order['dockstatus'],
					'process' => $order['process'],
					'remarks' => $order['remarks']
				);
			}
			
			// 如果找到了订单记录，将其状态更新为"进行中"
			if (count($result) > 0) {
				// 收集查询到的订单ID
				$order_ids = array();
				foreach ($result as $order) {
					$order_ids[] = $order['oid'];
				}
				
				// 只更新查询到的这些订单的状态
				if (!empty($order_ids)) {
					$ids_str = implode(',', $order_ids);
					$DB->query("UPDATE qingka_wangke_order SET status='进行中' WHERE oid IN ({$ids_str})");
					
					// 修改返回结果中的状态值
					foreach ($result as &$order) {
						$order['status'] = '进行中';
					}
					unset($order); // 解除引用
					
					// 记录状态更新日志
					wlog($uid, "订单状态更新", "将查询到的".count($result)."条订单状态更新为'进行中'", 0);
				}
			}
			
			// 记录日志
			$count = count($result);
			wlog($uid, "订单查询", "查询dockstatus=99的订单，找到{$count}条记录", 0);
			
			// 返回结果
			exit(json_encode([
				'status' => 'success',
				'message' => "查询成功，共找到{$count}条记录",
				'data' => $result
			]));
			break;
	
		// 新增update_progress接口
		case 'update_progress':
			// 验证必要参数
			$uid = isset($_POST['uid']) ? intval($_POST['uid']) : 0;
			$key = isset($_POST['key']) ? trim($_POST['key']) : '';
			$username = isset($_POST['username']) ? trim($_POST['username']) : '';
			$password = isset($_POST['password']) ? trim($_POST['password']) : '';
			$kcid = isset($_POST['kcid']) ? trim($_POST['kcid']) : '';
			$progress_data = isset($_POST['progress_data']) ? trim($_POST['progress_data']) : '';
			$progress_percent = isset($_POST['progress_percent']) ? intval($_POST['progress_percent']) : 0;
			
			// 参数验证
			if ($uid <= 0 || empty($key)) {
				$result = [
					'status' => 'error',
					'message' => '用户ID或密钥不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			if (empty($username) || empty($password)) {
				$result = [
					'status' => 'error',
					'message' => '账号和密码不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			if (empty($progress_data)) {
				$result = [
					'status' => 'error',
					'message' => '进度数据不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 验证用户身份（使用与其他接口相同的验证逻辑）
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			
			if (!$row) {
				// 用户不存在
				wlog(0, "进度更新", "用户{$uid}不存在", 0);
				$result = [
					'status' => 'error',
					'message' => '用户ID不存在'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "进度更新", "密钥错误", 0);
				$result = [
					'status' => 'error',
					'message' => '密钥错误'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 构建查询条件
			$username = $DB->escape($username); // 防SQL注入
			$password = $DB->escape($password);
			
			// 构建查询SQL
			$query = "SELECT * FROM `qingka_wangke_order` WHERE `user` = '{$username}' AND `pass` = '{$password}'AND `kcid` = '{$kcid}'AND `cid` = '99999'";
			
			// 如果提供了课程ID，则添加课程ID条件
			if (!empty($kcid)) {
				$kcid = $DB->escape($kcid);
				$query .= " AND `kcid` LIKE '%{$kcid}%'";
			}
			
			$query .= " ORDER BY `oid` DESC LIMIT 1";
			
			$result = $DB->query($query);
			$order_data = $DB->fetch($result);
			
			if (!$order_data) {
				$result = [
					'status' => 'error',
					'message' => '未找到匹配的订单'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			$order_id = $order_data['oid'];
			
			// 安全处理进度数据
			$progress_data = $DB->escape($progress_data);
			
			// 更新订单remarks和process字段
			$update_query = "UPDATE `qingka_wangke_order` SET 
							 `remarks` = '{$progress_data}',
							 `process` = '{$progress_percent}%'
							 WHERE `oid` = {$order_id}";
			
			$update_result = $DB->query($update_query);
			
			if ($update_result) {
				// 记录进度更新日志
				// wlog($uid, "进度更新", "订单ID: {$order_id}, 账号: {$username}, 进度: {$progress_percent}%", 0);
				$result = [
					'status' => 'success',
					'message' => '进度数据更新成功',
					'oid' => $order_id
				];
				} else {
				wlog($uid, "进度更新", "更新失败 - 订单ID: {$order_id}, 账号: {$username}", 0);
				$result = [
					'status' => 'error',
					'message' => '进度数据更新失败: ' . $DB->error()
				];
			}
			
			echo json_encode($result, JSON_UNESCAPED_UNICODE);
				break;
				
		// 新增complete_task接口 - 用于在所有课程学习任务完成时更新订单状态
		case 'complete_task':
			// 验证必要参数
			$uid = isset($_POST['uid']) ? intval($_POST['uid']) : 0;
			$key = isset($_POST['key']) ? trim($_POST['key']) : '';
			$username = isset($_POST['username']) ? trim($_POST['username']) : '';
			$password = isset($_POST['password']) ? trim($_POST['password']) : '';
			$kcid = isset($_POST['kcid']) ? trim($_POST['kcid']) : '';
			
			// 参数验证
			if ($uid <= 0 || empty($key)) {
				$result = [
					'status' => 'error',
					'message' => '用户ID或密钥不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			if (empty($username) || empty($password)) {
				$result = [
					'status' => 'error',
					'message' => '账号和密码不能为空'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 验证用户身份
			$row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
			
			if (!$row) {
				// 用户不存在
				wlog(0, "任务完成", "用户{$uid}不存在", 0);
				$result = [
					'status' => 'error',
					'message' => '用户ID不存在'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			} elseif ($row['key'] != $key) {
				// 密钥错误
				wlog($uid, "任务完成", "密钥错误", 0);
				$result = [
					'status' => 'error',
					'message' => '密钥错误'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			// 记录API调用日志
			wlog($uid, "任务完成API", "接收任务完成请求 - 账号: {$username}", 0);
			
			// 构建查询条件
			$username = $DB->escape($username); // 防SQL注入
			$password = $DB->escape($password);
			
			// 构建查询SQL
			$query = "SELECT * FROM `qingka_wangke_order` WHERE `user` = '{$username}' AND `pass` = '{$password}'";
			
			// 如果提供了课程ID，则添加课程ID条件
			if (!empty($kcid)) {
				$kcid = $DB->escape($kcid);
				$query .= " AND `kcid` LIKE '%{$kcid}%'";
			}
			
			$query .= " ORDER BY `oid` DESC LIMIT 1";
			
			$result = $DB->query($query);
			$order_data = $DB->fetch($result);
			
			if (!$order_data) {
				wlog($uid, "任务完成", "未找到匹配的订单 - 账号: {$username}", 0);
				$result = [
					'status' => 'error',
					'message' => '未找到匹配的订单'
				];
				echo json_encode($result, JSON_UNESCAPED_UNICODE);
				exit;
			}
			
			$order_id = $order_data['oid'];
			$kcname = $order_data['kcname'];
			
			// 更新订单状态为已完成，并设置进度为100%
			$update_query = "UPDATE `qingka_wangke_order` SET 
							 `status` = '已完成',
							 `process` = '100%',
							 `remarks` = '所有课程学习任务已完成'
							 WHERE `oid` = {$order_id}";
			
			$update_result = $DB->query($update_query);
			
			if ($update_result) {
				// 记录完成任务日志
				wlog($uid, "任务完成", "订单ID: {$order_id}, 账号: {$username}, 课程: {$kcname}, 状态已更新为'已完成'", 0);
				// 记录系统通知日志
				wlog($uid, "系统通知", "你的订单 [{$order_id}] {$kcname} 已完成", 0);
				$result = [
					'status' => 'success',
					'message' => '任务已标记为完成',
					'oid' => $order_id
				];
			} else {
				wlog($uid, "任务完成", "更新失败 - 订单ID: {$order_id}, 账号: {$username}, 错误: " . $DB->error(), 0);
				$result = [
					'status' => 'error',
					'message' => '任务完成状态更新失败: ' . $DB->error()
				];
			}
			
			echo json_encode($result, JSON_UNESCAPED_UNICODE);
			break;


		
		    case 'aimodel': // AI大模型专用接口
			// 获取认证信息 - 改进验证流程，参考autoai接口
			$auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
			$key = '';
			
			// 从Authorization头部提取Bearer token
			if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
				$key = $matches[1];
			}
			
			// 如果Authorization头部没有token，尝试从GET/POST参数获取key
			if (empty($key)) {
				$key = isset($_POST['key']) ? trim(strip_tags(daddslashes($_POST['key']))) : (isset($_GET['key']) ? trim(strip_tags(daddslashes($_GET['key']))) : '');
			}
			
			// 获取请求参数
			$question = isset($_POST['question']) ? trim($_POST['question']) : '';
			$model = isset($_POST['model']) ? trim($_POST['model']) : 'gpt-3.5-turbo-16k'; // 默认模型
			$prompt = isset($_POST['prompt']) ? trim($_POST['prompt']) : '';
			
			// 验证参数
			if(empty($question)){
				wlog(0, "AI大模型", "请求验证失败: 缺少question参数", 0);
				exit(json_encode(['code'=>-1, 'msg'=>'请提供问题内容']));
			}
			
			// 严格验证key - 检查用户表
			$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			if (!$row) {
				wlog(0, "AI大模型", "请求验证失败: 密钥错误: " . substr($key, 0, 10) . "...", 0);
				exit(json_encode(['code'=>-1, 'msg'=>'密钥错误，请提供有效的key']));
			}
			
			// 获取用户ID用于记录
			$uid = $row['uid'];
			
			// 设置不同模型的积分消费
			$model_cost = array(
				'gpt-3.5-turbo-16k' => 1,
				'deepseek-chat' => 2,
				'gpt-4o-mini' => 2,
				'gpt-4' => 10,
				'glm-4-flash' => 2
			);
			
			// 根据模型确定需要的积分
			$required_points = isset($model_cost[$model]) ? $model_cost[$model] : 1;
			
			// 检查用户积分是否足够
			if($row['money'] < $required_points) {
				wlog($uid, "AI大模型", "积分不足，无法使用AI模型 {$model}，当前积分: {$row['money']}, 需要: {$required_points}", 0);
				exit(json_encode(['code'=>-1, 'msg'=>"积分不足，无法使用AI模型，当前积分: {$row['money']}, 需要: {$required_points}"]));
			}
			
			// 记录请求日志
			wlog($uid, "AI大模型", "准备请求模型: {$model}, 问题: " . substr($question, 0, 100) . (strlen($question) > 100 ? "..." : ""), 0);
			
			// 调用AI接口
			try {
				// 准备请求数据
				$default_system_prompt = "你是一个专业的答题助手，请严格按照以下规则回答问题：

1. **单选题规则**:
   - 分析题目和选项
   - 找出正确答案
   - 只返回正确答案的完整选项内容（不要返回选项字母如A/B/C/D）
   - 例如：题目'索氏抽提法测定粗脂肪含量的时，用（ ）作提取剂。A. 乙醇 B. 甲苯 C. 丙酮 D. 乙醚或石油醚'
   - 正确回答：'乙醚或石油醚'（而不是返回'D'）

2. **多选题规则**:
   - 找出所有正确选项
   - 返回所有正确选项的完整内容，用###分隔
   - 例如：'素氏抽取器接收瓶###乙醚必须无水###控制回流速度'
   - 不要返回选项字母，不要添加序号或标记

3. **判断题规则**:
   - 只返回'正确'或'错误'
   - 不要添加任何解释

4. **填空题规则**:
   - 直接返回填空内容
   - 多个空用###分隔

5. **简答题规则**:
   - 直接回答问题要点，简洁明了
   - 不要使用序号、标记符号（如*、•、1.、2.等）
   - 不要添加\"答案：\"等前缀
   - 多个要点用句号分隔，保持在一段内
   - 例如：室内楼梯栏杆通过固定支撑柱连接到梯段。踏步防滑可采用防滑条或防滑材料。楼梯平面形式有直梯、L型梯、U型梯等。


重要：永远不要在答案中包含选项字母（如A、B、C、D），只返回选项的实际内容文本。不要添加任何解释、分析或其他内容，只返回答案本身。";
				$system_prompt = !empty($prompt) ? $prompt : $default_system_prompt;
				
				// 构建请求数据
				$ai_data = array(
					"model" => $model,
					"messages" => array(
						array(
							"role" => "system",
							"content" => $system_prompt
						),
						array(
							"role" => "user",
							"content" => $question
						)
					),
					"temperature" => 0.7
				);
				
				// 发送API请求
				$ch = curl_init('https://api.ephone.ai/v1/chat/completions');
				curl_setopt($ch, CURLOPT_POST, 1);
				curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($ai_data));
				curl_setopt($ch, CURLOPT_HTTPHEADER, array(
					'Accept: application/json',
					'Authorization: sk-YQSpuaSrMqVxGviMB07zYwOAkm90ZK48mffK852a2r8iEPgi',
					'Content-Type: application/json'
				));
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_TIMEOUT, 15); // 超时时间15秒
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
				
				$response = curl_exec($ch);
				$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
				$curl_error = curl_error($ch);
				curl_close($ch);
				
				// 处理响应
				if ($http_code == 200 && $response) {
					$ai_result = json_decode($response, true);
					if (isset($ai_result['choices'][0]['message']['content'])) {
						$answer = $ai_result['choices'][0]['message']['content'];
						
						// 扣除积分
						$DB->query("UPDATE qingka_wangke_user SET money=money-{$required_points} WHERE uid='$uid'");
						// 记录积分扣除日志
						wlog($uid, "AI大模型", "消费{$required_points}积分，使用模型：{$model}", -$required_points);
						
						// 记录搜题历史
						$time = date('Y-m-d H:i:s');
						$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
								   VALUES ('{$uid}', '{$question}', '0', 'AI大模型[{$model}]', '{$answer}', '100', '{$time}')");
						
						exit(json_encode(['code'=>1, 'msg'=>'success', 'data'=>$answer]));
					} else {
						// 响应格式不正确
						wlog($uid, "AI大模型", "AI响应格式不正确，模型: {$model}", 0);
						exit(json_encode(['code'=>-1, 'msg'=>'AI响应格式不正确']));
					}
				} else {
					// 请求失败
					wlog($uid, "AI大模型", "API请求失败: HTTP码={$http_code}, cURL错误: {$curl_error}", 0);
					exit(json_encode(['code'=>-1, 'msg'=>'API请求失败: HTTP码='.$http_code]));
				}
				
			} catch (Exception $e) {
				wlog($uid, "AI大模型", "请求异常: " . $e->getMessage(), 0);
				exit(json_encode(['code'=>-1, 'msg'=>'AI接口请求异常: '.$e->getMessage()]));
			}
			break;








			// case 'daanpipei': // 答案匹配
			// 	$key = trim(strip_tags(daddslashes($_POST['key'])));
			// 	$question = trim(strip_tags(daddslashes($_POST['question'])));
			// 	$options = isset($_POST['options']) ? trim(strip_tags(daddslashes($_POST['options']))) : '';
				
			// 	// 调试日志
			// 	error_log("Debug - 接收到的参数:");
			// 	error_log("key: " . $key);
			// 	error_log("question: " . $question);
			// 	error_log("options: " . $options);
				
			// 	// 验证参数
			// 	if ($key == '' || $question == '') {
			// 		error_log("Debug - 参数验证失败");
			// 		exit('{"code":0,"msg":"所有项目不能为空"}');
			// 	}
	
			// 	// 验证key
			// 	$row = $DB->get_row("select * from qingka_wangke_user where `key`='$key' limit 1");
			// 	if (!$row) {
			// 		$result = array("code" => -2, "msg" => "密匙错误");
			// 		exit(json_encode($result));
			// 	}
				
			// 	// 获取用户ID用于记录
			// 	$uid = $row['uid'];
				
			// 	// 清理问题文本，移除特殊字符以提高匹配率
			// 	$cleaned_question = $question;
			// 	$cleaned_question = preg_replace('/\s+/', ' ', $cleaned_question); // 合并多个空格为一个
			// 	$cleaned_question = trim($cleaned_question); // 移除首尾空格
			// 	$cleaned_question = str_replace(array('()', '[]', '（）', '【】', '(', ')', '[', ']', '（', '）', '【', '】', '"', '"', '\'', ':', '：', '、', '，', ',', '?', '？', '.', '。', '!', '！'), '', $cleaned_question); // 移除各种括号和标点符号
				
			// 	// 提取核心问题（去掉可能的选项部分）
			// 	$core_question = $cleaned_question;
			// 	if (mb_strlen($core_question, 'UTF-8') > 15) {
			// 		// 如果问题较长，尝试提取前半部分作为核心问题
			// 		$core_question = mb_substr($core_question, 0, min(mb_strlen($core_question, 'UTF-8'), 30), 'UTF-8');
			// 	}
				
			// 	error_log("Debug - 清理后的问题: " . $cleaned_question);
			// 	error_log("Debug - 核心问题: " . $core_question);
				
			// 	// 查询题库 - 使用多种查询方式
			// 	$original_question = $DB->escape($question); // 原始问题（SQL注入防护）
			// 	$cleaned_question = $DB->escape($cleaned_question); // 清理后的问题（SQL注入防护）
			// 	$core_question = $DB->escape($core_question); // 核心问题（SQL注入防护）
				
			// 	// 1. 先尝试完全匹配原始问题
			// 	$sql = "SELECT * FROM question WHERE question = '{$original_question}' LIMIT 1";
			// 	error_log("Debug - SQL精确查询(原始): " . $sql);
			// 	$q = $DB->get_row($sql);
				
			// 	// 2. 如果未匹配，尝试完全匹配清理后问题
			// 	if (!$q) {
			// 		$sql = "SELECT * FROM question WHERE question = '{$cleaned_question}' LIMIT 1";
			// 		error_log("Debug - SQL精确查询(清理后): " . $sql);
			// 		$q = $DB->get_row($sql);
			// 	}
				
			// 	// 3. 如果仍未匹配，尝试题库表中的问题去除特殊字符后与清理后的问题进行比较
			// 	if (!$q) {
			// 		$sql = "SELECT * FROM question WHERE REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
			// 		'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ',', ''), '，', ''), '?', ''), '？', ''), '.', ''), '。', '') = '{$cleaned_question}' LIMIT 1";
			// 		error_log("Debug - SQL特殊字符对比查询: " . $sql);
			// 		$q = $DB->get_row($sql);
			// 	}
				
			// 	// 4. 如果仍未匹配，尝试模糊匹配原始问题
			// 	if (!$q) {
			// 		$sql = "SELECT * FROM question WHERE question LIKE '%{$original_question}%' LIMIT 1";
			// 		error_log("Debug - SQL模糊查询(原始): " . $sql);
			// 		$q = $DB->get_row($sql);
			// 	}
				
			// 	// 5. 如果仍未匹配，尝试模糊匹配清理后问题
			// 	if (!$q) {
			// 		$sql = "SELECT * FROM question WHERE question LIKE '%{$cleaned_question}%' LIMIT 1";
			// 		error_log("Debug - SQL模糊查询(清理后): " . $sql);
			// 		$q = $DB->get_row($sql);
			// 	}
				
			// 	// 6. 如果仍未匹配，尝试核心问题的模糊匹配
			// 	if (!$q && mb_strlen($core_question, 'UTF-8') > 5) {
			// 		$sql = "SELECT * FROM question WHERE question LIKE '%{$core_question}%' LIMIT 1";
			// 		error_log("Debug - SQL核心问题模糊查询: " . $sql);
			// 		$q = $DB->get_row($sql);
			// 	}
				
			// 	// 7. 如果仍未匹配，尝试将清理后问题作为题库的子串或反向查询
			// 	if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
			// 		$sql = "SELECT * FROM question WHERE 
			// 				question LIKE '%{$cleaned_question}%' OR 
			// 				'{$cleaned_question}' LIKE CONCAT('%', REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, 
			// 				'(', ''), ')', ''), '（', ''), '）', ''), '[', ''), ']', ''), ' ', ''), '　', ''), '%') 
			// 				LIMIT 1";
			// 		error_log("Debug - SQL双向子串查询: " . $sql);
			// 		$q = $DB->get_row($sql);
			// 	}
				
			// 	// 8. 尝试关键词匹配 - 提取较长的词语作为关键词进行搜索
			// 	if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 5) {
			// 		// 将问题拆分为关键词
			// 		$keywords = preg_split('/\s+/', $cleaned_question);
			// 		$long_keywords = array();
					
			// 		// 筛选长度大于3的关键词
			// 		foreach ($keywords as $keyword) {
			// 			if (mb_strlen($keyword, 'UTF-8') > 3) {
			// 				$long_keywords[] = $DB->escape($keyword);
			// 			}
			// 		}
					
			// 		// 如果没有大于3的关键词，就尝试使用所有的关键词
			// 		if (empty($long_keywords) && !empty($keywords)) {
			// 			foreach ($keywords as $keyword) {
			// 				if (mb_strlen($keyword, 'UTF-8') > 1) {
			// 					$long_keywords[] = $DB->escape($keyword);
			// 				}
			// 			}
			// 		}
					
			// 		// 如果有足够的关键词，使用它们进行查询
			// 		if (!empty($long_keywords)) {
			// 			$keyword_conditions = array();
			// 			foreach ($long_keywords as $keyword) {
			// 				$keyword_conditions[] = "question LIKE '%{$keyword}%'";
			// 			}
						
			// 			$sql = "SELECT * FROM question WHERE " . implode(" OR ", $keyword_conditions) . " LIMIT 1";
			// 			error_log("Debug - SQL关键词查询: " . $sql);
			// 			$q = $DB->get_row($sql);
			// 		}
			// 	}
				
			// 	// 9. 如果仍未匹配，尝试分词匹配（将问题分成几部分，只要匹配其中一部分）
			// 	if (!$q && mb_strlen($cleaned_question, 'UTF-8') > 10) {
			// 		$parts = array();
			// 		$len = mb_strlen($cleaned_question, 'UTF-8');
					
			// 		// 将问题分成3部分尝试匹配
			// 		$part1 = mb_substr($cleaned_question, 0, ceil($len/3), 'UTF-8');
			// 		$part2 = mb_substr($cleaned_question, ceil($len/3), ceil($len/3), 'UTF-8');
			// 		$part3 = mb_substr($cleaned_question, 2*ceil($len/3), $len-2*ceil($len/3), 'UTF-8');
					
			// 		if (mb_strlen($part1, 'UTF-8') > 5) $parts[] = $DB->escape($part1);
			// 		if (mb_strlen($part2, 'UTF-8') > 5) $parts[] = $DB->escape($part2);
			// 		if (mb_strlen($part3, 'UTF-8') > 5) $parts[] = $DB->escape($part3);
					
			// 		if (!empty($parts)) {
			// 			$part_conditions = array();
			// 			foreach ($parts as $part) {
			// 				$part_conditions[] = "question LIKE '%{$part}%'";
			// 			}
						
			// 			$sql = "SELECT * FROM question WHERE " . implode(" OR ", $part_conditions) . " LIMIT 1";
			// 			error_log("Debug - SQL分词查询: " . $sql);
			// 			$q = $DB->get_row($sql);
			// 		}
			// 	}
				
			// 	error_log("Debug - 查询结果: " . print_r($q, true));
				
			// 	// 计算相似度并记录搜题历史
			// 	$time = date('Y-m-d H:i:s');
			// 	if ($q) {
			// 		// 检查答案是否为占位符（如"____"或空值）
			// 		if ($q['answer'] == "____" || trim($q['answer']) == "" || $q['answer'] == "0") {
			// 			// 记录搜题历史（找到题目但答案无效）
			// 			$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
			// 					   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '答案无效', '0', '{$time}')");
						
			// 			// 返回未找到答案的结果
			// 			$result = array(
			// 				"code" => 0,
			// 				"data" => array(
			// 					"question" => "未找到有效答案！",
			// 					"answer" => "很抱歉, 题目找到但答案无效。",
			// 					"times" => 999
			// 				),
			// 				"message" => "验证成功，但是没有找到有效答案"
			// 			);
			// 			// 记录未找到有效答案的日志
			// 			wlog($uid, "蜜雪程序查询", "查询题目：{$question} - 找到题目但答案无效", 0);
			// 		} else {
			// 			// 计算相似度
			// 			$accuracy = 0;
			// 			similar_text($question, $q['question'], $accuracy);
			// 			$accuracy = round($accuracy, 2);
						
			// 			// 记录搜题历史（找到答案）
			// 			$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
			// 					   VALUES ('{$uid}', '{$question}', '{$q['id']}', '{$q['question']}', '{$q['answer']}', '{$accuracy}', '{$time}')");
						
			// 			// 检查用户积分是否足够
			// 			if($row['money'] < 1) {
			// 				exit('{"code":-3,"msg":"积分不足，无法进行搜索，请充值"}');
			// 			}
						
			// 			// 扣除用户积分（只在查询成功时扣除）
			// 			$DB->query("UPDATE qingka_wangke_user SET money=money-1 WHERE uid='$uid'");
			// 			// 记录积分扣除日志
			// 			wlog($uid, "积分消费", "API题库搜索消费1积分", -1);
						
			// 			// 处理选择题答案匹配
			// 			$answer_letter = '';
			// 			$original_answer = $q['answer'];
						
			// 			// 如果提供了选项，尝试匹配答案对应的选项
			// 			if (!empty($options)) {
			// 				// 解析选项
			// 				$option_array = array();
			// 				$option_lines = explode("\n", $options);
			// 				$current_option = '';
			// 				$current_content = '';
							
			// 				foreach ($option_lines as $line) {
			// 					$line = trim($line);
			// 					if (empty($line)) continue;
								
			// 					// 检查是否是新选项的开始（A、B、C、D等）
			// 					if (preg_match('/^([A-Z])[\.、\s:]?$/i', $line, $matches)) {
			// 						// 保存之前的选项
			// 						if (!empty($current_option) && !empty($current_content)) {
			// 							$option_array[$current_option] = $current_content;
			// 						}
			// 						$current_option = strtoupper($matches[1]);
			// 						$current_content = '';
			// 					} 
			// 					// 检查是否是"A. 内容"格式
			// 					else if (preg_match('/^([A-Z])[\.、\s:](.+)$/i', $line, $matches)) {
			// 						// 保存之前的选项
			// 						if (!empty($current_option) && !empty($current_content)) {
			// 							$option_array[$current_option] = $current_content;
			// 						}
			// 						$current_option = strtoupper($matches[1]);
			// 						$current_content = trim($matches[2]);
			// 						$option_array[$current_option] = $current_content;
			// 					}
			// 					// 否则，这是当前选项的内容
			// 					else if (!empty($current_option)) {
			// 						$current_content .= (!empty($current_content) ? ' ' : '') . $line;
			// 						$option_array[$current_option] = $current_content;
			// 					}
			// 				}
							
			// 				// 清理答案文本，便于匹配
			// 				$cleaned_answer = trim(str_replace(array('，', ',', '、', ' ', '　'), '', $original_answer));
							
			// 				error_log("Debug - 清理后的答案: " . $cleaned_answer);
			// 				error_log("Debug - 选项数组: " . print_r($option_array, true));
							
			// 				// 尝试匹配答案与选项
			// 				$max_similarity = 0;
			// 				$matched_option = '';
							
			// 				foreach ($option_array as $option_letter => $option_content) {
			// 					// 清理选项内容
			// 					$cleaned_option = trim(str_replace(array('，', ',', '、', ' ', '　'), '', $option_content));
			// 					error_log("Debug - 选项 {$option_letter} 清理后: {$cleaned_option}");
								
			// 					// 计算相似度
			// 					similar_text($cleaned_answer, $cleaned_option, $similarity);
			// 					error_log("Debug - 选项 {$option_letter} 相似度: {$similarity}%");
								
			// 					// 精确匹配
			// 					if (strcasecmp($cleaned_answer, $cleaned_option) == 0) {
			// 						$matched_option = $option_letter;
			// 						error_log("Debug - 精确匹配到选项: {$matched_option}");
			// 						break;
			// 					}
								
			// 					// 包含匹配 - 答案包含在选项中
			// 					if (mb_stripos($cleaned_option, $cleaned_answer, 0, 'UTF-8') !== false) {
			// 						if ($similarity > $max_similarity) {
			// 							$max_similarity = $similarity;
			// 							$matched_option = $option_letter;
			// 							error_log("Debug - 答案包含在选项中: {$matched_option}, 相似度: {$similarity}%");
			// 						}
			// 					}
								
			// 					// 选项包含在答案中
			// 					if (mb_stripos($cleaned_answer, $cleaned_option, 0, 'UTF-8') !== false) {
			// 						if ($similarity > $max_similarity) {
			// 							$max_similarity = $similarity;
			// 							$matched_option = $option_letter;
			// 							error_log("Debug - 选项包含在答案中: {$matched_option}, 相似度: {$similarity}%");
			// 						}
			// 					}
								
			// 					// 相似度匹配（如果相似度超过80%）
			// 					if ($similarity > $max_similarity && $similarity > 80) {
			// 						$max_similarity = $similarity;
			// 						$matched_option = $option_letter;
			// 						error_log("Debug - 相似度匹配: {$matched_option}, 相似度: {$similarity}%");
			// 					}
			// 				}
							
			// 				// 如果找到匹配的选项
			// 				if (!empty($matched_option)) {
			// 					$answer_letter = $matched_option;
			// 					error_log("Debug - 最终匹配到的选项: {$answer_letter}");
			// 				}
			// 			}
						
			// 			// 构建返回结果
			// 			$result = array(
			// 				"code" => 1,
			// 				"data" => array(
			// 					"question" => $q['question'],
			// 					"answer" => !empty($answer_letter) ? $answer_letter : $original_answer,
			// 					"original_answer" => $original_answer,
			// 					"times" => 999
			// 				),
			// 				"message" => "请求成功"
			// 			);
						
			// 			// 记录操作日志
			// 			$log_answer = !empty($answer_letter) ? "{$answer_letter}（原始答案：{$original_answer}）" : $original_answer;
			// 			wlog($uid, "蜜雪程序查询", "查询题目：{$question} -答案：{$log_answer} 查询成功", 0);
			// 		}
			// 	} else {
			// 		// 记录搜题历史（未找到答案）
			// 		$DB->query("INSERT INTO search_history (uid, query, matched_id, matched_question, answer, accuracy, search_time) 
			// 				   VALUES ('{$uid}', '{$question}', '0', '未找到匹配题目', '未找到答案', '0', '{$time}')");
					
			// 		// 未找到答案
			// 		$result = array(
			// 			"code" => 0,
			// 			"data" => array(
			// 				"question" => "未找到答案！",
			// 				"answer" => "很抱歉, 题目搜索不到。",
			// 				"times" => 999
			// 			),
			// 			"message" => "验证成功，但是没有找到答案"
			// 		);
			// 		// 记录未找到答案的日志
			// 		wlog($uid, "蜜雪程序查询", "查询题目：{$question} - 未找到答案", 0);
			// 	}
				
			// 	exit(json_encode($result));
			// 	break;

	}
}


?>