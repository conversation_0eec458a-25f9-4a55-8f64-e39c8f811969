#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试答案匹配逻辑
"""

import difflib

def debug_matching():
    """调试答案匹配过程"""
    print("=== 调试答案匹配逻辑 ===")
    
    # 模拟选项
    options = [
        ("A", "PGC"),
        ("B", "UGC"),
        ("C", "生产商"),
        ("D", "代理商")
    ]
    
    # 模拟AI答案
    ai_answer = "PGC###UGC###生产商###代理商"
    ai_answers = ai_answer.split("###")
    
    print(f"AI答案: {ai_answer}")
    print(f"AI答案分割: {ai_answers}")
    print(f"可用选项: {options}")
    print()
    
    Xlist = []
    Alist = []
    
    for i, ans in enumerate(ai_answers):
        ans = ans.strip()
        print(f"处理第{i+1}个答案: '{ans}'")
        
        for x, a in options:
            similarity = difflib.SequenceMatcher(None, a, ans).ratio()
            print(f"  与选项{x}('{a}')的相似度: {similarity:.3f}")
            
            if similarity > 0.5:
                if x not in Xlist:
                    Xlist.append(x)
                    Alist.append(a)
                    print(f"  ✅ 匹配成功: '{ans}' -> 选项{x}('{a}')")
                else:
                    print(f"  ⚠️ 选项{x}已存在，跳过")
                break
            else:
                print(f"  ❌ 相似度不足，跳过")
        print()
    
    print(f"最终匹配结果:")
    print(f"  选中选项: {Xlist}")
    print(f"  选项文本: {Alist}")
    print(f"  匹配数量: {len(Xlist)}/4")
    
    # 检查为什么UGC没有匹配
    print("\n=== 特别检查UGC匹配 ===")
    ugs_ans = "UGC"
    for x, a in options:
        similarity = difflib.SequenceMatcher(None, a, ugs_ans).ratio()
        print(f"'{ugs_ans}' vs 选项{x}('{a}'): 相似度 = {similarity:.3f}")
        if similarity > 0.5:
            print(f"  应该匹配选项{x}")

def test_exact_matching():
    """测试精确匹配"""
    print("\n=== 测试精确匹配 ===")
    
    test_cases = [
        ("PGC", "PGC"),
        ("UGC", "UGC"),
        ("生产商", "生产商"),
        ("代理商", "代理商"),
        ("PGC", "UGC"),  # 不同的情况
    ]
    
    for ans, option in test_cases:
        similarity = difflib.SequenceMatcher(None, option, ans).ratio()
        print(f"'{ans}' vs '{option}': 相似度 = {similarity:.3f}")

def check_code_changes():
    """检查代码修改"""
    print("\n=== 检查代码修改 ===")
    
    try:
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否有未注释的###移除代码
        lines = content.split('\n')
        problematic_lines = []
        
        for i, line in enumerate(lines, 1):
            # 检查是否有未注释的replace("###", "")
            if 'replace("###", "")' in line and not line.strip().startswith('#'):
                problematic_lines.append((i, line.strip()))
        
        if problematic_lines:
            print("❌ 发现未注释的###移除代码:")
            for line_num, line_content in problematic_lines:
                print(f"  第{line_num}行: {line_content}")
        else:
            print("✅ 没有发现未注释的###移除代码")
        
        # 检查关键方法是否存在
        if '_process_ai_answer_directly' in content:
            print("✅ _process_ai_answer_directly方法存在")
        else:
            print("❌ _process_ai_answer_directly方法不存在")
        
        # 检查调用是否正确
        if 'return self._process_ai_answer_directly(html, ai_answer)' in content:
            print("✅ AI答题调用逻辑已修改")
        else:
            print("❌ AI答题调用逻辑未修改")
            
    except Exception as e:
        print(f"❌ 检查代码时出错: {e}")

if __name__ == "__main__":
    debug_matching()
    test_exact_matching()
    check_code_changes()
