#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import difflib

def test_matching():
    print("=== 快速验证匹配逻辑 ===")
    
    options = [
        ("A", "PGC"),
        ("B", "UGC"),
        ("C", "生产商"),
        ("D", "代理商")
    ]
    
    ai_answer = "PGC###UGC###生产商###代理商"
    ai_answers = ai_answer.split("###")
    
    print(f"AI答案: {ai_answer}")
    print(f"AI答案分割: {ai_answers}")
    print(f"选项: {options}")
    print()
    
    Xlist = []
    Alist = []
    
    for ans in ai_answers:
        ans = ans.strip()
        print(f"处理答案: '{ans}'")
        
        for x, a in options:
            similarity = difflib.SequenceMatcher(None, a, ans).ratio()
            print(f"  vs 选项{x}('{a}'): 相似度 = {similarity:.3f}")
            
            if similarity > 0.5:
                if x not in Xlist:
                    Xlist.append(x)
                    Alist.append(a)
                    print(f"  ✅ 匹配成功: '{ans}' -> 选项{x}('{a}')")
                    break
                else:
                    print(f"  ⚠️ 选项{x}已存在，跳过")
                    break
        print()
    
    print(f"最终结果: 选项{Xlist}, 文本{Alist}")
    print(f"匹配数量: {len(Xlist)}/4")
    
    if len(Xlist) == 4:
        print("✅ 所有选项都匹配成功")
        return True
    else:
        print("❌ 匹配不完整")
        return False

def test_code_check():
    print("\n=== 检查代码修改 ===")
    
    try:
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查未注释的###移除代码
        lines = content.split('\n')
        has_uncommented_replace = False
        for line in lines:
            if 'ai_answer.replace("###", "")' in line and not line.strip().startswith('#'):
                has_uncommented_replace = True
                break
        
        if not has_uncommented_replace:
            print("✅ 错误的###移除代码已删除")
        else:
            print("❌ 错误的###移除代码仍然存在")
            return False
        
        # 检查关键方法
        if '_process_ai_answer_directly' in content:
            print("✅ 新的直接处理方法已添加")
        else:
            print("❌ 新的直接处理方法未找到")
            return False
        
        # 检查调用逻辑
        if 'return self._process_ai_answer_directly(html, ai_answer)' in content:
            print("✅ AI答题调用逻辑已修改")
        else:
            print("❌ AI答题调用逻辑未修改")
            return False
        
        print("✅ 所有代码修改都正确")
        return True
        
    except Exception as e:
        print(f"❌ 检查代码时出错: {e}")
        return False

if __name__ == "__main__":
    print("平台ID9006修复验证")
    print("=" * 30)
    
    result1 = test_matching()
    result2 = test_code_check()
    
    print("\n" + "=" * 30)
    print("验证结果:")
    print(f"1. 答案匹配逻辑: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"2. 代码修改检查: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print("🎉 所有验证通过，修复成功！")
    else:
        print("⚠️ 部分验证失败")
