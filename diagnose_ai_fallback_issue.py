# -*- coding: utf-8 -*-
"""
诊断AI答题fallback问题
分析为什么平台ID9006的AI答题没有触发
"""

import sys
import traceback
from loguru import logger

def test_homework_ai_availability():
    """测试HomeworkAI可用性"""
    try:
        logger.info("=== 测试HomeworkAI可用性 ===")
        
        # 检查HomeworkAI导入
        try:
            from API.HomeworkAI import HomeworkAI
            HOMEWORK_AI_AVAILABLE = True
            logger.info("✓ HomeworkAI导入成功")
            logger.info(f"HomeworkAI类: {HomeworkAI}")
        except ImportError as e:
            HOMEWORK_AI_AVAILABLE = False
            logger.error(f"✗ HomeworkAI导入失败: {str(e)}")
            return False
        
        # 检查data/Exam.py中的导入
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "HOMEWORK_AI_AVAILABLE = True" in content:
            logger.info("✓ data/Exam.py中HOMEWORK_AI_AVAILABLE设置正确")
        else:
            logger.error("✗ data/Exam.py中HOMEWORK_AI_AVAILABLE设置错误")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"测试HomeworkAI可用性失败: {str(e)}")
        return False

def test_ai_answer_conditions():
    """测试AI答题触发条件"""
    try:
        logger.info("=== 测试AI答题触发条件 ===")
        
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查AI答题条件
        conditions = [
            {"check": "elif self.qnum == 3:", "description": "qnum=3触发条件"},
            {"check": "hasattr(self.session, 'cid') and self.session.cid == 9006", "description": "平台ID检查"},
            {"check": "HOMEWORK_AI_AVAILABLE", "description": "AI可用性检查"},
            {"check": "题库未找到答案，尝试使用AI答题", "description": "AI答题日志"},
            {"check": "_get_ai_answer_for_exam(self.question, self.qtp)", "description": "AI答题方法调用"}
        ]
        
        all_passed = True
        for condition in conditions:
            if condition["check"] in content:
                logger.debug(f"✓ {condition['description']}")
            else:
                logger.error(f"✗ 缺少{condition['description']}")
                all_passed = False
        
        if all_passed:
            logger.info("✓ AI答题触发条件完整")
        else:
            logger.error("✗ AI答题触发条件不完整")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"测试AI答题触发条件失败: {str(e)}")
        return False

def test_qnum_flow():
    """测试qnum状态流转"""
    try:
        logger.info("=== 测试qnum状态流转 ===")
        
        # 检查questionbank函数返回值
        with open("API/Questionbank.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        if "return None, 1  # 返回qnum=1以触发备用题库查询" in content:
            logger.info("✓ questionbank失败时返回qnum=1")
        else:
            logger.error("✗ questionbank返回值错误")
            return False
        
        if "return None, 2" in content:
            logger.info("✓ questionbank2失败时返回qnum=2")
        else:
            logger.error("✗ questionbank2返回值错误")
            return False
        
        # 检查Answers方法中的qnum处理
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        flow_checks = [
            {"check": "if self.qnum == 1:", "description": "qnum=1: 备用题库查询"},
            {"check": "elif self.qnum == 2:", "description": "qnum=2: 跳转到AI答题"},
            {"check": "self.qnum = 3", "description": "设置qnum=3"},
            {"check": "elif self.qnum == 3:", "description": "qnum=3: AI答题"}
        ]
        
        all_passed = True
        for check in flow_checks:
            if check["check"] in content:
                logger.debug(f"✓ {check['description']}")
            else:
                logger.error(f"✗ 缺少{check['description']}")
                all_passed = False
        
        if all_passed:
            logger.info("✓ qnum状态流转正确")
        else:
            logger.error("✗ qnum状态流转错误")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"测试qnum状态流转失败: {str(e)}")
        return False

def analyze_user_log():
    """分析用户日志"""
    try:
        logger.info("=== 分析用户日志 ===")
        
        # 用户日志分析
        user_log_analysis = [
            "1. 多选题处理: qtp=1，应该调用Answers方法",
            "2. questionbank查询失败: 返回None, qnum应该=1",
            "3. Answers方法应该检查qnum=1，调用备用题库查询",
            "4. 备用题库查询失败: 返回None, qnum应该=2",
            "5. Answers方法应该检查qnum=2，跳转到AI答题(qnum=3)",
            "6. Answers方法应该检查qnum=3，执行AI答题",
            "7. 但是用户日志没有显示AI答题的日志"
        ]
        
        logger.info("用户日志分析:")
        for analysis in user_log_analysis:
            logger.info(f"  {analysis}")
        
        # 可能的问题
        possible_issues = [
            "1. session.cid没有正确设置为9006",
            "2. HOMEWORK_AI_AVAILABLE为False",
            "3. qnum状态流转有问题",
            "4. AI答题方法有异常",
            "5. 用户的代码版本不是最新的"
        ]
        
        logger.info("\n可能的问题:")
        for issue in possible_issues:
            logger.info(f"  {issue}")
        
        return True
        
    except Exception as e:
        logger.error(f"分析用户日志失败: {str(e)}")
        return False

def test_session_cid_setting():
    """测试session.cid设置"""
    try:
        logger.info("=== 测试session.cid设置 ===")
        
        with open("学习通跑单启动文件.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查session.cid设置
        cid_settings = [
            "self.session.cid = self.cid",
            "session.cid = cid"
        ]
        
        found_settings = 0
        for setting in cid_settings:
            count = content.count(setting)
            if count > 0:
                logger.info(f"✓ 找到{count}处{setting}")
                found_settings += count
        
        if found_settings > 0:
            logger.info(f"✓ 总共找到{found_settings}处session.cid设置")
        else:
            logger.error("✗ 没有找到session.cid设置")
            return False
        
        # 检查平台ID9006的支持
        if "9006" in content:
            logger.info("✓ 代码中包含平台ID9006")
        else:
            logger.error("✗ 代码中不包含平台ID9006")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"测试session.cid设置失败: {str(e)}")
        return False

def test_debug_logging():
    """测试调试日志"""
    try:
        logger.info("=== 测试调试日志 ===")
        
        with open("data/Exam.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键日志
        log_checks = [
            {"check": "题库未找到答案，尝试使用AI答题", "description": "AI答题尝试日志"},
            {"check": "AI生成答案成功", "description": "AI答题成功日志"},
            {"check": "AI答题失败", "description": "AI答题失败日志"}
        ]
        
        all_passed = True
        for check in log_checks:
            if check["check"] in content:
                logger.debug(f"✓ {check['description']}")
            else:
                logger.error(f"✗ 缺少{check['description']}")
                all_passed = False
        
        if all_passed:
            logger.info("✓ 调试日志完整")
        else:
            logger.error("✗ 调试日志不完整")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"测试调试日志失败: {str(e)}")
        return False

def suggest_debugging_steps():
    """建议调试步骤"""
    try:
        logger.info("=== 建议调试步骤 ===")
        
        debugging_steps = [
            "1. 在Answers方法开始处添加日志，记录qnum值",
            "2. 在AI答题条件检查处添加日志，记录各个条件的值",
            "3. 检查session.cid的实际值",
            "4. 检查HOMEWORK_AI_AVAILABLE的实际值",
            "5. 添加更详细的异常处理和日志记录",
            "6. 创建一个测试脚本来模拟整个流程"
        ]
        
        logger.info("建议的调试步骤:")
        for step in debugging_steps:
            logger.info(f"  {step}")
        
        return True
        
    except Exception as e:
        logger.error(f"建议调试步骤失败: {str(e)}")
        return False

def main():
    """主诊断函数"""
    logger.info("=" * 60)
    logger.info("诊断AI答题fallback问题")
    logger.info("=" * 60)
    logger.info("目标: 分析为什么平台ID9006的AI答题没有触发")
    
    tests = [
        ("HomeworkAI可用性测试", test_homework_ai_availability),
        ("AI答题触发条件测试", test_ai_answer_conditions),
        ("qnum状态流转测试", test_qnum_flow),
        ("用户日志分析", analyze_user_log),
        ("session.cid设置测试", test_session_cid_setting),
        ("调试日志测试", test_debug_logging),
        ("调试步骤建议", suggest_debugging_steps)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                logger.warning(f"{test_name}: 检查失败")
        except Exception as e:
            logger.error(f"{test_name}: 异常 - {str(e)}")
            traceback.print_exc()
    
    logger.info("\n" + "=" * 60)
    logger.info(f"诊断结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.success("🎉 所有检查通过，代码逻辑正确")
        logger.info("问题可能在于运行时的条件不满足")
    else:
        logger.warning(f"⚠ 有{total-passed}个检查失败，需要修复")
    
    return passed == total

if __name__ == "__main__":
    main()
