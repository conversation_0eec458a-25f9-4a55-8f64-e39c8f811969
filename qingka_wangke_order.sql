/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : mixuelo

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 07/06/2025 02:40:04
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for qingka_wangke_order
-- ----------------------------
DROP TABLE IF EXISTS `qingka_wangke_order`;
CREATE TABLE `qingka_wangke_order`  (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL COMMENT '平台ID',
  `hid` int(11) NOT NULL COMMENT '接口ID',
  `yid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '对接站ID',
  `ptname` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '平台名字',
  `school` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '学校',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名',
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '账号',
  `pass` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '密码',
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '手机号',
  `kcid` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程名字',
  `courseStartTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程开始时间',
  `courseEndTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '课程结束时间',
  `examStartTime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '考试开始时间',
  `examEndTime` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '考试结束时间',
  `chapterCount` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '总章数',
  `unfinishedChapterCount` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '剩余章数',
  `cookie` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'cookie',
  `fees` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '扣费',
  `noun` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '对接标识',
  `miaoshua` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '0不秒 1秒',
  `addtime` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '添加时间',
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '下单ip',
  `dockstatus` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '对接状态 0待 1成  2失 3重复 4取消',
  `loginstatus` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '待处理',
  `process` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `bsnum` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '备注',
  `score` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '分数',
  `shichang` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '时长',
  `fenlei` int(11) NULL DEFAULT NULL,
  `shoujia` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商城售价',
  `uptime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `qg` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL,
  `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `paytime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `payUser` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '',
  `type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '',
  `updatetime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `autoTk` int(11) NOT NULL COMMENT '自动退款',
  `pushUid` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '微信推送用户ID',
  `pushStatus` tinyint(1) NULL DEFAULT 0 COMMENT '推送状态:0-未推送,1-已推送',
  `lirun` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `fenzhan` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `submitPeriod` int(11) NULL DEFAULT NULL COMMENT '国开单页 形考提交时间间隔，不传这个参数默认是1 即1分钟提交一次，支持1-10的整数',
  `learnTimes` int(11) NULL DEFAULT NULL COMMENT '国开单页 --学习次数',
  `learnDays` int(11) NULL DEFAULT NULL COMMENT '国开单页 --学习天数',
  `finishLives` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否观看直播回放,0不观看 1观看',
  `doForum` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成讨论 1完成 0不完成',
  `doExam` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成形考 1完成 0不完成',
  `doHomework` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成大作业 1完成 0不完成',
  `doActivities` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否完成任务点 1完成 0不完成',
  `doHomeworkMode` int(11) NULL DEFAULT NULL COMMENT '国开单页 --大作业提交模式 1直接提交 0保存草稿，学生可基于我们保存的草稿添加自己要的东西再自己点提交',
  `doNightLearn` int(11) NULL DEFAULT NULL COMMENT '国开单页 --夜间是否学习 1学习 0不学习',
  `tag` int(11) NULL DEFAULT NULL COMMENT '国开单页 --下单标签',
  `isNewGk` int(11) NULL DEFAULT NULL COMMENT '国开单页 --是否是单页下单',
  PRIMARY KEY (`oid`) USING BTREE,
  INDEX `hid`(`hid`, `user`, `kcname`, `addtime`) USING BTREE,
  INDEX `hid_2`(`hid`) USING BTREE,
  INDEX `user`(`user`, `addtime`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 230132 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
