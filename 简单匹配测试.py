#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import difflib

# 测试数据
options = [
    ("A", "PGC"),
    ("B", "UGC"),
    ("C", "生产商"),
    ("D", "代理商")
]

ai_answer = "PGC###UGC###生产商###代理商"
ai_answers = ai_answer.split("###")

print("=== 简单匹配测试 ===")
print(f"AI答案: {ai_answer}")
print(f"AI答案分割: {ai_answers}")
print(f"选项: {options}")
print()

# 逐个测试匹配
for i, ans in enumerate(ai_answers):
    ans = ans.strip()
    print(f"测试答案 {i+1}: '{ans}' (长度: {len(ans)})")
    
    for x, a in options:
        similarity = difflib.SequenceMatcher(None, a, ans).ratio()
        print(f"  vs 选项{x}('{a}', 长度: {len(a)}): 相似度 = {similarity:.3f}")
        
        # 检查字符编码
        print(f"    '{ans}' bytes: {ans.encode('utf-8')}")
        print(f"    '{a}' bytes: {a.encode('utf-8')}")
        
        if similarity > 0.5:
            print(f"    ✅ 匹配成功")
        else:
            print(f"    ❌ 匹配失败")
    print()

# 特别测试UGC
print("=== 特别测试UGC ===")
test_ugc = "UGC"
option_ugc = "UGC"
similarity = difflib.SequenceMatcher(None, option_ugc, test_ugc).ratio()
print(f"'{test_ugc}' vs '{option_ugc}': 相似度 = {similarity:.3f}")
print(f"是否相等: {test_ugc == option_ugc}")
print(f"test_ugc bytes: {test_ugc.encode('utf-8')}")
print(f"option_ugc bytes: {option_ugc.encode('utf-8')}")
